CS 285 Lecture 22: Transfer Learning & Meta-Learning

--- Part 1: 迁移学习简介与预训练 ---

今天我们将讨论迁移学习和元学习，这涉及到一个核心问题：如何利用从某些源强化学习（RL）问题中获得的经验，从而更高效地解决新的下游任务。

让我们从一些动机开始。回想一下我们在第一个探索讲座中讨论过的例子，我们处理的一些马尔可夫决策过程（MDP），例如一些简单的 Atari 游戏，即使使用相对简单的方法（比如您在家庭作业3中实现的Q学习）也相当容易解决。但其他一些游戏，可能看起来并没有太大不同，却似乎异常困难。例如，如果你尝试用家庭作业3的Q学习算法来学习《蒙特祖玛的复仇》（Montezuma's Revenge）中的策略，你会发现它甚至很难通过第一关。

为什么会这样呢？问题在于，这些游戏对我们人类来说看起来很简单，但对计算机来说却非常困难，因为计算机没有我们所拥有的先验知识。在这个游戏中，奖励结构并没有为如何解决任务提供很好的指导。比如，捡起钥匙会有一点奖励，打开门也会有一点奖励，被骷髅杀死是很糟糕的事情，但你实际上不会因此得到任何负面奖励。它之所以糟糕，唯一的原因是如果你被骷髅杀死的次数足够多，你就会输掉游戏，然后就不能继续捡钥匙和开门了。所以这种奖励结构并没有真正为你提供很多关于如何在游戏中取得进展的指导。此外，捡起钥匙和开门并不一定是赢得游戏的正确方法，因为在后面的关卡中，你可以去不同的地方，也许你不需要完成所有有奖励的步骤就能到达终点。

但对我们来说，所有这些事情都不会构成巨大的挑战。事实上，当我们玩游戏时，我们甚至可能不会关注分数，我们可能只是在运用我们先前的知识，这些知识告诉我们应该如何在这样的游戏中取得进展。这里有一个视觉上的比喻：一个探险家在探索一个古老的诅咒神庙。我们从电子游戏的知识和一般的物理常识中都有这种信念，你知道骷髅代表着坏东西，钥匙可以用来开门，梯子是可以攀爬的东西。我们也从电子游戏的基本知识中知道，通过不同的关卡、进入新的房间等等，是取得进步的好方法。

因此，我们对问题结构的先前理解可以帮助我们快速解决复杂的任务，当这些任务的某些方面符合我们的预期时，也就是说，它符合我们的先前知识。当我们学习玩《蒙特祖玛的复仇》时，我们实际上是在进行一种迁移学习，我们将我们对世界的了解转移到这个新的MDP中。所以，在我们之前讨论《蒙特祖玛的复仇》作为研究探索领域的例子时，我们现在要讨论的是迁移问题。在这种情况下，正确的做法可能不是设计一个好的“从头开始”的探索算法，而是一个可以迁移你从其他任务中获得的知识的算法，从而更有效地解决这个新任务。

那么，强化学习是否可以像我们一样利用这些先验知识呢？也许我们可以构建一个算法，可以观看大量的《印第安纳琼斯》电影，并用它来找出如何解决《蒙特祖玛的复仇》的问题。这也许是一个非常崇高的目标，我们实际上还没有完全实现，但我们可以开始思考迁移学习问题，并且实际上可以设计一些非常聪明的算法来解决这个问题。

我们的想法是，如果我们已经解决了先前的任务（即先前的MDPs），我们可能会获得解决新任务的有用知识。如何封装这些知识，如何更好地表示它们？实际上有很多选择。我们可以说Q函数很适合迁移，因为Q函数告诉我们哪些动作或状态是好的。所以，如果你以某种方式将《印第安纳琼斯》的经验编码为Q函数，你知道，比如偷宝藏是有价值的，也许这将是一个很好的初始化函数来解决《蒙特祖玛的复仇》。但Q函数可能不是最好的选择，因为在电子游戏中，你不是通过移动你的腿和手臂来移动，而是通过按按钮来移动，所以动作空间可能完全不同。

策略（Policy）告诉我们哪些行为可能有用，这可能可以很好地迁移。有些动作可能永远没有用，所以只要你能排除这些，那么也许你可以取得更大的进展。或者你可以迁移模型（Model），也许支配世界如何运作的物理定律在两个领域都是相同的，即使任务的其他一些因素不同，迁移模型也可以是一种非常有效的策略。

也可能是一个更抽象的设置，迁移某种特征或隐藏状态可能会为你提供良好的表示。所以也许你在玩《蒙特祖玛的复仇》时没有好的动作可以迁移，你甚至没有好的模型，因为事情不太一致，但视觉特征可能会有所帮助。也许通过观看一些视频，你会发现骷髏、梯子和钥匙是世界上重要的东西，只要你一开始就用一个好的骷髅、梯子或钥匙探测器，这可能已经为你带来了一些优势。对于最后这一点，不要低估它，实际上一点点视觉预训练就可以起到很大的作用。

但在我们深入讨论具体技术之前（我不会介绍所有这些技术，只会介绍这个领域中一些有影响力的想法），我们先来定义一些术语。

正式的迁移学习处理的是如何利用一组任务中的经验，以便在新任务上更快地学习和获得更好的表现。当我们在强化学习的背景下讨论迁移学习时，一个“任务”当然就是一个MDP。所以你也可以理解为，利用一个MDP或一组MDP的经验，以便在新的MDP上更快地学习和获得更好的表现。

你训练的MDP，也就是你获得初始经验的MDP，被称为“源域”（Source Domain），因为它是你知识的来源。然后，你想要解决的新任务的MDP是“目标域”（Target Domain），也就是你想要获得好结果的MDP。经典的迁移学习关注的是你在目标域中能做得多好，尽管它与“终身学习”或“持续学习”密切相关，后者的目标是在源域和目标域上都取得好成绩。

我们的目标是在目标域上取得好成绩，无论源域中发生什么。人们会用一些术语来指代你在目标域中的学习速度。有时我们会用“shot”（尝试次数）这个词来指代我们在目标域中尝试了多少次。所以你可能会听到“零次迁移”（zero-shot transfer）。零次迁移意味着在源域上训练之后，你甚至没有在目标域中进行任何尝试，就能立即在目标域中获得良好的性能。所以零次迁移是指，如果你与一些涉及印第安纳琼斯偷宝藏的MDP进行交互，你会得到一个策略，你可以简单地部署在《蒙特祖玛的复仇》游戏中，它就能立即运行良好。

“一次迁移”（one-shot transfer）意味着你尝试了一次任务，这在某种程度上是领域独立的。在《蒙特祖玛的复仇》游戏中，这意味着你基本上玩了一局，直到你的生命耗尽。如果它是一个与世界互动的机器人，也许机器人会尝试一次任务。“几次迁移”（few-shot transfer）意味着你尝试了几次任务，“多次迁移”（many-shot transfer）意味着你尝试了很多次任务。这些不一定是非常精确的术语，但它能让你在读论文时看到这些词汇时，能大致了解正在发生的事情。

那么我们如何构建这些迁移学习问题呢？首先要说的是，这里实际上没有任何一个单一的问题陈述。我们到目前为止在课程中讨论的很多内容都是相当基础的材料，有相当容易理解的理论和指导方针。但很多迁移学习都有点“临时抱佛脚”的性质，因为它非常依赖于迁移源域和目标域的性质。如果你想在《夺宝奇兵》视频上进行预训练来玩《蒙特祖玛的复仇》，你可能会使用一种非常不同的算法，这与例如训练一个机器人抓取大量物体，然后部署去抓取一个新物体的情况不同。

但仍然有一些人们常用的核心想法，这就是我在今天的讲座中试图涵盖的内容。请记住，我只会涵盖一小部分想法，而不是全部。我会尝试涵盖那些可以在各种环境中使用的主要中心思想。

我将讨论“前向迁移”（Forward Transfer），它处理的是学习可以有效迁移的策略。你可以在源任务上训练，然后直接在目标任务上运行，或者在目标任务上进行微调。这通常依赖于任务之间非常相似。大量的前向迁移工作都涉及寻找使源域看起来像目标域的方法，或者使从源域中恢复的策略更有可能迁移到目标域。

另一个重要领域是“多任务迁移”（Multi-task Transfer），即在许多不同的任务上进行训练，然后迁移到新任务。这种方法效果很好，因为它不再依赖于单个源域靠近目标域，而是可以获取多个源域，因此目标域直观地位于它们的“凸包”内。如果你想让机器人抓住一个新物体，而你之前只处理过一个其他物体，那么迁移可能会非常困难。但如果你处理过许多不同的物体，那么这个新物体可能看起来与你见过的各种物体很相似，因此多任务迁移往往更容易。

今天讲座的大部分时间，我们将讨论一种叫做“元学习”（Meta-Learning）的东西。元学习可以理解为迁移学习的逻辑延伸。我们不是简单地在某个源域上进行训练，然后在目标域中以零样本或简单的微调方式取得成功。我们实际上会在源域中以一种特殊的方式进行训练，这种方式会让我们意识到我们稍后需要适应新的目标域。因此，元学习通常被定义为“学习如何学习”（learning to learn）的问题。本质上，你会尝试解决那些源域，不一定非要以一种在所有源域上都能找到非常好的解决方案的方式，而是以一种让你准备好解决新域的方式。这明确地考虑了我们将在训练过程中适应新任务的事实。

在强化学习中进行预训练和微调是一种常见的方法。如果你想解决一些通用的迁移学习问题，比如在计算机视觉领域，一种非常流行的方法是在某个大型代表性数据集上进行训练，比如在ImageNet上训练一个卷积网络，或者在大型文本语料库上训练一个语言模型。然后用它来提取表征，这些表征可以是图像的表征，也可以是文本的表征。接着，你会在这些表征之上训练一些额外的层，或者对整个网络进行微调，以解决你想要的特定任务，而对于这些任务，你的数据量相对有限。这是监督学习领域中相当标准的工作流程。

你可以想象在强化学习中也使用类似的方法。在某些情况下，这种方法实际上是开箱即用的。你可以用强化学习来学习表征，然后利用这些表征，在这些表征之上微调几层来解决你的任务。你也可以用监督学习来学习表征，然后在这些表征之上用强化学习微调几层来解决你的强化学习任务。这些都是相当直接的方法，我不会深入讨论，因为它们基本上是从监督学习继承过来的标准技术。

但我想讨论一下在将这种预训练和微调模式应用于强化学习时可能遇到的问题。这些问题不一定是强化学习独有的，但它们往往会更频繁地出现。一个问题是“领域漂移”（Domain Shift），这是一个相当明显的问题，即在源领域学习到的表示在目标领域可能效果不佳。这种情况经常发生，想象一下，在某种模拟器中学习一项任务，该模拟器模拟视觉观察或其他高维观察，然后将得到的策略迁移到现实世界环境中，在这些环境中，观察结果在结构上相似，但不完全相同。

另一个问题是将微调概念应用于强化学习时遇到的挑战。例如，微调过程可能需要在新的目标域中进行探索。但请记住，在完全可观察的MDP中，最优策略可以是确定性的。所以在源域中运行策略梯度后，你可能会得到一个确定性的策略，当你部署到目标域中时，它不再进行探索，因为它已经收敛了。你可能需要处理一些这类低级技术问题。

让我们先来谈谈领域漂移问题。计算机视觉社区已经开发了许多工具来处理这类问题。我将讨论这些问题，因为它们与视觉感知有关，但这些问题并非视觉感知所独有，它们是高维观测的一般问题。让我们考虑一下在模拟中学习驾驶的例子。我们希望在模拟器的图像上进行训练，并希望当策略面对现实世界中的图像时表现良好。

我们可以利用一个叫做“不变性假设”（Invariance Hypothesis）的假设来帮助我们解决这个问题。这个假设认为，两个域之间的所有差异都是无关紧要的。这意味着什么呢？也许模拟器不会模拟下雨，但现实世界的图像中可能包含雨水。不变性假设意味着是否下雨与你的驾驶方式无关。另一方面，道路上汽车的位置在模拟中与现实世界中的汽车位置在统计上是一致的。如果你认同这个假设，你可以正式地写出来：输入（图像X）的分布 P(X) 在源域和目标域中是不同的，但存在一些特征化 Z = f(X)，使得 Z 的分布 P(Z) 在两个域中是相同的，并且给定 Z 预测输出 Y 的概率与给定 X 预测 Y 的概率相同。这意味着 Z 保留了预测所需的所有信息。如果你能找到这样的表示，并且不变性假设成立，那么你将能够完美地进行迁移。

获得这些不变表示的一种常用技术是领域混淆或领域对抗网络。基本思想是，我们在神经网络中采用一些中间层，并添加一个额外的损失函数来强制该层的激活（表示为Z）在不同领域间保持不变，即 P(Z) 在源域和目标域中是相同的。为了做到这一点，我们训练一个二元分类器，它会查看Z并预测它来自源域还是目标域。然后，我们反转分类器的梯度并将其反向传播回网络中，从而“教”网络生成让分类器无法区分来源的Z。

当然，有时不仅仅是图像不同，动态（dynamics）本身也可能是不同的。在这种情况下，仅仅强制表示不变性可能不是一个好主意。如果动态不匹配，那么不变性就不够好，因为你实际上不想忽略功能相关的差异。但你可以做的是，改变你的奖励函数，惩罚智能体在源域中执行那些在目标域中不可能完成的任务。

例如，假设在现实世界的目标域中，你想从起点到达目标，中间有一堵墙，所以你必须绕过这堵墙。但在你的模拟器（源域）中，这堵墙不存在。如果你在源域中训练，你会得到直接冲向目标的行为，这在目标域中是行不通的。所以我们可以做的是，如果在目标域中有一点经验，我们可以改变源域的奖励函数，对于执行在目标域中不可能完成的转换给予一个非常大的负奖励。这种技术直观地会惩罚智能体“打破幻觉”——即意识到它处于源域而不是目标域的行为。

除了领域漂移，微调本身在强化学习中也比在监督学习中更困难。首先，强化学习任务通常多样性较低。在计算机视觉或NLP中，预训练通常在拥有数百万图像或数十亿行文本的非常广泛的环境中进行，然后在更窄的域中进行微调。在强化学习中，预训练可能也需要在一个较窄的任务上进行。如果你在较窄的任务上预训练，特征通常不太通用，策略和价值函数可能会变得过于专业化。在完全可观察的MDP中，最优策略往往是确定性的。训练时间越长，策略的确定性就越强。结合第一个问题，这可能是一个大问题，因为随着策略的确定性越来越强，探索越来越少，因此会失去探索的能力。一个收敛的、低熵的策略会非常缓慢地适应新环境，因为它探索得不够。

因此，在强化学习中，简单地进行微调通常不是很有效。我们经常需要做各种各样的事情来确保我们的预训练过程能够产生比常规强化学习预训练得到的结果更加多样化的解决方案。我们在之前的讲座中讨论过的一些技术，如无监督技能发现和最大熵强化学习，都可以作为有效的预训练方法，因为它们能为你提供更多样化的解决方案。

另一个非常有用的工具是操纵源域。如果你对源域有一些控制权（比如它是一个你可以修改的模拟器），你可以做一些事情来最大限度地提高迁移成功的概率。基本直觉是，训练领域越多样化，我们就越有可能在零样本的情况下推广到一个稍微不同的领域。这在实践中经常通过“领域随机化”（Domain Randomization）来实现。人们会故意在源域中添加更多的随机性，可能比他们在现实世界中预期的要多得多，以增加策略对各种参数变化的鲁棒性。例如，随机化物理参数（如质量、摩擦力）或视觉外观（如光照、纹理）。

多任务迁移是另一个强大的工具。基本理念是，通过学习多个任务，你可以学得更快，迁移得更好。如果你有各种各样的任务，比如一个机器人需要做各种家务，这些任务很可能共享一些共同的结构。如果你不单独训练它们，而是一起训练它们，共享表征的事实将使所有任务学习得更快。此外，如果你学习了多个不同的任务，然后给你一个新任务，似乎更有可能有一些东西可以迁移到目标任务。

为了实现多任务学习，一个简单而重要的想法是，多任务问题实际上对应于一个联合MDP中的单任务问题。你可以通过改变初始状态分布来将多任务问题嵌入到标准RL设置中。在第一步，你对任务进行采样，然后你玩那个任务。这仍然是单个MDP，所以原则上算法根本不需要改变。在某些情况下，你需要向智能体明确指示它应该执行哪个任务。我们通过为每个任务分配某种“上下文”（context）来实现这一点。上下文策略的形式是 π(a|s, ω)，其中ω是上下文。这可以通过扩充状态空间来实现，将上下文添加到状态中。训练这个多任务策略的问题，基本上与之前的多任务MDP相同，其中ω在初始时间步随机选择，然后在剩余的情节中保持不变。

--- Part 2: 什么是元学习？ ---

在今天讲座的下一节中，我们将讨论元学习算法。元学习是多任务学习的一种逻辑扩展，它不是简单地学习如何解决各种任务，而是使用许多不同的任务来学习如何更快地学习新任务。因此，我将首先在一种更传统的监督学习环境中对元学习进行概述，然后讨论如何在强化学习中实例化这些想法。

什么是元学习？如果你已经学习了100个任务，你能弄清楚如何更有效地学习新任务吗？在这种情况下，拥有多个任务是一个巨大的优势，因为如果你能从多个任务中概括学习过程本身，那么你就可以大大加快新任务的习得速度。因此，元学习本质上相当于“学习如何学习”。

在实践中，它与多任务学习密切相关，并且有许多不同的表述，尽管这些表述可以概括为同一个概念。这些表述可能涉及学习优化器、学习一个可以读取大量经验来解决新任务的循环神经网络（RNN），或者只是学习一个好的表征以便可以更快地针对新任务进行微调。即使这些方法看起来非常不同，它们实际上可以在同一个框架中实例化，并且许多用于解决元学习问题的不同技术，一旦你深入到细节，实际上看起来很像同一个基本算法框架的不同架构选择。

为什么元学习是一个好主意？深度强化学习，特别是无模型学习，需要大量样本。所以如果你可以元学习一个更快的强化学习器，那么你就可以有效地学习新任务。那么，元学习的强化学习方法可能会做什么不同的事情呢？它可能会更智能地探索，因为解决那些先前任务的经验告诉它如何构建探索策略以快速解决新任务。它可能会避免尝试它知道无用的操作，所以也许它不知道如何精确地解决新任务，但它知道某些类型的行为永远不适合做。它也可能会更快地获得正确的特征，也许它经过了这样一种训练，使得网络可以快速变化，从而修改其特征表示以适应新任务。

让我描述一个非常基本的方法来设置为监督学习问题的元学习，我认为这个方法可以揭开元学习周围的许多神秘面纱。在常规的监督学习中，你有一个训练集和一个测试集。在元学习中，我们实际上将拥有一组训练集和一组测试集。我们用于元学习过程的数据集集合称为“元训练集”（Meta-Training Set），而当我们接到新任务时将会看到的内容称为“元测试集”（Meta-Testing Set）。元训练是源域，元测试是目标域。

在元训练期间，每个“任务”都由一个训练集和一个测试集组成。例如，一个任务可能是识别五种不同的物体。训练集包含这五类物体的带标签的样本，测试集包含这些类别的未见过的测试图像。另一个任务可能是识别另外五种完全不同的物体。我们的想法是，通过查看许多不同的训练集及其对应的测试集，来构建某种模型，该模型能够接收一个全新的训练集（其中包含你从未见过的新类别），并在其对应的测试集上表现出色。

我们可以这样看待这个过程：常规的监督学习是接收一些输入X，然后生成预测y。输入X可能是图像，输出y可能是标签。而元学习可以被认为是一个函数 F，它接受整个训练集 D_train 以及一个测试样本 X_test，并生成该测试样本的标签 y_test。所以它实际上并没有太大的不同，只是一个函数会读取训练集和测试样本，并对测试样本进行预测。

当然，如果你想实例化它，你必须解决几个问题，例如，如何“读取”训练集。有很多方法可以实现这一点，例如，循环神经网络（RNN）或Transformers可以很好地完成这项工作。你可以想象一个RNN，它按顺序读取训练集中的样本（(x1, y1), (x2, y2), ...）。在读取完整个训练集后，它会生成一个代表该任务的隐藏状态。然后，这个隐藏状态会和测试样本 X_test 一起被送入一个最终的分类器，来预测 y_test。

让我们用更正式的框架来理解。在通用学习中，你有一个参数 θ，你需要找到一个 θ，使得在训练集上的某个损失函数最小化。我们把这个寻找最优参数的过程称为学习过程 F_learn。F_learn 接受训练集，输出模型参数 θ。而通用元学习可以被认为是寻找一个学习函数 F_θ，这个函数接受一个训练集 D_train_i 并产生一些参数 φ_i。我们的目标是训练 F_θ 的参数 θ，使得由它为元训练任务生成的参数 φ_i 在各自的测试集上的损失最小化。这有点像一个二阶优化过程：你训练一个学习过程 F_θ，使得它的输出（模型参数）在面对新数据时表现良好。

对于前面提到的RNN例子，F_θ 是什么呢？F_θ 就是那个读取训练集的RNN本身，它的参数就是我们需要元学习的参数 θ。当它读取一个训练集 D_train_i 时，它会产生一个隐藏状态 h_i。这个 h_i 就构成了任务特定的参数 φ_i 的一部分。然后，这个 h_i 会被提供给一个小型的最终分类器，该分类器接收 h_i 和测试样本 X_test 并生成预测。这个最终分类器也有自己的参数。因此，学习一个新任务的过程，基本上就是运行这个RNN来处理新任务的训练集，并获取其最终的隐藏状态。

总结一下这个过程：我们有一个RNN编码器，它读取一系列图像及其标签，并生成一个隐藏状态。这个隐藏状态进入一个小型神经网络，该网络接收测试图像并生成其标签。元训练过程会训练所有这些网络的参数，包括RNN编码器和最终的分类器。当你进入目标任务（元测试时），你会得到一个目标任务的训练集。然后，你使用训练好的RNN编码器对该训练集进行编码，生成一个新的隐藏状态 h_i。这个新的 h_i 结合最终分类器的参数，就构成了新任务的模型，然后用它来进行预测。实际上，这是一种解释非常简单的事情的很长的方式，实际上你只需运行这个RNN就可以得到答案，但这解释了它与元学习的关系。

--- Part 3: 元强化学习 ---

好的，现在我们来谈谈如何将这些元学习的思想应用到我们的领域——强化学习中。我们之前提到的通用学习图景是：常规学习是指在训练集上最小化某个损失函数来找到最优参数。这对应于某个固定的学习函数 F_learn，比如梯度下降。通用的元学习可以被视为最小化在测试集上的损失函数，其中进入损失函数的参数是由一个学习到的学习函数 F_θ 应用于训练集 D_train 后给出的。

我们可以通过类比将相同的想法应用于强化学习。我们可以说，常规强化学习是指在某个策略 π_θ 下最大化预期奖励。这可以被视为一个学习函数 F_rl，但 F_rl 不再应用于训练集，而是应用于一个MDP。因此，F_rl 是MDP的函数。

元强化学习则可以被视为最大化一个具有某些参数 φ_i 的策略的预期奖励，其中参数 φ_i 是通过将一个学习到的函数 F_θ 应用于MDP M_i 给出的。好的，我们只是将相同的定义转移到了RL设置中。这意味着元强化学习将会是一个强化学习过程，但它要训练的是 F_θ，它本身会“读取”一个MDP并输出某种策略或策略的表征。

让我们尝试实例化这个想法。我们有一组MDP，它们被称为“元训练MDPs”。为了使其工作，我们需要假设这些元训练MDP是从某个分布 P(M) 中抽取的。然后在测试时，我们将有一个新的测试MDP M_test，它也来自同一个分布。我们将通过将这个学习到的函数 F_θ 应用于 M_test 来获取我们策略的参数。假设它们来自同一个分布是非常重要的，因为就像在监督学习中一样，只有当训练和测试来自同一个分布时，学习才有效。

例如，不同的MDP可能对应于执行不同任务的机器人，然后 M_test 将是机器人学习一个新任务。或者它可能是更简单的事情，比如你的作业中的半猎豹机器人以不同的速度向前和向后奔跑，然后 M_test 将是一种新的速度。

看待元学习的一种方式是将其视为训练一个策略 π_θ 的问题，该策略的决策取决于你在当前MDP中的所有经验。因此，F_θ 本质上所做的就是利用当前MDP中的经验，并将其总结成一些汇总统计数据，然后用于确定你的策略将做什么。这基本上等同于拥有一个以你在这个MDP中经历的所有历史为条件的策略。

这与我们之前讨论的“上下文策略”有什么关系呢？这基本上就是一个上下文策略，只不过现在“上下文”是当前MDP M_i 中的所有经验。在多任务学习中，上下文是提供给我们的，比如有人会告诉我们“你的工作是洗衣服”。而在元学习中，上下文是从在 M_i 中的经验中推断出来的。

那么 F_θ(M_i) 应该怎么做呢？它需要根据从 M_i 中获得的经验来改进策略，它需要读取这些经验来帮助策略做得更好。同时，它还需要选择如何与环境交互，这与监督学习不同。在元强化学习中，我们还必须明智地选择如何在一个新的MDP M_i 中进行探索。

我们可以直接应用监督元学习的类比，简单地建立某种模型来读取我们在 M_i 中所有的经验。最简单的版本就是一个循环神经网络（RNN）。这个RNN会读取一系列的转换 (s1, a1, r1, s2), (s2, a2, r2, s3), ...，这些转换都是我们在当前MDP中经历过的，可能跨越多个回合（episodes）。然后，RNN会用某种隐藏向量来表示它，这个隐藏向量会被输入到一个策略头中，策略头会接受当前状态和这个隐藏向量，并产生一个动作。

这是一种非常直接的表示 F_θ 的方法，它可以读取所有经验，并使用它来指导策略应该做什么。当然，参数 θ 是这个RNN编码器和策略头的参数。这可能看起来有点过于简单，我们为元学习引入了很多形式主义，但最终似乎只需要训练一个RNN策略。答案基本上是肯定的。

为了说服自己这是真的，让我们来看一个使用这种基于历史的策略的元学习小例子。假设我们有一只老鼠，它的目标是得到奶酪。在不同的任务（MDPs）中，奶酪会在不同的位置，或者墙的位置可能会不同，所以它必须适应不同的MDP。在第一个回合的第一个时间步，老鼠向右移动，我们给RNN的第一个转换是（状态，向右移动，奖励0，新状态）。然后老鼠向左移动，回合结束。现在开始一个新的回合，但我们不会重置RNN的隐藏状态，RNN仍然在读取所有这些经验。在新的回合中，老鼠向上移动，然后向右移动，得到了奶酪（奖励+1）。从内部来看，RNN应该能够通过编码这些经验来找出奶酪在右上角。所以当下一个新回合开始时，因为RNN的隐藏状态没有被重置，它仍然携带着“向上走、向右走能获得奖励”的上下文信息，从而可以直接走向奶酪。

为什么这种方法可以有效地学习探索？因为RNN在第一回合向右走时没有看到奖励，如果它经过了充分的元训练，它应该知道在第二回合不应该再向右走，而应该去别的地方探索。如果它在各种各样的MDP上进行元训练，这些探索模式就会变得明显。常规的强化学习算法能够恢复这些探索策略。技巧在于，一旦你给策略提供了整个“元回合”（由多个回合连接而成）的经验，探索问题就变成了解决一个更高层次的MDP的问题。因此，使用RNN策略来优化整个元回合的总奖励，实际上会自动地学习到探索策略。这是元强化学习中一个非常重要的思想。

--- Part 4: 基于梯度的元强化学习 ---

好的，接下来我要讲讲基于梯度的元强化学习。让我们稍微回顾一下我们之前讨论过的预训练和微调。在常规监督学习中使用预训练的一个非常标准的方法是简单地学习一些表示，然后根据这些表示针对新任务进行微调。因此，我们可以问的一个具体问题是，预训练和微调实际上只是某种元强化学习的一种吗？如果是这样，我们能否以某种方式进行元训练，使预训练和微调效果良好？这基本上就是基于梯度的元强化学习背后的理念。

本质上，如果我们有更好的特征，那么我们就可以更快地学习新任务，我们实际上可以优化我们的特征，以便更快地学习新任务。我们可以将其融入到我们迄今为止开发的元强化学习框架中。

之前对元强化学习的看法是，我们元训练一个函数 F_θ，以便 F_θ 为任务 M_i 产生一个好的策略参数 φ_i，从而在元训练任务中获得高回报。为了使其发挥作用，F_θ 需要能够利用迄今为止在 MDP M_i 中看到的经验来生成这个 φ_i。

现在，如果 F_θ 本身是一种强化学习算法呢？例如，一种策略梯度算法。F_θ 的参数化实际上只是输入其中的策略的初始参数 θ。标准RL会采用目标函数 J(θ)，计算 J 相对于参数的梯度，然后更新参数。假设 F_θ 执行相同的操作，它采用参数 θ 并将当前任务 M_i 的目标 J_i 的梯度添加到它们中。这就是 F_θ 的定义：它通过一个梯度步骤来更新参数 θ。那么问题就变成了：我们能否找到一个初始参数 θ，使得在所有元训练任务上，仅仅通过一个梯度步骤的更新，就能获得高回报？

这种方法被称为“模型无关元学习”（Model-Agnostic Meta-Learning, MAML）。MAML 基本上只是一种元学习方法，其中学习函数 F_θ 具有与梯度下降更新相匹配的特殊结构。

让我们以图片的形式直观地思考一下。假设你有一个神经网络策略，可以读取状态并输出动作。我们不是在单个任务上训练并使用策略梯度进行更新，而是会有各种不同的任务。对于每个任务（比如让一只蚂蚁朝不同方向奔跑），我们将使用该任务的梯度来更新策略参数 θ。我们本质上是在尝试优化一个初始的 θ，使得在这个 θ 的基础上应用一个梯度步骤后，能够尽可能地增加该任务的奖励。所以这有点像一个二阶优化问题：找到一个 θ，使得应用梯度步骤后能最大化奖励的增益。

如果你只执行一个梯度步骤，你可以形象地想象它：在参数空间中，你试图找到一个点 θ，这个点离每个任务的最优解 θ_1*, θ_2*, θ_3* 等都只有一个梯度步骤的距离。当然，你不必只执行一个梯度步骤，你可以执行多个梯度步骤，只是代数公式会更麻烦一些，但这完全是可行的。计算它需要二阶导数，这对于策略梯度的实现来说有点棘手，但完全是可能的。

让我们稍微解释一下这种方法的作用。MAML 至少在监督学习设置中，可以被看作是训练集 D_train 和测试点 X 的函数，只不过这个函数具有特殊的结构：f_MAML(D_train, X) = f_θ'(X)，其中 θ' 是通过在 D_train 上采取梯度步骤从 θ 获得的。这清楚地表明，这实际上只是另一个计算图，只是这个函数 f 的另一种架构。即使它内部有梯度下降，你也可以将梯度下降视为神经网络的一部分，并使用自动微分包来实现它。对于策略梯度来说要复杂一些，因为计算策略梯度的二阶导数需要一些谨慎，常规的自动微分（例如 TensorFlow 和 PyTorch 中的）无法直接做到这一点，但对于监督学习来说，这很简单。

你可能会问，如果这只是另一种架构，我们为什么要这样做？原因在于它具有良好的归纳偏见。只要基于梯度的学习方法（如策略梯度）是好的学习算法，你就会期望它能够产生良好的自适应过程。实际上，在实践中，人们在模型无关元学习中发现的一个问题是，你可以在元测试时采取比实际训练时更多的梯度步骤。网络倾向于泛化，并允许你采取更多的梯度步骤。而基于RNN的元学习则无法做到这一点，因为使用RNN或Transformer，它只是读取训练集并产生一些答案，仅此而已，没有在测试任务上进行更长时间训练的概念，因为“学习”过程只是对网络进行一次前向传递。

那么模型无关元学习在实践中起什么作用呢？假设我们有一项任务，让蚂蚁向前、向后、向左或向右奔跑。如果我们将元训练得到的初始参数（适应前的参数）下的策略可视化，我们会看到蚂蚁在原地打转。但如果我们给它一个梯度步骤，并在前进时给予奖励，它就会前进；如果我们给它一个梯度步骤，并在后退时给予奖励，它就会愉快地后退。它学会了一个非常容易适应的初始行为。

--- Part 5: 作为部分可观察MDP的元强化学习 ---

好的，关于元强化学习的最后一个讨论是关于如何将元强化学习构建为一种部分可观察马尔可夫决策过程（Partially Observable MDP, POMDP）问题。

POMDP 指的是除了状态和动作之外，还包含观察值和观察概率的MDP。在POMDP中，策略必须根据观察历史来采取行动，这通常需要显式的状态估计或具有记忆的策略。

假设我们有一个策略 π_θ(a|s, z)，其中 z 是一些变量，它封装了策略解决当前任务所需的信息。学习当前任务就相当于推断出 z 是什么。在元学习中，你必须从上下文中推断出这一点，而上下文就是你在新MDP M_i 中积累的经验（一系列转换）。这实际上就是一个POMDP问题。你有一个潜在的任务变量 z，你不知道它是什么，你需要从一系列观察中弄清楚它是什么，一旦你弄清楚了，你就可以完成任务了。

所以，关键思想是，解决这个特殊的POMDP相当于进行元学习。因为如果你能在这个POMDP（其中真实状态s是可观察的，但任务z不是）中获得非常高的奖励，那么你将能够仅从状态和奖励的观察中解决一个新任务。

通常，解决POMDP需要像我之前说的那样，要么是明确的状态估计，要么是具有记忆的策略。具有记忆的策略本质上是我们之前讨论过的基于RNN的元学习器。它们也可以被视为解决POMDP的方法，其中任务是未知的，应该从记忆中推断出来。

但让我们来谈谈另一类POMDP求解器——那些执行显式状态估计的算法。事实证明，这也将导致一类具有一些有趣特性的元学习算法。这些算法旨在根据状态历史、动作历史和奖励历史，直接估计 z 的后验概率 P(z | history)。

实际上，我们不知道 z 是什么，z 是某种潜在变量。所以我们将以类似于训练潜在变量模型的方式进行训练，我们将使用变分推断来获取任务的学习表征。然后一旦我们获得了它，我们就可以通过后验采样来探索这个潜在上下文。如果你还记得探索课程中关于后验采样的讨论，它相当于从我们的后验信念中采样一个模型（在这里是任务z），然后在该信念下采取最佳行动。

这个过程会是这样：根据你的历史记录，从你对 z 的信念中抽取一个样本 z，然后假设这个 z 是正确的，并据此采取行动以收集更多数据。然后重复这个过程。元训练将包括训练策略 π_θ 和训练你的变分近似后验（状态估计器）。

已经提出了各种基于这个想法的技术，我今天要讨论的技术叫做PEARL。它训练一个以状态s和潜在变量z为条件的策略 π(a|s, z)，同时训练一个推理网络（编码器），该网络根据状态、动作和奖励的历史来预测z的后验分布。整个过程都用变分推断进行训练。

这个过程在概念上与基于RNN的元强化学习非常相似，因为你都读取历史记录，预测某种统计数据，然后将该统计数据提供给你的策略，并最大化给定该统计数据的策略的奖励。不同之处在于，你的编码器现在是随机的，它推断出一个潜在分布，你可以通过从这个编码器中采样来探索，即通过后验采样进行探索。

这里有一个例子，一个小的二维点状智能体，目标总是在一个半圆上。智能体一开始并不知道目标在哪里。你可以看到，智能体探索的方式是去半圆上的随机位置。一旦它到达有高奖励的区域，它就会不断地重复去那里。这种方法效果很好，但也解释了为什么它有点次优，因为在现实中，对智能体来说，更理想的做法可能是在一次回合中沿着那个圆圈扫描以找到奖励的位置，然后一次又一次地重新访问它。

现在让我们把我们讨论过的元RL的三个视角结合在一起。我们讨论的所有元RL方法都可以表示为某种函数 F_θ，它接受一个MDP M_i，积累经验，从中改进，并选择有效探索的行动。

我们讨论的第一个视角是一个黑盒模型，就像一个RNN。它在概念上非常简单，相对容易应用，但它很容易受到“元过拟合”的影响，这意味着如果测试时的任务稍微超出训练任务的分布范围，RNN可能无法产生很好的适应性。而且没有其他补救措施，你无法在测试任务上进一步改进它。

基于梯度的方法（如MAML）具有很好的外推性，因为你可以在测试时继续运行更多的梯度步骤，最终你会变得更好。它在概念上很优雅，但实现起来可能很复杂，并且可能需要许多元训练样本。将这些方法从策略梯度扩展到Actor-Critic等方法也非常困难。

基于推理的方法（如PEARL）很简单，并且可以通过后验采样进行有效的探索，可以优雅地简化为解决一种特殊的POMDP问题。但与RNN方法一样，它可能容易受到元过拟合的影响，在实践中优化起来也有点困难。

不过，它们之间实际上非常相似。基于推理的过程实际上就像带有随机变量的RNN过程。而基于梯度的方法可以通过选择特定的架构来实例化为另外两种方法之一。所以它们实际上非常相似。

最后我想说的是，关于元强化学习，人们研究过的另一件事是它如何导致有趣的涌现现象，这通常发生在强化学习和认知科学的交叉领域。观察结果大致如下：人类和动物似乎以各种方式学习行为，包括高效但显然是无模型的强化学习、情景回忆（回忆以前有效的事情并立即执行）以及基于模型的强化学习。有一种假设认为，也许这些不同的学习方式都是某种可以“学习如何学习”的总体方法的涌现属性。

人们已经研究过元学习如何导致涌现的学习过程，这些过程与最初用于元学习的过程不同。有些论文分析了元强化学习如何产生情景学习、如何从无模型的元强化学习中产生基于模型的适应，甚至如何产生看起来像因果推理的东西。我不会详细讨论这些内容，但如果你想了解更多，我鼓励你查看相关的论文。