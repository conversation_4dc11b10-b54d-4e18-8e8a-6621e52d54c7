#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini AI测试功能一键设置脚本
自动配置Gemini测试功能的所有必要设置
"""

import os
import sys
import json

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = ['pygame', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} 未安装")
    
    if missing_packages:
        print(f"\n需要安装以下包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for package in missing_packages:
            print(f"  pip install {package}")
        return False
    
    return True

def setup_api_key():
    """设置API密钥"""
    print("\n设置Gemini API密钥...")
    
    print("请访问 https://makersuite.google.com/app/apikey 获取您的Gemini API密钥")
    api_key = input("请输入您的Gemini API密钥: ").strip()
    
    if not api_key:
        print("❌ API密钥不能为空")
        return False
    
    # 更新配置文件
    config_file = os.path.join(os.path.dirname(__file__), 'continuous_reading_config.py')
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换API密钥
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "'api_key':" in line and "GEMINI_CONFIG" in content[:content.find(line)]:
                lines[i] = f"    'api_key': '{api_key}',  # Gemini API密钥"
                break
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ API密钥设置成功")
        return True
        
    except Exception as e:
        print(f"❌ 设置API密钥失败: {e}")
        return False

def configure_gemini_settings():
    """配置Gemini测试设置"""
    print("\n配置Gemini测试设置...")
    
    print("请设置以下参数（直接按回车使用默认值）:")
    
    # 测试间隔
    test_interval = input("测试间隔（每隔多少段进行测试，默认10）: ").strip()
    if not test_interval:
        test_interval = 10
    else:
        try:
            test_interval = int(test_interval)
        except ValueError:
            print("无效输入，使用默认值10")
            test_interval = 10
    
    # 每次测试题目数量
    max_questions = input("每次测试最大题目数量（默认8）: ").strip()
    if not max_questions:
        max_questions = 8
    else:
        try:
            max_questions = int(max_questions)
        except ValueError:
            print("无效输入，使用默认值8")
            max_questions = 8
    
    # 答题超时时间
    timeout = input("每题答题超时时间（秒，默认30）: ").strip()
    if not timeout:
        timeout = 30
    else:
        try:
            timeout = int(timeout)
        except ValueError:
            print("无效输入，使用默认值30")
            timeout = 30
    
    # 是否显示正确答案
    show_answer = input("是否显示正确答案（y/N，默认是）: ").strip().lower()
    show_answer = show_answer in ['y', 'yes', '是', '']
    
    # 是否显示解释
    show_explanation = input("是否显示题目解释（y/N，默认是）: ").strip().lower()
    show_explanation = show_explanation in ['y', 'yes', '是', '']
    
    # 自定义提示词
    print("\n是否设置自定义提示词？")
    print("1. 不设置（使用默认）")
    print("2. 重点关注概念定义")
    print("3. 重点关注事实数据")
    print("4. 重点关注逻辑关系")
    print("5. 自定义输入")
    
    prompt_choice = input("请选择（1-5，默认1）: ").strip()
    
    custom_prompt = ""
    if prompt_choice == "2":
        custom_prompt = """请根据以下文本内容生成测试题，重点关注：
1. 关键概念的定义和含义
2. 重要术语的解释
3. 基本原理和理论

文本内容：
{text_content}

请为每个重要概念生成一道4选项单选题。"""
    elif prompt_choice == "3":
        custom_prompt = """请根据以下文本内容生成测试题，重点关注：
1. 具体的事实和数据
2. 时间、地点、人物等信息
3. 数字、比例、统计数据

文本内容：
{text_content}

请为每个重要事实生成一道4选项单选题。"""
    elif prompt_choice == "4":
        custom_prompt = """请根据以下文本内容生成测试题，重点关注：
1. 因果关系
2. 逻辑推理
3. 前后关联
4. 比较对比

文本内容：
{text_content}

请为每个重要逻辑关系生成一道4选项单选题。"""
    elif prompt_choice == "5":
        print("请输入自定义提示词（必须包含 {text_content} 占位符）:")
        custom_prompt = input().strip()
        if "{text_content}" not in custom_prompt:
            print("警告：提示词中没有 {text_content} 占位符，将使用默认提示词")
            custom_prompt = ""
    
    # 更新配置文件
    config_file = os.path.join(os.path.dirname(__file__), 'continuous_reading_config.py')
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新配置
        lines = content.split('\n')
        in_gemini_config = False
        
        for i, line in enumerate(lines):
            if "GEMINI_CONFIG = {" in line:
                in_gemini_config = True
            elif in_gemini_config and line.strip() == "}":
                in_gemini_config = False
            elif in_gemini_config:
                if "'test_interval':" in line:
                    lines[i] = f"    'test_interval': {test_interval},"
                elif "'max_questions_per_test':" in line:
                    lines[i] = f"    'max_questions_per_test': {max_questions},"
                elif "'question_timeout':" in line:
                    lines[i] = f"    'question_timeout': {timeout},"
                elif "'show_correct_answer':" in line:
                    lines[i] = f"    'show_correct_answer': {show_answer},"
                elif "'show_explanation':" in line:
                    lines[i] = f"    'show_explanation': {show_explanation},"
                elif "'custom_prompt':" in line:
                    lines[i] = f"    'custom_prompt': '''{custom_prompt}''',"
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ Gemini测试设置配置成功")
        print(f"  测试间隔: {test_interval}段")
        print(f"  最大题目数: {max_questions}题")
        print(f"  答题超时: {timeout}秒")
        print(f"  显示答案: {'是' if show_answer else '否'}")
        print(f"  显示解释: {'是' if show_explanation else '否'}")
        if custom_prompt:
            print(f"  自定义提示词: 已设置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置设置失败: {e}")
        return False

def test_configuration():
    """测试配置"""
    print("\n测试Gemini配置...")
    
    try:
        # 测试导入
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'gemini'))
        from gemini_client import GeminiClient
        
        # 测试连接
        client = GeminiClient()
        if client.test_connection():
            print("✅ Gemini API连接测试成功")
            return True
        else:
            print("❌ Gemini API连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def main():
    """主函数"""
    print("Gemini AI测试功能一键设置")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装必要的依赖包，然后重新运行此脚本")
        return
    
    # 设置API密钥
    if not setup_api_key():
        print("\n设置失败，请检查API密钥")
        return
    
    # 配置设置
    if not configure_gemini_settings():
        print("\n配置失败，请检查设置")
        return
    
    # 测试配置
    if test_configuration():
        print("\n🎉 Gemini AI测试功能设置完成！")
        print("\n现在可以运行连续阅读实验，系统会自动在指定间隔进行测试")
        print("运行命令: python run_experiment.py")
    else:
        print("\n⚠️ 设置完成但测试失败，请检查API密钥和网络连接")

if __name__ == "__main__":
    main()
