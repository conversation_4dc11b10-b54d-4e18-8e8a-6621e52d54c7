#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
删除指定根目录下所有名为"video"的子文件夹内容的工具

功能：
- 递归搜索指定根目录下所有名为"video"的文件夹
- 删除这些文件夹中的所有内容（文件和子文件夹）
- 保留video文件夹本身，只清空内容
- 提供详细的操作日志和确认提示

使用方法：
python delete_video_folders.py --root_dir <根目录路径> [--dry_run] [--force]
"""

import os
import shutil
import argparse
import logging
from pathlib import Path
from typing import List, Tuple


def setup_logging(log_file: str = None) -> logging.Logger:
    """设置日志记录"""
    logger = logging.getLogger('delete_video_folders')
    logger.setLevel(logging.INFO)
    
    # 清除已有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（如果指定了日志文件）
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger


def find_video_folders(root_dir: str) -> List[str]:
    """
    递归查找所有名为"video"的文件夹
    
    Args:
        root_dir: 根目录路径
        
    Returns:
        包含所有video文件夹路径的列表
    """
    video_folders = []
    root_path = Path(root_dir)
    
    if not root_path.exists():
        raise FileNotFoundError(f"根目录不存在: {root_dir}")
    
    if not root_path.is_dir():
        raise NotADirectoryError(f"指定路径不是目录: {root_dir}")
    
    # 递归搜索所有名为"video"的文件夹
    for item in root_path.rglob("*"):
        if item.is_dir() and item.name == "video":
            video_folders.append(str(item))
    
    return video_folders


def get_folder_info(folder_path: str) -> Tuple[int, int, int]:
    """
    获取文件夹信息：文件数量、子文件夹数量、总大小
    
    Args:
        folder_path: 文件夹路径
        
    Returns:
        (文件数量, 子文件夹数量, 总大小(字节))
    """
    folder_path = Path(folder_path)
    file_count = 0
    dir_count = 0
    total_size = 0
    
    try:
        for item in folder_path.rglob("*"):
            if item.is_file():
                file_count += 1
                try:
                    total_size += item.stat().st_size
                except (OSError, PermissionError):
                    pass  # 忽略无法访问的文件
            elif item.is_dir() and item != folder_path:
                dir_count += 1
    except (OSError, PermissionError):
        pass  # 忽略无法访问的目录
    
    return file_count, dir_count, total_size


def format_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    
    return f"{size_bytes:.2f} {size_names[i]}"


def delete_folder_contents(folder_path: str, logger: logging.Logger) -> bool:
    """
    删除文件夹中的所有内容，保留文件夹本身
    
    Args:
        folder_path: 文件夹路径
        logger: 日志记录器
        
    Returns:
        删除是否成功
    """
    folder_path = Path(folder_path)
    success = True
    
    try:
        for item in folder_path.iterdir():
            try:
                if item.is_file():
                    item.unlink()
                    logger.info(f"删除文件: {item}")
                elif item.is_dir():
                    shutil.rmtree(item)
                    logger.info(f"删除目录: {item}")
            except (OSError, PermissionError) as e:
                logger.error(f"删除失败 {item}: {e}")
                success = False
    except (OSError, PermissionError) as e:
        logger.error(f"无法访问文件夹 {folder_path}: {e}")
        success = False
    
    return success


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="删除指定根目录下所有名为'video'的子文件夹内容",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python delete_video_folders.py --root_dir C:/data
  python delete_video_folders.py --root_dir C:/data --dry_run
  python delete_video_folders.py --root_dir C:/data --force --log_file deletion.log
        """
    )
    
    parser.add_argument('--root_dir', required=False,default="E:/curiosity_pupil",
                       help='要搜索的根目录路径')
    parser.add_argument('--dry_run', action='store_true',
                       help='仅显示将要删除的内容，不实际执行删除操作')
    parser.add_argument('--force', action='store_true',
                       help='强制删除，不显示确认提示')
    parser.add_argument('--log_file', 
                       help='日志文件路径（可选）')
    
    args = parser.parse_args()
    # args.root_dir = "data"
    
    # 设置日志
    logger = setup_logging(args.log_file)
    
    try:
        # 查找所有video文件夹
        logger.info(f"开始搜索根目录: {args.root_dir}")
        video_folders = find_video_folders(args.root_dir)
        
        if not video_folders:
            logger.info("未找到任何名为'video'的文件夹")
            return
        
        logger.info(f"找到 {len(video_folders)} 个video文件夹:")
        
        # 显示找到的文件夹信息
        total_files = 0
        total_dirs = 0
        total_size = 0
        
        for folder in video_folders:
            file_count, dir_count, size = get_folder_info(folder)
            total_files += file_count
            total_dirs += dir_count
            total_size += size
            
            logger.info(f"  {folder}")
            logger.info(f"    文件: {file_count}, 子文件夹: {dir_count}, 大小: {format_size(size)}")
        
        logger.info(f"\n总计: {total_files} 个文件, {total_dirs} 个子文件夹, {format_size(total_size)}")
        
        if args.dry_run:
            logger.info("\n[DRY RUN] 以上内容将被删除（当前为预览模式）")
            return
        
        # 确认删除
        if not args.force:
            print(f"\n确认要删除以上 {len(video_folders)} 个video文件夹中的所有内容吗？")
            print("注意：此操作不可恢复！")
            response = input("请输入 'yes' 确认删除: ").strip().lower()
            
            if response != 'yes':
                logger.info("操作已取消")
                return
        
        # 执行删除操作
        logger.info("\n开始删除操作...")
        success_count = 0
        
        for folder in video_folders:
            logger.info(f"\n正在处理: {folder}")
            if delete_folder_contents(folder, logger):
                success_count += 1
                logger.info(f"成功清空文件夹: {folder}")
            else:
                logger.error(f"清空文件夹失败: {folder}")
        
        logger.info(f"\n删除操作完成！成功处理 {success_count}/{len(video_folders)} 个文件夹")
        
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
