#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini API密钥设置工具
"""

import os
import sys

def setup_api_key():
    """设置Gemini API密钥"""
    print("=== Gemini API密钥设置 ===")
    print()
    print("请访问 https://makersuite.google.com/app/apikey 获取您的Gemini API密钥")
    print()
    
    api_key = input("请输入您的Gemini API密钥: ").strip()
    
    if not api_key:
        print("❌ API密钥不能为空")
        return False
    
    # 更新配置文件
    config_file = os.path.join(os.path.dirname(__file__), 'gemini_config.py')
    
    try:
        # 读取配置文件
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换API密钥
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if "'api_key':" in line and "GEMINI_CONFIG" in content[:content.find(line)]:
                lines[i] = f"    'api_key': '{api_key}',  # Gemini API密钥"
                break
        
        # 写回文件
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ API密钥设置成功！")
        
        # 测试API密钥
        print("\n正在测试API密钥...")
        try:
            from gemini_client import GeminiClient
            client = GeminiClient(api_key)
            if client.test_connection():
                print("✅ API密钥测试成功！")
                return True
            else:
                print("❌ API密钥测试失败，请检查密钥是否正确")
                return False
        except Exception as e:
            print(f"❌ API密钥测试出错: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 设置API密钥失败: {e}")
        return False

def check_api_key():
    """检查API密钥是否已设置"""
    try:
        from gemini_config import GEMINI_CONFIG
        return bool(GEMINI_CONFIG['api_key'])
    except:
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        if check_api_key():
            print("✅ API密钥已设置")
        else:
            print("❌ API密钥未设置")
    else:
        setup_api_key()
