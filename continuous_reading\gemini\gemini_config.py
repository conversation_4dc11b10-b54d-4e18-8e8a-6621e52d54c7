#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini AI 测试题生成配置文件
"""

# ==================== Gemini API 配置 ====================
GEMINI_CONFIG = {
    'api_key': 'AIzaSyDgHKaJRSKK-ywZTBqv6Eu-C_uTxD3Hh5c',  # 需要用户设置自己的API密钥
    'model': 'gemini-2.5-pro',  # 使用的模型
    'temperature': 1,  # 生成温度
    'max_tokens': 20000,  # 最大token数
    'timeout': 30,  # 请求超时时间（秒）
}

# ==================== 测试题生成配置 ====================
QUIZ_CONFIG = {
    'test_interval': 10,  # 每隔多少段进行一次测试（可配置）
    'questions_per_test': 5,  # 每次测试的题目数量（默认值，实际由文本知识点决定）
    'max_questions': 10,  # 单次测试最大题目数量
    'min_questions': 1,   # 单次测试最小题目数量
    'question_timeout': 30,  # 每题答题超时时间（秒）
    'show_correct_answer': True,  # 是否显示正确答案
    'show_explanation': True,     # 是否显示解释
    'auto_advance_after_answer': True,  # 答题后是否自动进入下一题
}

# ==================== 提示词配置 ====================
PROMPT_CONFIG = {
    'system_prompt': """你是一个专业的教育测试专家。请先阅读全部文本，根据提供的文本内容，为每个重要知识点生成一道4选项单选题。

要求：
1. 题目数量应该与文本中的知识点数量相同
2. 每道题都应该测试文本中的一个具体知识点
3. 选项要有一定的迷惑性和难度，但正确答案要明确
4. 提供详细的解释说明
5. 题目难度适中，既不过于简单也不过于困难

请严格按照以下JSON格式返回：
{
    "questions": [
        {
            "question": "题目内容",
            "options": {
                "A": "选项A",
                "B": "选项B", 
                "C": "选项C",
                "D": "选项D"
            },
            "correct_answer": "A",
            "explanation": "详细解释为什么这个答案是正确的"
        }
    ]
}""",
    
    'user_prompt_template': """请根据以下文本内容生成测试题：

文本内容：
{text_content}

请为文本中的每个重要知识点生成一道4选项单选题。题目应该测试读者对文本内容的理解和记忆。""",
    
    'custom_prompt': '',  # 用户自定义提示词（可在配置中设置）
}

# ==================== 显示配置 ====================
QUIZ_DISPLAY_CONFIG = {
    'background_color': 'black',
    'text_color': 'white',
    'font_size': 24,
    'font_name': 'SimHei',
    'question_font_size': 28,
    'option_font_size': 24,
    'explanation_font_size': 20,
    'title_font_size': 32,
    'line_spacing': 1.5,
    'margin': 50,
    'option_spacing': 40,
    'answer_highlight_color': 'green',
    'wrong_answer_color': 'red',
    'explanation_color': 'yellow',
}

# ==================== 按键配置 ====================
QUIZ_KEY_CONFIG = {
    'option_keys': ['a', 'b', 'c', 'd'],  # 选项按键
    'continue_key': 'space',              # 继续按键
    'quit_key': 'escape',                 # 退出按键
    'confirm_key': 'return',              # 确认按键
}

# ==================== 数据保存配置 ====================
QUIZ_DATA_CONFIG = {
    'save_quiz_results': True,            # 是否保存测试结果
    'save_detailed_log': True,            # 是否保存详细日志
    'include_text_content': False,        # 是否在结果中包含原文本内容
    'save_response_time': True,           # 是否保存答题时间
}

def get_custom_prompt():
    """获取自定义提示词"""
    if PROMPT_CONFIG['custom_prompt']:
        return PROMPT_CONFIG['custom_prompt']
    return PROMPT_CONFIG['user_prompt_template']

def validate_gemini_config():
    """验证Gemini配置"""
    errors = []
    warnings = []
    
    if not GEMINI_CONFIG['api_key']:
        errors.append("Gemini API密钥未设置")
    
    if QUIZ_CONFIG['test_interval'] <= 0:
        errors.append("测试间隔必须大于0")
    
    if QUIZ_CONFIG['max_questions'] < QUIZ_CONFIG['min_questions']:
        errors.append("最大题目数量不能小于最小题目数量")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

if __name__ == "__main__":
    # 验证配置
    result = validate_gemini_config()
    if result['valid']:
        print("✓ Gemini配置验证通过")
        if result['warnings']:
            print("警告:")
            for warning in result['warnings']:
                print(f"  ⚠ {warning}")
    else:
        print("✗ Gemini配置验证失败")
        for error in result['errors']:
            print(f"  ✗ {error}")
