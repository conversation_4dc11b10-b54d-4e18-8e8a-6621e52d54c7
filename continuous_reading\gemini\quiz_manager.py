#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试题管理器
整合Gemini AI生成、题目显示和数据保存功能
"""

import os
import json
import time
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .gemini_client import GeminiClient
    from .quiz_display import QuizDisplay
    from .gemini_config import QUIZ_CONFIG, QUIZ_DATA_CONFIG, PROMPT_CONFIG
except ImportError:
    from gemini_client import GeminiClient
    from quiz_display import QuizDisplay
    from gemini_config import QUIZ_CONFIG, QUIZ_DATA_CONFIG, PROMPT_CONFIG

class QuizManager:
    """测试题管理器"""
    
    def __init__(self, screen, font_manager=None, data_dir: str = "data", api_key: Optional[str] = None):
        """
        初始化测试题管理器
        
        Args:
            screen: pygame屏幕对象
            font_manager: 字体管理器
            data_dir: 数据保存目录
            api_key: Gemini API密钥
        """
        self.screen = screen
        self.font_manager = font_manager
        self.data_dir = data_dir
        
        # 初始化组件
        try:
            self.gemini_client = GeminiClient(api_key)
            self.quiz_display = QuizDisplay(screen, font_manager)
            self.client_available = True
        except Exception as e:
            print(f"Gemini客户端初始化失败: {e}")
            self.client_available = False
            self.gemini_client = None
            self.quiz_display = QuizDisplay(screen, font_manager)
        
        # 测试数据
        self.quiz_results = []
        self.current_quiz_number = 0
        self.total_text_segments = 0
        
        # 确保数据目录存在
        os.makedirs(data_dir, exist_ok=True)
    
    def should_run_quiz(self, segment_number: int) -> bool:
        """
        判断是否应该运行测试
        
        Args:
            segment_number: 当前段落编号
            
        Returns:
            是否应该运行测试
        """
        test_interval = QUIZ_CONFIG['test_interval']
        return test_interval > 0 and segment_number > 0 and segment_number % test_interval == 0
    
    def run_quiz(self, text_content: str, segment_number: int, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        运行完整的测试流程
        
        Args:
            text_content: 要测试的文本内容
            segment_number: 段落编号
            custom_prompt: 自定义提示词
            
        Returns:
            测试结果数据
        """
        if not self.client_available:
            print("Gemini客户端不可用，跳过测试")
            return {'success': False, 'error': 'Gemini客户端不可用'}
        
        self.current_quiz_number += 1
        quiz_start_time = time.time()
        
        print(f"\n开始第 {self.current_quiz_number} 次测试...")
        print(f"文本长度: {len(text_content)} 字符")
        
        try:
            # 1. 生成测试题
            print("正在生成测试题...")
            generation_result = self.gemini_client.generate_questions(text_content, custom_prompt)
            
            if not generation_result['success']:
                return {
                    'success': False,
                    'error': f"生成测试题失败: {generation_result['error']}",
                    'quiz_number': self.current_quiz_number,
                    'segment_number': segment_number
                }
            
            questions = generation_result['questions']
            if not questions:
                return {
                    'success': False,
                    'error': "没有生成任何测试题",
                    'quiz_number': self.current_quiz_number,
                    'segment_number': segment_number
                }
            
            # 限制题目数量
            max_questions = QUIZ_CONFIG['max_questions']
            min_questions = QUIZ_CONFIG['min_questions']
            
            if len(questions) > max_questions:
                questions = questions[:max_questions]
                print(f"题目数量超过限制，只保留前 {max_questions} 道题")
            elif len(questions) < min_questions:
                print(f"题目数量不足 {min_questions} 道，但继续进行测试")
            
            print(f"生成了 {len(questions)} 道测试题")
            
            # 2. 显示测试介绍
            if not self.quiz_display.display_quiz_intro(self.current_quiz_number, len(questions)):
                return {
                    'success': False,
                    'error': "用户取消测试",
                    'quiz_number': self.current_quiz_number,
                    'segment_number': segment_number
                }
            
            # 3. 逐题进行测试
            quiz_results = []
            for i, question in enumerate(questions, 1):
                print(f"显示第 {i}/{len(questions)} 题...")
                
                # 显示题目并获取答案
                user_answer, response_time = self.quiz_display.display_question(question, i, len(questions))
                
                if user_answer is None:  # 用户退出
                    return {
                        'success': False,
                        'error': "用户退出测试",
                        'quiz_number': self.current_quiz_number,
                        'segment_number': segment_number,
                        'partial_results': quiz_results
                    }
                
                # 记录答题结果
                is_correct = user_answer == question['correct_answer'].upper()
                question_result = {
                    'question_number': i,
                    'question': question['question'],
                    'options': question['options'],
                    'correct_answer': question['correct_answer'],
                    'user_answer': user_answer,
                    'is_correct': is_correct,
                    'response_time': response_time,
                    'explanation': question.get('explanation', ''),
                    'timestamp': time.time()
                }
                quiz_results.append(question_result)
                
                print(f"用户答案: {user_answer}, 正确答案: {question['correct_answer']}, {'正确' if is_correct else '错误'}")
                
                # 显示答案反馈
                if not self.quiz_display.display_answer_feedback(question, user_answer, response_time):
                    # 用户选择退出，但保存已完成的题目结果
                    break
            
            # 4. 计算测试统计
            total_questions = len(quiz_results)
            correct_answers = sum(1 for result in quiz_results if result['is_correct'])
            accuracy = correct_answers / total_questions if total_questions > 0 else 0
            avg_response_time = sum(result['response_time'] for result in quiz_results) / total_questions if total_questions > 0 else 0
            
            quiz_end_time = time.time()
            total_time = quiz_end_time - quiz_start_time
            
            # 5. 保存测试结果
            quiz_summary = {
                'success': True,
                'quiz_number': self.current_quiz_number,
                'segment_number': segment_number,
                'start_time': quiz_start_time,
                'end_time': quiz_end_time,
                'total_time': total_time,
                'text_length': len(text_content),
                'total_questions': total_questions,
                'correct_answers': correct_answers,
                'accuracy': accuracy,
                'avg_response_time': avg_response_time,
                'questions_results': quiz_results,
                'generation_info': {
                    'generation_time': generation_result.get('generation_time', 0),
                    'raw_response': generation_result.get('raw_response', '') if QUIZ_DATA_CONFIG['save_detailed_log'] else ''
                }
            }
            
            # 添加文本内容（如果配置允许）
            if QUIZ_DATA_CONFIG['include_text_content']:
                quiz_summary['text_content'] = text_content
            
            self.quiz_results.append(quiz_summary)
            
            # 保存到文件
            if QUIZ_DATA_CONFIG['save_quiz_results']:
                self._save_quiz_results(quiz_summary)
            
            print(f"测试完成！正确率: {accuracy:.1%} ({correct_answers}/{total_questions})")
            print(f"平均答题时间: {avg_response_time:.1f}秒")
            
            return quiz_summary
            
        except Exception as e:
            error_msg = f"测试过程中出错: {str(e)}"
            print(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'quiz_number': self.current_quiz_number,
                'segment_number': segment_number
            }
    
    def _save_quiz_results(self, quiz_summary: Dict[str, Any]):
        """
        保存测试结果到文件
        
        Args:
            quiz_summary: 测试摘要数据
        """
        try:
            # 生成文件名
            timestamp = datetime.fromtimestamp(quiz_summary['start_time']).strftime("%Y%m%d_%H%M%S")
            filename = f"quiz_results_{timestamp}_quiz{quiz_summary['quiz_number']}.json"
            filepath = os.path.join(self.data_dir, filename)
            
            # 保存JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(quiz_summary, f, ensure_ascii=False, indent=2)
            
            print(f"测试结果已保存到: {filepath}")
            
        except Exception as e:
            print(f"保存测试结果失败: {e}")
    
    def get_quiz_statistics(self) -> Dict[str, Any]:
        """
        获取所有测试的统计信息
        
        Returns:
            统计信息字典
        """
        if not self.quiz_results:
            return {'total_quizzes': 0}
        
        total_quizzes = len(self.quiz_results)
        total_questions = sum(result['total_questions'] for result in self.quiz_results)
        total_correct = sum(result['correct_answers'] for result in self.quiz_results)
        overall_accuracy = total_correct / total_questions if total_questions > 0 else 0
        
        avg_quiz_time = sum(result['total_time'] for result in self.quiz_results) / total_quizzes
        avg_response_time = sum(result['avg_response_time'] for result in self.quiz_results) / total_quizzes
        
        return {
            'total_quizzes': total_quizzes,
            'total_questions': total_questions,
            'total_correct': total_correct,
            'overall_accuracy': overall_accuracy,
            'avg_quiz_time': avg_quiz_time,
            'avg_response_time': avg_response_time,
            'quiz_results': self.quiz_results
        }
    
    def set_custom_prompt(self, prompt: str):
        """
        设置自定义提示词
        
        Args:
            prompt: 自定义提示词
        """
        PROMPT_CONFIG['custom_prompt'] = prompt
        print(f"已设置自定义提示词: {prompt[:100]}...")

def test_quiz_manager():
    """测试测试题管理器功能"""
    import pygame
    
    # 初始化pygame（用于测试）
    pygame.init()
    screen = pygame.display.set_mode((1200, 800))
    pygame.display.set_caption("测试题管理器测试")
    
    try:
        # 创建管理器
        manager = QuizManager(screen, data_dir="test_data")
        
        # 测试文本
        test_text = """
        人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。
        深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。
        自然语言处理是人工智能的另一个重要应用领域，它使计算机能够理解和生成人类语言。
        """
        
        # 运行测试
        result = manager.run_quiz(test_text, segment_number=10)
        
        if result['success']:
            print("✅ 测试成功完成")
            stats = manager.get_quiz_statistics()
            print(f"总体正确率: {stats['overall_accuracy']:.1%}")
        else:
            print(f"❌ 测试失败: {result['error']}")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
    finally:
        pygame.quit()

if __name__ == "__main__":
    test_quiz_manager()
