#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试题显示和交互模块
负责在屏幕上显示测试题并处理用户交互
"""

import time
import pygame
from typing import Dict, List, Optional, Any, Tuple

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .gemini_config import QUIZ_DISPLAY_CONFIG, QUIZ_KEY_CONFIG, QUIZ_CONFIG
except ImportError:
    from gemini_config import QUIZ_DISPLAY_CONFIG, QUIZ_KEY_CONFIG, QUIZ_CONFIG

class QuizDisplay:
    """测试题显示类"""
    
    def __init__(self, screen, font_manager=None):
        """
        初始化测试题显示
        
        Args:
            screen: pygame屏幕对象
            font_manager: 字体管理器（可选）
        """
        self.screen = screen
        self.screen_width, self.screen_height = screen.get_size()
        self.font_manager = font_manager
        
        # 显示配置
        self.bg_color = pygame.Color(QUIZ_DISPLAY_CONFIG['background_color'])
        self.text_color = pygame.Color(QUIZ_DISPLAY_CONFIG['text_color'])
        self.answer_highlight_color = pygame.Color(QUIZ_DISPLAY_CONFIG['answer_highlight_color'])
        self.wrong_answer_color = pygame.Color(QUIZ_DISPLAY_CONFIG['wrong_answer_color'])
        self.explanation_color = pygame.Color(QUIZ_DISPLAY_CONFIG['explanation_color'])
        
        # 字体设置
        self._setup_fonts()
        
        # 按键配置
        self.option_keys = QUIZ_KEY_CONFIG['option_keys']
        self.continue_key = QUIZ_KEY_CONFIG['continue_key']
        self.quit_key = QUIZ_KEY_CONFIG['quit_key']
        
        # 布局配置
        self.margin = QUIZ_DISPLAY_CONFIG['margin']
        self.option_spacing = QUIZ_DISPLAY_CONFIG['option_spacing']
        self.line_spacing = QUIZ_DISPLAY_CONFIG['line_spacing']
    
    def _setup_fonts(self):
        """设置字体"""
        try:
            if self.font_manager:
                # 使用字体管理器
                self.title_font = self.font_manager.get_font(QUIZ_DISPLAY_CONFIG['title_font_size'])
                self.question_font = self.font_manager.get_font(QUIZ_DISPLAY_CONFIG['question_font_size'])
                self.option_font = self.font_manager.get_font(QUIZ_DISPLAY_CONFIG['option_font_size'])
                self.explanation_font = self.font_manager.get_font(QUIZ_DISPLAY_CONFIG['explanation_font_size'])
            else:
                # 使用pygame默认字体
                font_name = QUIZ_DISPLAY_CONFIG['font_name']
                self.title_font = pygame.font.Font(None, QUIZ_DISPLAY_CONFIG['title_font_size'])
                self.question_font = pygame.font.Font(None, QUIZ_DISPLAY_CONFIG['question_font_size'])
                self.option_font = pygame.font.Font(None, QUIZ_DISPLAY_CONFIG['option_font_size'])
                self.explanation_font = pygame.font.Font(None, QUIZ_DISPLAY_CONFIG['explanation_font_size'])
        except Exception as e:
            print(f"字体设置失败，使用默认字体: {e}")
            # 使用pygame默认字体作为备选
            self.title_font = pygame.font.Font(None, 32)
            self.question_font = pygame.font.Font(None, 28)
            self.option_font = pygame.font.Font(None, 24)
            self.explanation_font = pygame.font.Font(None, 20)
    
    def display_quiz_intro(self, quiz_number: int, total_questions: int) -> bool:
        """
        显示测试开始界面
        
        Args:
            quiz_number: 测试编号
            total_questions: 总题目数量
            
        Returns:
            是否继续（True）或退出（False）
        """
        self.screen.fill(self.bg_color)
        
        # 标题
        title_text = f"知识点测试 #{quiz_number}"
        title_surface = self.title_font.render(title_text, True, self.text_color)
        title_rect = title_surface.get_rect(center=(self.screen_width // 2, self.screen_height // 4))
        self.screen.blit(title_surface, title_rect)
        
        # 说明文字
        info_lines = [
            f"本次测试共有 {total_questions} 道题目",
            "每题都是4选项单选题",
            "请按 A、B、C、D 键选择答案",
            "",
            "按空格键开始测试",
            "按ESC键退出"
        ]
        
        y_offset = self.screen_height // 2 - 100
        for line in info_lines:
            if line:  # 跳过空行
                text_surface = self.question_font.render(line, True, self.text_color)
                text_rect = text_surface.get_rect(center=(self.screen_width // 2, y_offset))
                self.screen.blit(text_surface, text_rect)
            y_offset += 40
        
        pygame.display.flip()
        
        # 等待用户输入
        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_SPACE:
                        return True
                    elif event.key == pygame.K_ESCAPE:
                        return False
    
    def display_question(self, question_data: Dict[str, Any], question_number: int, total_questions: int) -> Tuple[Optional[str], float]:
        """
        显示单个测试题并获取用户答案
        
        Args:
            question_data: 题目数据
            question_number: 当前题目编号
            total_questions: 总题目数量
            
        Returns:
            (用户选择的答案, 答题时间)，如果退出则返回(None, 0)
        """
        start_time = time.time()
        selected_answer = None
        
        while selected_answer is None:
            self.screen.fill(self.bg_color)
            
            # 显示题目编号和进度
            progress_text = f"题目 {question_number}/{total_questions}"
            progress_surface = self.title_font.render(progress_text, True, self.text_color)
            progress_rect = progress_surface.get_rect(center=(self.screen_width // 2, 50))
            self.screen.blit(progress_surface, progress_rect)
            
            # 显示题目
            question_text = question_data['question']
            question_lines = self._wrap_text(question_text, self.question_font, self.screen_width - 2 * self.margin)
            
            y_offset = 120
            for line in question_lines:
                text_surface = self.question_font.render(line, True, self.text_color)
                text_rect = text_surface.get_rect(center=(self.screen_width // 2, y_offset))
                self.screen.blit(text_surface, text_rect)
                y_offset += int(self.question_font.get_height() * self.line_spacing)
            
            # 显示选项
            y_offset += 40
            options = question_data['options']
            for i, (key, text) in enumerate(options.items()):
                option_text = f"{key.upper()}. {text}"
                option_lines = self._wrap_text(option_text, self.option_font, self.screen_width - 2 * self.margin)
                
                for line in option_lines:
                    text_surface = self.option_font.render(line, True, self.text_color)
                    text_rect = text_surface.get_rect(center=(self.screen_width // 2, y_offset))
                    self.screen.blit(text_surface, text_rect)
                    y_offset += int(self.option_font.get_height() * self.line_spacing)
                
                y_offset += self.option_spacing
            
            # 显示说明
            instruction_text = "请按 A、B、C、D 键选择答案，ESC键退出"
            instruction_surface = self.explanation_font.render(instruction_text, True, self.text_color)
            instruction_rect = instruction_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 50))
            self.screen.blit(instruction_surface, instruction_rect)
            
            pygame.display.flip()
            
            # 处理用户输入
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return None, 0
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        return None, 0
                    elif event.unicode.lower() in self.option_keys:
                        selected_answer = event.unicode.upper()
                        break
            
            # 检查超时
            if QUIZ_CONFIG['question_timeout'] > 0:
                elapsed_time = time.time() - start_time
                if elapsed_time > QUIZ_CONFIG['question_timeout']:
                    selected_answer = 'TIMEOUT'
                    break
        
        response_time = time.time() - start_time
        return selected_answer, response_time
    
    def display_answer_feedback(self, question_data: Dict[str, Any], user_answer: str, response_time: float) -> bool:
        """
        显示答案反馈
        
        Args:
            question_data: 题目数据
            user_answer: 用户答案
            response_time: 答题时间
            
        Returns:
            是否继续（True）或退出（False）
        """
        self.screen.fill(self.bg_color)
        
        correct_answer = question_data['correct_answer'].upper()
        is_correct = user_answer == correct_answer
        
        # 显示结果
        if user_answer == 'TIMEOUT':
            result_text = "超时未答题"
            result_color = self.wrong_answer_color
        elif is_correct:
            result_text = "回答正确！"
            result_color = self.answer_highlight_color
        else:
            result_text = "回答错误"
            result_color = self.wrong_answer_color
        
        result_surface = self.title_font.render(result_text, True, result_color)
        result_rect = result_surface.get_rect(center=(self.screen_width // 2, 100))
        self.screen.blit(result_surface, result_rect)
        
        # 显示正确答案
        if QUIZ_CONFIG['show_correct_answer']:
            answer_text = f"正确答案: {correct_answer}"
            answer_surface = self.question_font.render(answer_text, True, self.answer_highlight_color)
            answer_rect = answer_surface.get_rect(center=(self.screen_width // 2, 150))
            self.screen.blit(answer_surface, answer_rect)
        
        # 显示解释
        if QUIZ_CONFIG['show_explanation'] and 'explanation' in question_data:
            explanation_lines = self._wrap_text(question_data['explanation'], self.explanation_font, self.screen_width - 2 * self.margin)
            
            y_offset = 220
            for line in explanation_lines:
                text_surface = self.explanation_font.render(line, True, self.explanation_color)
                text_rect = text_surface.get_rect(center=(self.screen_width // 2, y_offset))
                self.screen.blit(text_surface, text_rect)
                y_offset += int(self.explanation_font.get_height() * self.line_spacing)
        
        # 显示答题时间
        time_text = f"答题时间: {response_time:.1f}秒"
        time_surface = self.explanation_font.render(time_text, True, self.text_color)
        time_rect = time_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 100))
        self.screen.blit(time_surface, time_rect)
        
        # 显示继续说明
        continue_text = "按空格键继续，ESC键退出"
        continue_surface = self.explanation_font.render(continue_text, True, self.text_color)
        continue_rect = continue_surface.get_rect(center=(self.screen_width // 2, self.screen_height - 50))
        self.screen.blit(continue_surface, continue_rect)
        
        pygame.display.flip()
        
        # 自动进入下一题或等待用户输入
        if QUIZ_CONFIG['auto_advance_after_answer']:
            time.sleep(3)  # 显示3秒后自动继续
            return True
        
        # 等待用户输入
        while True:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    return False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_SPACE:
                        return True
                    elif event.key == pygame.K_ESCAPE:
                        return False
    
    def _wrap_text(self, text: str, font: pygame.font.Font, max_width: int) -> List[str]:
        """
        文本换行处理
        
        Args:
            text: 要换行的文本
            font: 字体对象
            max_width: 最大宽度
            
        Returns:
            换行后的文本列表
        """
        words = text.split(' ')
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + (" " if current_line else "") + word
            if font.size(test_line)[0] <= max_width:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        
        if current_line:
            lines.append(current_line)
        
        return lines if lines else [text]
