欢迎来到 CS285 第 19 讲。在今天的讲座中，我们将讨论如何将控制问题重构成一个推理问题。我们实际上会看到，上周关于变分推理讲座中的一些思想，在今天的讲座中会作为解决强化学习问题的方法出现，一旦这些问题被重构成推理问题。

在今天的讲座中，我们将讨论以下几个问题：强化学习和最优控制是否为人类行为提供了一个合理的模型？是否有一个比我们目前所见的传统最优性概念更好的解释？我们能否将最优控制、强化学习和规划推导为概率推理？如果可以，我们是在什么模型中进行推理的？这又如何改变我们的强化学习算法，我们是否能基于这个基础推导出更好的算法？在下一讲中，我们将看到这些思想对于逆强化学习方法是何等关键，这些方法试图通过观察近乎最优的人类行为来恢复奖励函数。

因此，今天讲座的目标是: 理解推理与控制之间的联系，理解特定的强化学习算法如何在这个框架下实例化，以及理解为什么这可能是一个好主意。

让我们从讨论如何建模人类行为开始。我们知道，人类有时会做一些有目标导向的事情，我们可以争论人类有多理性，但我认为，说人类行为的某一部分是目标导向和有意的，这是合理的。人们研究这个问题已经有超过 100 年的历史了，研究最优性的概念如何能最好地代表人类行为，从最低级的原始行为，比如我们如何走路、如何导航到某个地方、如何伸手去拿东西，一直到更高级的概念，比如我们如何规划在城市中导航的路线。认为如果人类是理性的、智能的生物，那么我们应该以反映某种最优性的方式来执行我们的任务，从最低级的运动控制到最高级的认知技能，这是相当合理的。事实上，定义理性的方式之一就是说，一个理性的决策者，其行为可以用明确定义的效用函数来表达。

所以，我们可以说，我们有一个很好的框架来思考最优决策，即我们在讨论强化学习和最优控制时学到的框架。我们能用这个框架来理解人类行为吗？例如，我们可以做的一件事是，假设一个人或一个动物的行为方式是最优的，他们是理性的决策者，那么我们能否找出他们是在哪个奖励函数下表现为最优的？这实际上是我们周三讲座要讨论的内容，关于逆强化学习。

你可以在确定性环境中做这件事，也可以在随机性环境中做。之前我们讨论的是，给定奖励函数，我们想恢复一个策略。现在我们可能会想，我们正在观察一个人，观察他们的策略，我们想找到一个合适的奖励函数来解释我们观察到的数据和行为。如果我们研究人类或动物行为，这是一个非常诱人的想法，因为最优性原则提供了一个强大的工具来理解某人为什么会做某件事。如果你能用一个简单紧凑的目标函数来解释他们的行为，那么你就能预测他们在其他情况下会做什么。这当然非常直观，如果你知道某人想要什么，你就能更有效地预测他们会做什么。

好，想象一下你是一名科学家，你想思考最优控制和强化学习如何解释行为，比如说动物的行为。你找来一只猴子，让这只猴子完成一些任务，你想了解它的目标函数。你可能做的一件事是，选择一个目标函数已知的任务，也许猴子需要移动一个杠杆，让屏幕上的一个点与另一个点匹配。然后，如果猴子做对了，它就会得到奖励。

现在你可能会说，我知道奖励函数，所以如果最优行为是对动物行为的一个很好的解释，那么我期望猴子的行为会类似于一个最优控制器或强化学习算法。假设猴子必须从这个橙色圆圈移动到这个红色十字，最优的轨迹是这条直线。研究人员进行实验后发现，是的，这只猴子是一只好猴子，它通常能到达红色十字，但它是以各种不同的方式做到的。所以它并不总是走直线，也许有一天猴子觉得有点懒，但它仍然到达了目的地并得到了奖励。

那么，到底发生了什么？是猴子有点笨吗？也许一个非常聪明的人会更准确，但猴子只是不够精确？还是有其他事情在发生？事实证明，毫不奇怪，猴子和人类通常都不是完全最优的，我们通常会犯错误。但关键是，随着更多的练习，这些错误倾向于发生在对任务成功影响较小的方面。

直观地说，猴子可能会选择一条间接的路线到达目标，是因为它感觉有点懒，没有真正集中注意力，但它也知道到达目标的具体方式并没有那么重要。这是我们目前讨论的强化学习算法没有真正考虑到的，它们没有“偷懒”的概念，也没有理解“某件事没那么重要，因此可以做得不那么完美”的概念。有些错误比其他错误更重要。事实证明，适当地考虑这一点，对于开发能够解释人类和动物智能行为的模型至关셔重要，并且也成为我们构建更好强化学习算法以及之后构建逆强化学习算法的工具。

人类和动物的自然行为，在一阶近似下是随机的。这意味着，两次面对同样的情况，猴子不会做完全相同的事情。我们可以争论这到底是真正的随机，还是仅仅受到实验中未考虑的大量其他外部和内部因素的影响。例如，也许猴子饿了，也许它的手指痒了，也许它只是有点累，也许它分心了，不小心把操纵杆向左偏了一点。但我们可以将这些效应在一阶近似下视为随机的。然而，好的行为仍然是最有可能的。所以，虽然猴子可能会容忍一些适度的错误，它仍然会确保到达目标，因为那才能让它得到它非常想要的奖励。

因此，如果我们相信自然的理性决策者的行为是随机的，那么我们就需要一个近乎最优行为的概率模型。我们现有的模型并没有真正做到这一点，它们没有告诉我们为什么我们可能会选择随机而非最优。事实上，对于确定性和随机性的最优控制和强化学习公式，我们都可以证明，在所有完全可观测的环境中，都存在确定性的最优策略。这对于任何在状态-动作边缘分布上是线性的目标函数都成立，也就是说，任何可以表示为某个不依赖于策略的奖励在状态-动作分布下的期望值的目标，都会有一个确定性策略作为解。

所以，很明显，这个框架无法解释随机行为是理性的。我们需要一种不同的理性概念。当我们想表示随机事件时，一个我们经常求助的强大工具是概率图模型。这就是我们今天讲座要做的事情。我们实际上要画一个概率图模型，使得在该模型中进行推理能够产生近乎最优的行为。关键是，这种近乎最优的行为并不总是与强化学习和最优控制的解相同，但会非常相似。它会非常像我们前一张幻灯片中看到的次优猴子行为，即在其他条件相同的情况下，智能体会尝试完成任务，但对于那些对任务影响较小、对奖励影响很小的方面，智能体宁愿随机地去做。

在思考如何为决策和控制绘制一个图模型时，我们当然必须包含我们在马尔可夫决策过程（MDP）中常见的变量，即状态和动作。我们已经知道状态和动作是如何相互关联的。目前，我们将坚持使用完全可观测的设置，所以我们也可以将观测值加入这个谱系，但为了避免符号混乱，我们暂时不考虑它。然而，我们需要添加一些额外的变量来代表任务，代表为什么智能体可能选择一个动作而不是另一个。到目前TA，我们有转移概率 p(s'|s, a)，我们的目标是建模轨迹的联合分布 p(s1..T, a1..T)，也就是我们的轨迹 τ。我们可以将这个概率设为什么呢？

如果我们只有 MDP 中的条件概率分布（CPD）、转移概率和初始状态分布，那么就没有最优行为的假设。所以我们必须添加一些其他东西来表示为什么你可能会选择一个更优的动作而不是一个次优的动作。我们将这些称为“最优性变量”，我将用草书 O 来表示。这些最优性变量是被观测到的，你知道猴子正在努力完成任务。如果你不知道这一点，你会对它的行为做出不同的推断。

现在我们将做一个稍微奇怪的建模选择，但稍后我们会看到，这个建模选择实际上会导出一个非常方便和优雅的数学公式。我们做的建模选择是，这些变量是二元的，你可以认为它们基本上是真或假的变量，表示“猴子在此时此刻是否试图做到最优？”如果猴子总是在努力做到最优，那么所有这些变量都是被观测到的，并且都设置为真。那么，我们需要解决的推理问题就是，给定从时间 1 到 T 的所有最优性变量都为真，一个轨迹的概率是多少？或者，我们可能想在给定初始状态的情况下进行这个推理。所以我们可以计算 p(τ | o1..T) 或 p(τ | o1..T, s1)。

我们将选择的 p(ot | st, a) 的具体分布形式是，我们将 ot 等于真的概率设置为在 st, a 处奖励的指数，即 exp(r(st, a))。这又会看起来是一个有些随意的决定，我们稍后会看到，这个看似随意的决定实际上会导出一个非常方便和优雅的数学框架。但现在，我们只能把它当作一个给定的条件。让我们试试看，将概率设为这个值，然后看看数学上会推导出什么。

为了做出这个陈述，我们需要一个技术条件，那就是我们需要所有的奖励都是负数。因为一个离散（在这里是伯努利）随机变量的概率必须小于 1，而任何正数的指数都会大于 1。所以我们需要所有的奖励都是负数。但幸运的是，最优行为对于奖励的加性因子是不变的。所以，如果奖励不是负数，你可以简单地构造一个等价的决策问题，其奖励等于旧奖励减去最大可能的奖励。基本上，说奖励总是负的，只是意味着你减去了最大值，这意味着所有剩余的奖励都是负的。所以这实际上不是一个限制，只要奖励是有界的，你就可以在不失一般性的情况下这样做。当然，如果奖励是无界的，这是不可能的，那么你就可能得到无限的奖励，但这也不起作用，反正我也不知道如何处理无限奖励，所以这也不是一个太大的限制。

好，现在我们定义了一个概率图模型。它有动态性，有奖励，看起来我们可以做贝叶斯规则式的计算，得到一些分布，然后看看这个分布的方程是否合理。我将条件概率的定义代入，写出 p(τ | o1..T) = p(τ, o1..T) / p(o1..T)。然后我可以将所有的条件概率分布（CPD）代入。我只关心轨迹的概率，所以我会忽略分母，写成“正比于”。它正比于我们所有 CPD 的乘积，我将其写为 p(τ)，这只考虑了动态性和初始状态，再乘以所有这些伯努利随机变量概率在时间 1 到 T 上的乘积。

这基本上意味着，我们将 p(τ) 的乘积与所有时间步上指数化奖励的乘积相乘。这似乎相当合理。我们知道，指数的乘积是指数之和的指数，所以我们将它等价地写成一种可能更能暗示其作用的方式，即 p(τ) * exp(∑ r(τ))。

这应该立刻给我们一些相当吸引人的直觉，关于这个框架在做什么。例如，想象一下动态是确定性的，这意味着 p(τ) 基本上只是一个指示变量，如果 τ 是一条物理上一致的轨迹，它就是 1，否则就是 0。在这种特殊情况下，我们会看到，最可能的轨迹是奖励最高的那条。然而，次优的轨迹仍然有非零的概率，这个概率随着其奖励的减少而呈指数级下降。这实际上看起来相当直观。它基本上意味着，如果猴子有多个奖励相等的不同选择，它会随机地从中选择一个。但如果有一个选择的奖励低得多，它选择那个选择的可能性就会呈指数级下降。

所以，它在伸手去拿目标时偏离直线轨迹的原因是，它通过任何其他方式到达目标所获得的奖励大致相同。也许因为耗时更长，奖励会低一点，因为折扣因子让它变得更饿，但基本上是差不多的。而未能到达目标则会导致一个糟糕得多的奖励，因此它不会那样做。所以，这直观上似乎解释了我们在前一张幻灯片中看到的那种行为。我们基本上构建了一个概率模型，其中最优的轨迹是最可能的，但次优的轨迹也可能发生，只是其概率随着奖励的减少而呈指数级下降。

那么，这一切为什么有趣呢？如果你想为猴子建模，这可能对你很有趣。但如果你不关心猴子呢？将次优行为表示为在某种宽松的最优性概念下近似最优的能力，对于理解次优智能体的行为通常非常重要。对于模仿学习也是如此，如果你想弄清楚一个人试图向你展示什么奖励函数，你必须考虑到他们不会完美地完成。事实证明，这对于逆强化学习非常重要，我们将在周三的讲座中讨论。你还可以应用推理算法来解决基于这个框架的控制和规划问题。因为我们画了一个概率图模型，其中推理对应于解决控制问题，这意味着我们可以利用大量的推理方法来实际解决控制和规划问题，这被证明是一个相当强大的思想。最后，这为为什么即使确定性行为是可能的，随机性行为也可能被偏好提供了解释。这对于探索和迁移学习等事情非常有用。它对探索和迁移学习有用的原因是，如果你以多种不同的方式执行一个任务，那么当环境变化，任务需要以稍微不同的方式执行时，你更有可能迁移到新的环境中。

在今天讲座的大部分时间里，我们实际上将讨论如何执行这个推理问题。我们将看到，将精确推理和近似推理应用到这个图模型中，会得到与我们已经学过的强化学习算法非常相似的算法。

---

我们如何在这个模型中进行推理呢？我们需要了解三个操作。第一个我们需要知道的操作是如何计算“后向消息”（backward messages）。如果你研究过隐马尔可夫模型（HMM）或卡尔曼滤波器，或者听说过变量消除（variable elimination），那么你可能已经对如何在这个模型中进行推理有一些想法了。它是一个链式结构的动态贝叶斯网络，这意味着它应该非常适合通过消息传递进行推理，而消息传递当然是变量消除的一个特例。

在这样的图中，我们想计算两种消息，非常像你在 HMM 或卡尔曼滤波器中会做的那样。第一种消息是后向消息，它告诉你，给定你当前所处的状态和动作，从现在到轨迹结束一直保持最优的概率是多少。我们将这些后向消息称为 β。事实证明，使用 β 你实际上可以恢复策略。策略是在时间步 t，给定时间步 t 的状态以及整个轨迹从 1 到 T 都是最优的证据下，一个动作的概率。这是这个图模型中的随机最优策略。事实证明，如果你能计算后向消息，你就能计算策略。

第三个非常有用的操作，尤其是在我们处理逆强化学习时，是计算所谓的“前向消息”（forward messages）。前向消息有点像后向消息的反向模拟。一个前向消息说，如果你在时间步 t-1 之前都是最优的，那么你最终到达特定状态 st 的概率是多少。如果我们将后向消息和前向消息放在一起，我们实际上可以恢复状态占用率，这在技术上对于恢复最优策略不是必需的，但对于进行逆强化学习是必需的。

让我们从后向消息开始，它们确实是最重要的，因为如果你能计算这些，你就能恢复近乎最优的策略。我们推导后向消息的方式只是通过一些递归、概率论和一点代数。首先，我们可以取后向消息的这个方程，然后插入下一个状态 st+1 并将其积分掉。所以后向消息等于在 st+1 的所有值上对 p(o_t..T, st+1 | st, a) 的积分。现在我们要做的，就是使用我们模型中的条件概率分布（CPD）来分解这个分布。我们做这个分解的目标是恢复一个递归表达式，使得我们可以将 β_t(st, a) 表示为 β_t+1(st+1, at+1) 的某个函数。

为了分解这个表达式，我们必须注意到，未来的最优性变量，即 o_t+1..T，在给定 st+1 的条件下，与过去的一切都是独立的。我们可以通过检查图模型来看到这一点。这意味着我们可以将这个表达式分解为三个部分。第一部分是从 t+1 到结束的所有最优性变量给定 st+1 的概率。我们知道我们不必以 st 和 a 为条件，因为给定 st+1，所有未来的最优性变量都独立于 st 和 a。然后我们有 p(st+1 | st, a)，这只是我们已经知道的转移概率。然后我们有剩余的最优性变量 ot 给定 st 和 a 的概率，这只对应于指数化的奖励，因为那只是我们图模型中的一个 CPD。

所以我们知道这个东西，我们知道那个东西，那是我们的转移概率，尽管当我们在做强化学习时，我们可能想在不知道这个概率的函数知识的情况下计算后向消息。现在我们假设我们知道它。这只剩下最后一项了。从 t+1 到 T 的最优性概率，给定 st+1，可以写成从 t+1 到 T 的最优性概率，给定 st+1 和 at+1，乘以 at+1 给定 st+1 的概率。这一部分只是时间步 t+1 的后向消息。

我们已经得到了一些改进，我们得到了一个基本上是递归的表达式，除了它有一个我们还没有定义的奇怪项：一个动作给定一个状态的概率。关键是，这不是一个策略。这是在说，先验地哪些动作是可能的，也就是说，如果你不知道你是否最优，你采取某个特定动作的可能性有多大。通常，如果你不知道你是否正在努力做到最优，你可能对哪些动作更可能或更不可能一无所知。所以我们可以定义这一项，我们可以定义一个动作先验 p(a|s)，但我们现在假设它是均匀的。

这个假设有几个合理的原因。首先，如果你对猴子想做什么一无所知，那么你可能无法说出它更可能或更不可能执行哪些动作。其次，也许更具数学意义的是，如果你想施加一个动作先验，事实证明你可以等价地修改奖励函数，并保持一个均匀的动作先验，从而得到完全相同的解。这个结果稍微有点微妙，我把它留给你们作为练习在纸上推导。但长话短说，现在我们假设动作先验 p(at+1|st+1) 是均匀的，这意味着它是一个常数，我们可以忽略它。这并不意味着策略是均匀的，因为记住我们的策略是后验分布 p(at|st, o1..T)。动作先验只是在你知道你是否最优之前，动作的先验概率。

好，现在我们已经去掉了动作先验，并且已经用转移概率、最优性概率以及递归地用未来的后向消息来表达了一切，我们可以写下一个计算后向消息的递归算法。它是一个从轨迹末尾开始，向后步进直到开始的 for 循环。在从末尾到开始的每个时间步，我们计算时间步 t 的后向消息，即当前最优性概率乘以在 st+1 上的期望值，这个量我称之为 β_t+1(st+1)。β_t+1(st+1) 是一个状态后向消息。状态后向消息只是状态-动作后向消息在动作上的期望值，动作分布遵循均匀的动作先验。如果我们交替这两个步骤，那么我们就可以从末尾到开始递归地计算后向消息。最后一个后向消息 β_T 只是最后的奖励，因为 p(oT|sT, aT) 实际上是你模型中已经存在的 CPD。

现在让我们仔细看看这个后向传递。这里是我在前一张幻灯片上推导的递归算法。我们将做一些定义，用一些非常具有暗示性的名字来帮助我们理解这个算法。首先，我们将 vt(st) 定义为 β_t(st) 的对数。然后我们将 qt(st, a) 定义为 β_t(st, a) 的对数。现在我们可以在对数空间中写这些方程。

如果我们在对数空间中写状态后向消息的方程，我们得到 vt(st) = log(∫exp(qt(st, a)) da)。这有点意思，一个有趣的方程。乍一看，它似乎没有做什么特别直观的事情。但想象一下，qt 的值非常大。如果你对一堆大值的指数求和，那么其中最大的那个值将主导这个和。这意味着，当你然后取对数时，你将恢复一个接近于你求和的那些指数化值中最大值的数字。在极端情况下，你可以想象，当 qt 越来越接近无穷大时，指数和的对数越来越接近一个最大值（max）。事实上，我们可以称这个指数和的对数为一种“软最大值”（softmax）。这与我们用作深度学习损失函数的 softmax 不同，它是 max 算子的一个软松弛。所以，vt(st) 的这个表达式在 qt 越来越大时，接近于 max_a qt(st, a)。

这非常有趣。我们在强化学习中看到，最优值函数是最优 Q 函数的最大值。现在我们看到，在推理的视角下，值函数是 Q 函数的软最大值，这有点道理。我们想软化我们的最优性概念，这样稍微次优的事情仍然是可能的。

让我们谈谈另一个表达式。如果我们在对数空间中写另一个表达式，我们会得到以下方程：qt(st, a) = r(st, a) + log(E[exp(vt+1(st+1))])。这看起来很像一个贝尔曼备份，因为它有一个奖励项和一个期望项，只是现在你在期望外面有一个对数。

好的，让我们试着更好地理解这个方程。在一个特殊的例子中，它和贝尔曼方程完全一样。花点时间想一想那个特殊例子是什么，在什么情况下，我幻灯片右边的方程和贝尔曼备份完全一样？答案是在下一个状态是当前状态和动作的确定性函数的情况下。因为如果下一个状态是当前状态和动作的确定性函数，那么期望值中只有一个非零元素，这意味着对数和指数会抵消。所以在确定性情况下，它基本上和贝尔曼备份一样。在随机转移的情况下，你会得到一种乐观的转移。注意，这也是一个对数和指数，这意味着这些目标值将被具有最大值的下一个状态所主导。所以如果你有可能走运，你会认为那更有可能。这实际上不是一个好主意，我们可以通过修正这个问题来改进我们基于推理的过程。但直观地说，这个问题发生的原因是，当我们问“给定一个特定的状态和动作，你有多大可能是最优的？”这个问题时，它没有区分是因为你走运而最优，还是因为你采取了正确的行动而最优。我们稍后会回到这个问题，并更详细地讨论随机情况以及如何解决这个问题。但现在，我需要注意的一点是，至少在确定性情况下，它与我们之前看到的经典价值迭代概念完全匹配，唯一的例外是我们使用软最大值而不是硬最大值。

---

到目前为止，我们已经看到了如何将控制框架化为特定图模型中的推理，然后我们讨论了如何在该图模型中进行精确推理，并理解了三种可能的推理问题：计算后向消息、计算使用这些后向消息的策略，以及计算前向消息。正如我所提到的，这在稍后我们讨论逆强化学习时会很有用。

然而，我们目前讨论的所有推理过程都是精确推理。当然，在复杂、高维或连续的状态空间中，或者在动态未知、我们无法获得转移概率而只能通过执行 rollout 来从中采样的情况下，我们需要进行近似推理。这就是我将在下一节中讨论的内容。我实际上将使用我们上周学到的工具——变分推理的工具——来展示如何从这个“控制即推理”的框架中推导出无模型的强化学习过程。

在设计这些近似算法的过程中，我们还将看到如何为我之前提出的一个特定问题——即我提到的“乐观主义”问题——设计一个解决方案。如果你还记得讲座的前一部分，我们讨论了状态后向消息和状态-动作后向消息的对数如何可以被解释为与价值函数和 Q 函数非常相似。当我们用对数空间写出这些方程时，我们推导出了一个与价值迭代非常相似的算法，只是动作上的最大化被软最大化（softmax）所取代，并且贝尔曼备份具有对数-期望-指数的形式。

软最大化其实不是什么大问题，实际上，我们正是从这里得到了“软最优性”的概念，所以我们实际上想要它。但是这种备份有点问题。这种备份的麻烦在于，指数化下一状态价值的期望值的对数，将由最幸运的状态所主导。最容易看到这一点的方法是想象动作对应于购买彩票。你有千分之一的机会获得极高的回报，而有千分之九百九十九的机会一无所获。这样做的效果是，期望值是 0 * 0.999 加上一百万 * 0.001。当你对它取指数然后再取对数时，那些零的效果基本上会消失，最终的价值将由那个积极的结果所主导。这真是个坏消息，因为当然，买彩票不是一个好主意，它的期望值不高，但它的对数-期望-指数化价值很高。所以，基本上，这种备份导致了一种乐观偏见。

为什么会发生这种情况呢？我们正在解决的推理问题是，在给定最优性的前提下，推断出最可能的轨迹，然后通过边际化和条件化得到策略 p(at | st, o1..T)。这个推理问题直观地在问：“给定你获得了高回报，你的动作概率是多少？”现在回想一下彩票的例子。如果你知道你得到了一百万美元，那确实让你更有可能去玩了彩票。但这并不意味着玩彩票是个好主意。

这里的根本矛盾在于，我们问的推理问题并不完全是我们真正想要答案的那个问题。我们想知道的是，“如果你试图做到最优，你会做什么？”，而不是“鉴于你得到了一百万，我认为你做了什么？”。这个问题的根源在于，在给定 st, a 和 o1..T 的条件下，st+1 的后验概率与其先验概率不同。当我们执行这个推理过程时，我们实际上在改变动态以与我们的证据相符。这里的直觉与彩票的例子非常一致。如果你知道你得到了一百万美元并且你买了彩票，那么你赢得彩票的概率就更高了，因为你得到一百万美元的证据增加了你真的赢得彩票的信念。但当然，在现实中，动态是不允许改变的。在现实中，我们想弄清楚在实际的原始动态中，什么是近似最优的做法。

所以问题是，在给定你获得高回报的情况下，你的转移概率是多少？但在某种意义上，我们不关心这个问题，你的转移概率应该保持不变。那么，我们如何解决这个乐观主义问题呢？我们想要的是策略，但我们不希望我们推断策略的过程允许我们改变动态。直观地说，我们想要的是，在给定你获得高回报的情况下，你的动作概率是多少，前提是你的转移概率没有改变。

我们可以采取的一种方法是，我们能否找到另一个关于状态和动作的分布 q，它接近于给定 o1..T 的状态和动作的后验分布，但具有相同的原始动态？在这个近似后验 q 中，我们希望动态与原始动态相同，不受你对奖励的知识的影响，但我们希望动作概率改变。

我们在哪里见过这个？我们在哪里见过用另一个具有某些约束的概率分布来近似一个概率分布的概念？如果我们暂时说 x 是 o1..T，z 是 s1..T 和 a1..T，那么这个问题就等价于说，找到一个 q(z) 来近似 p(z|x)。基本上，就是找到一个近似分布，它能准确地近似未观测变量的后验分布，而这基本上就是变分推理解决的问题。所以，我们能把这个问题——找到另一个分布 q(s1..T, a1..T) 它接近后验 p 但具有动态 p(st+1 | st, a)——硬塞进变分推理的框架吗？

为了使用变分推理来执行控制，我们将为 q 定义一个有点奇特的分布族。我们将 q(s1..T, a1..T) 定义为 p(s1) 的乘积，乘以每个时间步的转移概率 p(st+1 | st, a)，再乘以一个动作分布 q(at | st)。这个变分分布的定义非常奇特，因为通常当我们使用变分推理时，我们学习整个变分分布，但在这里，我们实际上是固定了变分分布的某些部分与 p 相同，而只学习动作条件部分。所以，我们将有与 p 相同的动态和相同的初始状态，这对于对抗这种乐观偏见很重要。所以，我们为了学习这个近似后验而学习的唯一东西就是 q(at | st)。

我们可以用图来表示这一点。我们试图进行推理的真实图模型在这里显示。我们有观测变量 o1..T 和未观测变量 s 和 a。所以我们有初始状态、转移概率和最优性变量概率。近似对应于这个图模型。记住，在变分推理中，变分分布不包含观测变量，所以 o 被移除，只剩下 s 和 a 是合理的。我们有相同的初始状态分布，相同的转移概率，我们不再有 o，但我们有 q(at | st)，这是我们唯一要学习的部分。

顺便说一下，我应该提到，所有这些推导都是在 s1 未被观测的情况下提出的。通常情况下，你可能实际上知道 s1，在这种情况下，p(s1) 就消失了，s1 节点在各处都会被着色，它实际上不会作为你的变分分布的一部分来表示。推导那个情况非常直接，只是会给符号增加一些混乱，这就是为什么我在这些幻灯片上省略了这一点，并将 s1 视为一个潜在变量。但请记住，如果你处于一个你知道当前状态，只想弄清楚未来状态和动作的情况下，那么 s1 将被观测到。

现在，为了将这一点与上周的变分推理讨论联系起来，我们再次说，我们的观测变量 x 就是 o1..T，我们的潜在变量 z 对应于 s1..T 和 a1..T。如果第一个图模型是 p(z|x)，那么第二个就是 q(z)，然后我们将根据这些写出我们的变分下界，然后我们将优化那个变分下界，我们会看到，它实际上与我们已经学过的许多强化学习算法非常接近。

这里是我们在上周讲座中看到的变分下界。x 的对数概率大于或等于在 q(z) 下的期望值，期望内容是 log p(x, z) - log q(z)。这对于任何 q(z) 都成立，但当然，正如我们上周学到的，q(z) 越接近后验 p(z|x)，这个界就越紧。最后一项只是 q 的熵。

代入我们前一张幻灯片中对 x 和 z 的定义，我们可以说，让 q 等于这个东西，然后我们可以写出 log p(o1..T)——我们证据的对数概率——大于或等于在 q 分布下的 s1..T 和 a1..T 的期望值，期望内容是我们图模型中所有概率的对数：log p(s1) + ∑log p(transitions) + ∑log p(optimality variables)，再减去熵，熵将是 -log p(s1)（这个 s1 来自我们对 q 的定义）- ∑log p(transitions)（这也来自我们对 q 的定义），然后是 -∑log q(at | st)。

现在我们可以看到为什么我们对 q 做了这个特定的选择。我们选择 q，使得初始状态概率和转移概率非常方便地抵消掉了。这意味着我们的界现在只对应于最优性变量的对数概率之和，减去在 q 下动作的对数概率。代入 p(ot | st, a) 的定义，我们得到这个表达式：我们似然的下界就是总奖励的期望值，减去每个时间步的 -log q(at | st)。我可以通过期望的线性性质将求和移到期望外面，并将 log q 项替换为熵。现在我们可以看到，这个下界完全等价于最大化奖励和最大化动作熵。记住，q 具有与原始问题相同的初始状态分布和转移概率，这意味着这正是期望奖励——我们原始的强化学习目标——加上这些额外的熵项。额外的熵项为“为什么你不仅仅想要单一的最优解，而可能想要一些也模拟了轻微次优情况的随机行为”提供了理由。回想一下次优的猴子，优化这个目标基本上会给我们次优的猴子。

---

好了，让我们讨论一些基于我在上一节中提出的思想以及一些可能更直接的方法的强化学习算法。首先，我们可以想象用软最优性来做 Q-learning。标准的 Q-learning 使用这个更新规则：你将新的 φ 向量设置为旧的加上某个学习率乘以梯度 φQ，再乘以贝尔曼误差 r + max V - Q，其中你的目标值只是下一时间步 Q 值的最大值。你可以用完全相同的方式实现一个软 Q-learning 过程。事实上，Q 函数的更新是相同的，唯一的区别是当你计算你的目标值时，你不是取下一动作的硬最大值，而是取一个软最大值，也就是指数化 Q 值的对数积分，或者如果你有离散动作，就是指数化值的对数和。

这非常直接，你可以用我们之前在讨论基于价值的方法时从价值迭代推导 Q-learning 的相同方式来推导它。如果你想更熟悉这些材料，我鼓励你把这作为一个练习来做。在软 Q-learning 中恢复的策略是由指数化的优势函数给出的，而不是贪婪策略。可以证明这个策略是相应变分推理问题的解。

因此，最终的软 Q-learning 算法看起来与我们之前看到的算法非常相似。我们采取某个动作，观察到 s_i, a_i, s_i', r_i，将其添加到我们的缓冲区，从缓冲区中采样一个小批量，然后我们计算一个目标值，唯一的区别是，我们现在不是取下一时间步 Q 值的最大值，而是取一个软最大值。但就像之前一样，我们更新我们的 Q 函数，进行一个回归步骤来提高我们对目标值的拟合度，并且我们延迟更新我们的目标网络参数。所以，除了使用软最大值之外，一切都完全相同。

我们也可以不担心动态规划，而是回到我从变分推理中得到的原始目标，即期望奖励之和加上熵之和。实际上，推导一个策略梯度算法来优化这个目标非常直接。当然，目标的期望奖励部分的梯度正是标准的策略梯度，唯一的新东西是熵。

这个过程背后的直觉是，当策略 π 最小化 π 与 1/Z * exp(Q) 之间的 KL 散度时，策略 π 将与指数化的 Q 值成正比，或等于指数化的优势函数。当然，这个 KL 散度在常数范围内等于在 π 下 Q 的期望减去 π 的熵。这通常被称为熵正则化策略梯度，对于策略梯度来说，这可能是一个好主意，因为它能对抗策略熵的过早崩溃。记住，在线策略梯度算法完全是用策略中存在的随机性来进行探索的。所以如果策略过早变得过于确定性，你会得到非常糟糕的结果。所以我们添加到策略目标中的这个熵正则化器在实践中可能非常有帮助。

事实证明，它也与 Q-learning 算法非常密切相关，这在一些我将在本讲座最后一部分引用的先前工作中有所讨论。

好的，让我们稍微谈谈在这个推理框架中，策略梯度与 Q-learning 的关系。如果我们写出目标函数，这是目标函数，我称之为 J(θ)，就像在策略梯度讲座中一样。熵只是在 π 下 -log π 的期望值，所以这是写这个的等价方式。当我们对这个表达式求梯度时，我们可以将梯度写成两项之和。第一项是常规的策略梯度，它将 r - log π 视为奖励。第二项的梯度穿过了目标中的 log π 项。

事实证明，如果你真的计算一下数字，这个表达式实际上等于以 r - log π 作为奖励的常规策略梯度，而由梯度穿过 log π 增加的额外项，仅仅是 -1。为什么是 -1 呢？如果我们反向应用我们用来推导策略梯度的那个恒等式，它就是 -1。因为我们在括号外面有一个 grad log π，所以 -log π 的导数就是 grad log π * -1。所以 -1 就是这么来的，非常非常简单。但记住，如果你对奖励加上或减去任何量，策略梯度在期望上是相等的，所以那个 -1 实际上没有效果。这导致了一个令人惊讶的结论：如果你想做熵正则化策略梯度，你所要做的就是从奖励中减去 log π，而梯度表达式实际上没有改变。

好的，括号里的这个量，你可以把它看作是下一时间步的 Q 值，对吧？这是从 t+1 到结束的所有奖励减去所有 log π 的和。记住，在软最优性中，策略是由 Q-V 的指数给出的。所以我们可以用这个来代替 log π。我们到处用 Q-V 代替 log π，可以把这个表达式写成如下形式：grad Q - grad V * (r + next Q - current Q + current V)。但记住，因为基线的性质，括号内任何依赖于状态的函数都可以被移除。所以这个 V(st) 我们可以简单地去掉它。

现在记住 Q-learning 的目标是什么。Q-learning 的目标是 grad Q * (r + softmax(next Q) - current Q)。所以现在我们可以看到，策略梯度实际上很像软最优性框架下的 Q-learning 目标。主要区别在于策略梯度减去了 -grad V，而 Q-learning 目标有一个 softmax。所以 Q-learning 目标有这个离线策略修正，如果你有一个在线 Q-learning 方法，你可以省略这个。策略梯度有 -grad V 项，但除此之外，它们实际上非常相似。

好的，这可能只是关于这两种方法在软最优性框架下联系的一点花絮。但也许更实际的是，所有这些变分推理、控制即推理和软最优性这些东西有什么好处呢？

一个好处，至少在策略梯度的情况下，是它改善了探索，并防止了熵的过早崩溃，这对于策略梯度来说会极大地损害探索。当你最终得到更随机的策略时，为更具体的任务专门化或微调策略可能会更容易一些。事实证明，它们更适合在任务发生轻微变化时进行微调。这个方法提供了一种有原则的方法来打破平局。如果两个动作真的有完全相同的优势，它们也将有完全相同的概率，而不必担心如何取 argmax。这个方法也提供了更好的鲁棒性，因为你实现了对不同状态更好的覆盖。你可以直观地这样想：如果你学会了用多种可能的方式来解决任务，那么如果其中一种方式因为环境的变化而变得无效，你可能仍然有非零的成功机会。当然，这个框架在奖励幅度增加或者你引入一个温度并让那个温度趋于零时，会还原为经典的硬最优性。它也是一个很好的模型，用来建模人类行为，人类行为通常不是确定性的，人类确实倾向于犯错误。所以这基本上是说，你可以犯错误，但随着奖励的减少，错误的发生会呈指数级地变得不可能。

---

好了，在今天讲座的最后一部分，我将谈论一些先前的研究论文，它们实际上利用了这种变分推理或软最优性框架来实例化一些具有有趣属性的算法。

首先，我将谈论微调、探索和鲁棒性。假设我们运行一个策略梯度算法来训练这个人形机器人走路。我们可以让它走路，看起来会很酷，很有说服力。但如果我们两次运行相同的算法，我们可能会得到一个非常不同的步态。在第二次尝试中，人形机器人仍然在走路，但走的方式非常不同。事实上，我们在深度强化学习中一直看到这种情况，多次运行相同的算法不一定会得到两个相同的解。

直观地说，这个问题只是一个更复杂、更普遍的特定局部最优问题的版本。这最好用一个简单的例子来说明。假设你有这样一个环境，这个小蚂蚁机器人需要走到蓝色方块指示的位置。在学习的早期，它可以选择探索上面的通道或下面的通道，两者都让它更接近目标，所以它不知道哪个是更好的。但使用传统的强化学习算法，它可能会最终致力于其中一个通道。如果它随机地致力于错误的那个，它就会被卡住，无法到达目的地。直观地说，如果你想解决这样的问题，你必须跟踪两种假设，基本上探索两个通道，直到你发现其中一个明显比另一个好。

这就是软 Q-learning 实际上可以非常有效的地方。我们有我们的 Q 函数，它从状态和动作映射到连续值。在训练的早期，智能体在这里会看到，它在上面的通道和下面的通道都得到了更大的 Q 值。所以你可以把这张图看作是 Q 函数的一个粗略的卡通插图。在初始状态，它有两个峰值，一个对应上面的通道，一个对应下面的通道。哪个峰值更高，很大程度上是任意的，主要取决于智能体在每个通道上走了多远。所以如果它在上面的通道上走得更远一点，它会看起来好一点，因为它更接近目标。所以那个峰值可能会稍微高一点，当它稍微高一点时，智能体会将不成比例的探索精力投入到探索那个上面的通道，相应的峰值会变得更高。

如果我们根据这个变分推理框架来选择我们的策略，如果我们选择它与指数化的 Q 值成正比，我们就会把概率质量放在两个峰值上，实际上会探索两个通道。当然，正如我之前所说，这里的归一化器只是价值函数。所以它有一个吸引人的解释，即优势函数的指数。这直接导致了软 Q-learning 过程。事实证明，它有一个很好的吸引人的特性，它会探索两种假设，直到我们弄清楚哪一种是最好的。

事实证明，这种方法对于预训练非常有用。因为如果你在一个未指定任务的环境中使用它进行预训练，你将学会以各种不同的方式解决该任务。然后，当环境发生变化，你必须专门化你的技能时，你只需移除所有错误的解决方法，而无需重新学习。为了用一个例子来说明这一点，我录制这个视频是在 10 月 31 日，是万圣节，所以这非常应景，我们将看到一场蜘蛛的爆炸。在左边，你可以看到标准的，在这种情况下是 DDPG 确定性强化学习算法，其奖励函数是说“在任何方向上快速奔跑”。在右边，你可以看到软 Q-learning 的方法。

当软 Q-learning 方法被给予一个对于在任何方向上奔跑都有高奖励的奖励函数时，它会尝试在尽可能多的方向上奔跑，因为这会增加熵，并且还会制作一个非常适合万圣节的视频，就像这样可怕的蜘蛛爆炸。现在你可能会说，这有什么用？为什么我们想要在随机方向上奔跑的蚂蚁？这之所以有用，是因为如果你以这种方式预训练策略，然后把它放在像这个走廊这样的环境中，它必须微调以在单个特定方向上奔跑，用软 Q-learning 预训练的策略可以更快地进行微调。

最初，DDPG 策略以一个特定但错误的方向奔跑。软 Q-learning 策略在每一集都以一个随机的方向奔跑。经过一点微调，软 Q-learning 策略基本上需要学会不向错误的方向奔跑，只保留正确的方向。而 DDPG 策略必须先忘记如何向错误的方向奔跑，然后再重新学习如何向正确的方向奔跑，这意味着它的微调速度会慢得多。当然，我们在这里定量地看到了这一点，蓝线显示了从软 Q-learning 微调的结果，绿线显示了从确定性 DDPG 算法微调的结果。

除了为万圣节制作滑稽的蜘蛛爆炸和实现更好的微调之外，软最优性的框架也可以简单地导致性能更优、更有效的强化学习算法。事实上，当今最广泛使用的离策略连续控制算法之一，叫做软演员-评论家（Soft Actor-Critic, SAC），它就是基于软最优性原理的。

软演员-评论家本质上是软 Q-learning 的演员-评论家对应物。所以在软演员-评论家中，有一个 Q 函数更新，但它不是一个软最大值，它实际上是试图为一个给定的策略学习 Q 函数。你可以把这看作是在变分族中进行消息传递。它看起来和常规的演员-评论家 Q 函数更新完全一样，只是增加了这个 -log π 项来考虑熵。然后策略更新就像我之前展示的那个策略梯度目标一样，但使用的是这个 Q 函数。因为这个算法使用了一个 Q 函数，它可以从离策略数据中学习。然后当然，每次它更新 Q 函数和策略时，它都会与世界互动，并收集更多的数据添加到它的回放缓冲区中。所以你可以把第一步看作是，用强化学习的行话来说，是更新 Q 函数，但用变分推理的行话来说，是在对应于变分族的图模型中进行推理，以进行消息传递。然后对策略的更新，使变分分布更好地逼近原始图模型的近似后验。

所以，这个算法在变分推理方面的理由有些复杂，但实际的实例化其实非常简单。它是一个涉及 Q 函数的离策略算法，我们从未来的 Q 值中减去熵。这个算法被证明效果非常好。

这是一个索亚（Sawyer）机械臂学习乐高积木堆叠任务的视频，它实际上是直接在真实世界中学习的。这个实验的有趣之处不仅在于它学会了堆叠乐高积木，而且在于学习完成后，你实际上可以去扰动机器人。因为它通过那个熵项学会了以各种不同的方式来执行任务，它实际上保持了相当的鲁棒性。所以它可以对扰动做出反应，恢复过来，并且仍然能堆叠乐高积木。它在这个任务上非常非常鲁棒。

研究人员还进行了一些非常有趣的实验，使用这个算法来学习一些移动任务。这里这个叫做“牛头怪”（Minotaur）的机器人，它使用软演员-评论家直接在真实世界中学会了向前行走。最初它只是随机地移动，但经过相当多的训练后，它实际上可以找到一个相当不错的向前行走的步态。这是 5 倍速播放，但现在我们要快进一点。在 18 分钟时，你可以看到它有一种渐进的爬行，它能够向前移动，但只是一点点。在 36 分钟时，它有时会摔倒，但有时会向前移动得快得多，有点像蟑螂一样快速爬行。然后在 54 分钟时，它有了一个相当不错和可靠的步态。

当然，和以前一样，这个步态具有一定程度的鲁棒性。所以我们可以把它放在平地上，它可以展示它学到的东西。但我们也可以在它面前放一些障碍物，看看它如何反应。机器人当然没有在斜坡上训练过，但当它被放在斜坡上时，它实际上反应得相当智能。这里它在下楼梯，它不能上楼梯，但可以可靠地走下楼梯。它还可以玩叠叠乐（Jenga），但玩得很差。

好的，讲座到此结束。如果你有兴趣学习更多关于“控制即推理”和软最优性的内容，这里有一些建议阅读材料。我今天讨论的大部分材料都源自于一个关于所谓“线性可解马尔可夫决策问题”的先前研究体系。所以如果你对这个感兴趣，可以看看 Emmanuel Todorov 在这个主题上的一些工作。Todorov 的小组也开创了关于软最优性如何为人类运动控制提供合理解释的研究。Bert Kappen 是另一位在这个领域做了很多基础性工作的研究员。所以如果你感兴趣，可以看看论文《最优控制作为一个图模型推理问题》。当然，Brian Ziebart 是这个领域的先驱之一，特别是在将这个原则应用于逆强化学习方面，我们将在周三更多地讨论这个问题。关于这个主题的另一篇有趣的论文是 Rolex 等人的这篇论文，它使用类似的数学工具来开发一种迭代的演员-评论家式方法。然后当然还有更近期的论文，软 Q-learning，这是一篇也使用非常相似的框架来处理演员-评论家式方法的论文。这篇论文还讨论了策略梯度和 Q-learning 之间的关系。这是软演员-评论家的论文，它描述了现在最广泛使用的基于软最优性原理的离策略连续强化学习方法之一。然后，如果你想要一个教程或综述性的概述，我实际上在 2018 年写了一篇关于这个主题的教程，如果你想更全面地了解文献，可以去看看。