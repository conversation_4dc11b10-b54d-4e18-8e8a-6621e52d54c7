在伯克利，她从事
各种工作，广义上
涉及人机交互、机器人和
学习。我想是的，有些是
这三者的结合，有些是 ven 图表。今天她会
告诉我们什么是交互式学习，
对吧？非常感谢
Sergey 的介绍，也感谢
邀请我。我很高兴来上
这门课的最后一堂课。实际上，这是
我第一次来到这栋楼，
这栋楼很漂亮，我非常喜欢。嗯，
所以今天我想谈谈我们
实验室围绕
交互式学习这个概念所做的一些工作。Sergey 刚才
问什么是交互式学习。
当我想到交互式学习时，
我基本上会想到
从各种人类数据来源学习的想法。
你可以学习机器人策略，
可以学习奖励函数，
可以学习表示形式。但这个
想法是，有一个人
提供数据，你可以
与该人互动，或者你可以
从该人那里收集离线数据，你
可以尝试从中学习。
这是我们
实验室一直在思考的一个主题。 一段
时间以来，让我们从
这个想法开始讨论。我将讨论一下
我们在大型语言模型时代经历的一段旅程，
以及过去一年中这些事情发生了哪些
变化，以及
现在我们
有了大型语言模型（
视觉语言模型）之后，我们对交互式学习的看法发生了哪些变化，以及我们应该如何
看待这个问题。我知道我可能要到
6 个月才能讲完
这些，没关系，你可以
随时打断我，我们可以聊聊一些
我不需要讲完
所有
不喜欢的事情。好吧，让我们从我们
在实验室里非常感兴趣的一项任务开始，只是
为了激发一些我
认为在机器人技术中很困难的问题，嗯，
其中之一就是
辅助喂食的问题，所以我们一直在
研究拾取
食物的问题，并思考如何
以一种安全舒适的方式将食物送到人的嘴里，
嗯，
这是一个非常有趣的机器人
问题，因为你必须 拿起
食物之类的可变形的东西，嗯，
你还需要把
它传递给人，这样你就可以与
人机交互，你需要
确保这是安全和舒适的，
嗯，它开始于一种类似于
基线策略的视觉
伺服，所以它会计算出你的
嘴在哪里，它有一个摄像头，它
会计算出你的嘴在哪里，然后它会
试图移动到与你嘴巴一个固定的偏移量，
这非常
不舒服，看着这个视频
让我很难过，嗯，我们想要
做的是，我们想思考
如何
改进这些策略，
问题是，也许我们可以在这里使用某种
程度的学习，
也许我们可以尝试强化
学习，这是
你们在这门课上学习的内容之一，嗯，事实
证明，强化学习在
这里真的很难，因为首先我
没有一个好的模拟器，我
不知道它们是如何
与胡萝卜和
叉子互动的，而且如果我要
在现实中进行强化学习，我真的
不想打到人的鼻子，
比如得到负面奖励，那也不是
那么理想，嗯，所以
还有另一个极端，那就是
我们可以收集一些
数据，我们可以尝试进行模仿
学习，就像我们可以收集
一些专家数据，从这些
专家数据中，也许我可以找出
正确的方法，至少像转移食物的
想法的参数是什么，
这是我们一直在
研究的事情，但事实证明，
即使你正在研究数据
收集和进行模仿学习，
收集数据说实话也不是
那么容易，对吧，就像你开始
走这条路线，让我做超级智慧
学习，让我
从人类那里收集大量数据，就像人类一样，
你首先需要特定
类型的设备来收集数据，你
需要一个VR系统，这真的
很好，就像S和
Chelsea一直在研究的那个loha系统，对吧，B
手动设置，你可以
很好地远程操作东西，但
同样，你需要有像前臂这样的设备，
它们很便宜，我明白，但你仍然
需要一个完整的设置 嗯，
实际上就像收集这些数据一样，
这往往会在
数据收集方面施加一些限制，然后除此之外，
当你开始收集这些数据时，
还有一个问题，那就是这些数据
看起来怎么样，就像人类数据
往往与
脚本数据非常不同，如果你查看人类数据，
嗯，我们倾向于称之为次优，我不
知道次优是否一定是
人类数据的正确词，但
例如人类在演示中会有停顿，
你要做的第一件事就是删除所有
停顿，因为你想要获得干净的
数据，我认为一个很好的问题是，
为什么人类的
数据中有停顿，我认为一个
原因是当你观察人类
细胞运作并拿起一个
物品时，嗯，他们不仅仅是拿起
物品，他们还在思考
其他事情，关于晚餐的一切，
他们
在头脑中进行其他过程，其他认知过程，
所以你不能只是
限制他们拿起杯子，
嗯，这使得数据
可能不像 脚本化的
数据，人类数据中还存在其他类型的偏差
或次优性，
我们在
收集人类数据时经常看到这种情况，
并尝试从这些
数据中学习，所以在实践中，事实
证明，演示是
很好的数据，但它们并不是我们唯一
可以利用的东西，当然，
我们也在关注
演示，但我们
兴奋的是，也许我们可以利用
其他数据源，所以如果你
观察人类，他们往往会
随时泄露信息，或者还有
其他与人类互动的方式，
除了专家演示之外，我们
实际上可以研究和
学习，这些数据来源之一
是 Parise 比较，所以我的
意思是，与其
让我要求一个人告诉
机器人如何操作，不如展示两种
不同的轨迹，或者五种
不同的轨迹，我
实际上可以问一个人你的
偏好是什么，或者要求一个人
提供完整的排名，呃，从中
我可以了解到
它是什么 一个人实际上想要的，
我们一直在研究
从 par wise 比较中学习的想法，
但也有
其他数据来源，比如如果你有一个
机器人，它确实有一个化身，你可以
物理地移动它，当你
物理移动它时，会有
关于那个动作的信息，
你可以从那个动作中实际学到什么，
还有我提到的次
优性，有各种
处理次优性的方法，所以我相信
在这节课里你已经看过离线或
类似的东西，这可能是一种
从次优
演示中学习的方法，但你也可以采取
类似模仿学习类型的方法，
考虑加权方法来
去除
IM 的视角，然后
对次优演示进行模仿学习，
这也是一种非常好的方法，
所以我们一直在
研究所有这些不同的
领域，但在演讲的第一部分，
我想主要谈谈
从 Parise 比较中学习的想法，然后
我想谈谈 切换到
这种类似于我们现在所拥有的事实，
我们现在生活在
大型语言模型的世界中，以及事物
在这种环境中是如何演变的，所以让我
首先开始讨论交互式学习，从偏好中学习，
然后我们可以讨论语言模型，
那么当
你试图
从周围环境的比较中学习人类的偏好时，这意味着什么呢？所以在实践中，
当我们向人们提出这类问题时，
我们可以试着
弄清楚他们在W中是如何行动的，比如建立一个
人的模型，但你也可以
试着弄清楚人们希望
机器人在环境中如何行动，
例如，一个人
希望自动驾驶汽车如何驾驶，或者
一个人希望机器人如何
打开抽屉，或者呃，也许像
外骨骼，关于外骨骼如何
帮助你行走或做
各种事情，以及问题是什么，从
这些类比比较中得出，所以在这项
工作中，我们正在研究的是，
我们将奖励函数视为一种
表示，你实际上可以
从周围环境的比较中学习到它们，它们似乎
是一种不错的紧凑表示，
你可以 学习，然后
你可以优化，你会发现
强化学习，这往往
是一种
学习人类模型的强大方法。我想
指出的是，奖励函数
并不是你唯一可以学习的东西，
实际上，Brad
Nox 团队最近在研究
优势函数，而且似乎
人类的偏好，比如问这些
巴黎比较，实际上更能
代表优势函数，而
不是奖励函数，嗯，但你
可以继续学习，
比如说奖励函数。对于第一
部分，让我们假设我们正在尝试
学习奖励函数，
好的，那么设置是，嗯，不确定
战争
温度，嗯，所以设置是，我
将向一个人展示两种不同的轨迹，
或者和不同的
轨迹，然后我会问一个
人，你更喜欢什么，然后
根据这个人的回答，他
告诉我，他喜欢一胜过二，或者
二胜过一，那么这将给
我一些关于底层
奖励函数的信息。想象一下，
这个奖励函数很简单，它
不需要这么简单，但
想象一下 奖励函数
只是一组
非线性特征的线性组合，一些 W 乘以一些
费用集，一些向量集，
W 向量乘以一些
特征集，所以例如你可以想象
W 位于三维
空间中，对吧 W1 W2 W3，然后你
可以想象你的 W
位于单位 B 上，因为你
关心的是在你的报告函数中
这些不同特征之间的关系，
你可以从
这个单位球中抽样，你的真实 W 就在
这里的某个地方，好的，那么
你向一个人提出的每一个问题每一个查询，
你喜欢一个还是你喜欢对应
于这个空间中的分离超平面
对应于 w 的平面。  Fe
等于零，这是
轨迹一和轨迹二的特征之间的差异，
好的，而人类告诉我，我
喜欢 A 胜过 B，或者我喜欢 B 胜过 a，这
基本上告诉我
超平面的哪一侧更好，我喜欢
超平面的右侧，或者我喜欢
超平面的左侧，从
这个回答中我可以做的是，我可以
意识到真正的 W
位于超平面的右侧，
而不是
超平面的左侧，所以在实践中，从
我向一个人提出的一个问题来看，我
可以在我的搜索空间中有点像 T，
我几乎可以删除
超平面错误一侧的所有内容，但
我不会这样做，因为人类
很吵，所以我实际上要
使用一个 Boltman 理性模型，
假设人类很吵，
所以我要重新采样我的
点，并对超平面右侧的 W 赋予更高的权重，
因为那个人告诉我他们
喜欢 A 胜过 B，这告诉我真正的 W 在
某个地方，好的，所以这是一个
问题，所以很有趣
这里的研究问题是，
我
可以向一个人提出一系列信息丰富的问题，以便我
能够快速收敛到
真正的奖励函数，这
听起来很像一个主动学习问题，
就像
我们在 Wayback 的机器学习中看到的相同的主动学习问题一样，就像
它
看起来就像是一个非常
类似的问题，你可能
在电影推荐中看到过这个问题，
比如如果你看看
2007 年的 Netflix 挑战赛，或者
他们也在
看一个非常类似的问题，
比如我可以问你哪两部电影，我
可以问你的偏好，
这样我就能快速找出你的偏好，
那就是电影
偏好，这是关于你的奖励
偏好，关于机器人应该如何完成
一项任务，它可以是一个非常实用的
东西，比如我如何打开抽屉，也
可以是一个非常风格化的东西，比如它
可能是关于你在
试图打开抽屉时是否要温柔的偏好，所以
这里
我们正在定义奖励
函数实际上代表什么，所以
当我说人类偏好时 我的意思
是，真正的奖励函数，一个
功能性奖励函数，你
可以去优化它，然后为
你的机器人制定策略，并根据
这个
策略执行。那么，如果
这只是你的
建议，那么应该很容易解决，对吧？
我们应该运行
通常的主动学习方法，
这通常应该有效，
在某种程度上确实如此。我认为
有很多挑战
值得一提，那就是我们
试图将这个想法带到
机器人领域，我们研究的是
连续空间，而不是像我有一个
电影库可以
从中取出，我没有一个
轨迹库可以从中取出，我
想在这里做的是，我想
主动提出问题，我想
主动综合两个
问题，然后问一个人
你更喜欢哪一个，这似乎
比
仅仅优化信息要复杂一些，
当我只有一个电影库时，
我没有电影库可以
从中取出，嗯，我只有
一张幻灯片 我把
这个问题简化得有点过了，嗯，ERM 是
现在这个小组的一名博士生，他的论文
基本上就是关于这个的，我用一张幻灯片总结了他的
论文，但基本上
我们想要
做的是，你想找到一组
轨迹，比如两条
你想生成的轨迹，
这里的 feed 对应这两条
轨迹，Fe 是
这两条轨迹的特征向量之间的差异，
我们想要做的是，
我们想找到能够
收集信息的轨迹，所以我们
要优化的是某种
信息理论指标，然后
我们探索
可以使用的信息理论指标的数量，
信息增益，你可以使用
行列式点过程来
衡量多样性，或者在这种
情况下，我们真正要优化的是
提出这个问题后从假设空间中移除的体积，
因为当我问一个关于
超平面的问题时，你会
根据这个答案来回答，我会
从假设中删除一些
真实 W 上的值，而我
我想做的是，找到一个
信息丰富的问题，这个问题会
从假设空间中移除大量的体积，
而这个目标
只是说，这个目标
就是最大化这个体积，
那个体积是多少？我说我们正在看一个
单位球，所以单位球的体积
是1，如果你告诉我你更喜欢A而不是B，
或者如果你告诉我你更喜欢e而不是a，无论
哪种情况，都会有一些
体积从
假设空间中移除，这是基于一些嘈杂
的
非理性人为更新函数，无论哪种情况，就像
预期的体积一样，无论哪种情况，
我都会
最小化这个体积，也就是
移除的最小体积，我最大化的是
假设SP中移除的最小体积，好的，这
有点像
我们正在优化的信息度量，
这是一个模块化目标，所以你
可以使用与
模块化优化方法通常
使用的相同类型的理论，然后你可以
在这里得到一些收敛结果，因为正因为
如此，但还有一件事
我想 需要再次强调的是，我们
是在机器人环境中，所以这不是
约束优化，在这种情况下，
我们研究的机器人需要
满足动力学等条件。由于
我正在生成这些轨迹，所以我正在
从连续空间合成这些轨迹，
我需要确保
这些轨迹满足一系列
约束条件，所以我需要
真正解决约束
优化问题，才能生成下一个
最具信息量的问题。
好吧，这在某种程度上是
我想在这里深入探讨的数学问题，
但再次将其简化到
某种程度，但如果你在非常
简单的环境中这样做，你将能够
学习一些
简单任务的奖励函数，例如
拥有一个驾驶模拟器，在零个
问题之后，你真的不知道
该做什么，但在 30 个问题之后，你
就会学会如何驾驶，然后你会
学习如何保持前进，
最后，在 70 个问题之后，
我们驾驶的橙色
汽车通常会学习如何
避免串谋和驾驶 比如
障碍物之类的东西，我
觉得这很有趣，因为这
是 70 个二进制问题，我没有收集
任何演示，我只是问了 70 个
二进制对问题，
从中我能够学到
一些我很难
调整自己的东西，H 让这个
自动驾驶汽车在这个
非常简单的驾驶
模拟器中驾驶，我想在
这里提到的另一点是，最简单的形式
是线性奖励，所以
有点像这里的常见抱怨
是，哦，那是一个线性
奖励，最终我有一个
神经报告，我不会写一个
非常公平的线性奖励函数，
就像在实践中，我们将
编写非线性奖励函数神经
报告函数，在
那种工作的扩展中，我们一直在
研究这种主动学习的想法，
你会失去很多类似的
理论和数学，但你
仍然可以针对最
不确定的问题集进行优化，
仍然尝试了解奖励
函数是什么，
我实际研究的设置之一是
就像你穿着
外骨骼，试图学习
人类的偏好，比如人类
穿着
外骨骼时的行走偏好，这是
与 calc 的同事合作的，通常
人们在
康复期间，当他们
试图学习如何再次行走时，会佩戴这些外骨骼，
不同的人有不同的行走
偏好，所以你想要做的就是
询问人们，弄清楚
这些门控偏好是什么，这样你就
可以快速地收敛，
并了解如何帮助这个
人在这些环境中行走。
我想
提到的另一点是，每次我
向机器学习人群谈论主动学习时，
通常都会有人持怀疑态度，
这是一种合理的怀疑，因为
如果你看一下主动学习，任何一篇
主动学习论文都会有两辆车，
看起来像一个是
主动学习，另一个是随机
抽样，它们看起来很接近，
最终会收敛，
主动学习是否值得，我 我认为
这是一个非常公平的问题，即
主动学习是否值得。我认为
在很多机器学习
环境中，主动学习实际上并不值得。
随机抽样
更简单，而且效果很好。
你有电脑，可以
运行随机的 sampo，但我认为
在机器人领域，
这两种方法之间的区别
实际上很重要。这种区别在于，
在系统中待三个
小时和半个小时之间的区别。如果你在
运行用户测试，如果你在
用
真正的机器人系统与真人互动，那么
三个小时和半个小时之间的区别
确实很重要。所以我认为
在机器人领域和
与人类互动方面，主动学习还有很大的发展空间。因为
那时时间
复杂度和样本复杂度开始比
很多
机器学习
设置更重要。嗯，我们已经
讨论了学习人类
偏好和提问的想法，
比较
来自单个人类的问题，总的来说，
我认为这是 就像一个
可以在机器人技术中使用的想法，但是
有趣的是，它也适用于
机器人技术之外，对吧，就像它
不必在机器人技术中一样，所以
我们决定在谈判领域的不同领域使用一个非常相似的想法，
所以这是一个谈判
领域，我们有很多项目，所以
我们有一本书，两顶帽子，两个球，
一堆共享项目，然后我们的想法
是我们有Alice和Bob，
Alice和Bob正在尝试就
这里的共享项目进行谈判，好的，Alice
和Bob各自都有自己的
效用，所以他们可以看到自己的
效用
嗯，这里的想法是Bob可以
进来，我们有一堆类似的
行动项目，所以提出或接受
提案或拒绝
提案等等，Bob进来
并根据我的效用说，好吧，
我将提出什么建议，然后拿零
本书，两顶帽子和两个
球，问题是Alice应该做什么，
所以我可以为Alice构建一个AI代理吗？
我可以为Alice构建一个AI代理，
以便Alice实际上可以与B进行谈判，
我应该做什么
建议
那肯定是问Al的，所以你
告诉我策略是什么，对吧，所以
你说好的，对Alice来说好的策略是要
一本书，对吧，就像
Alice说的，你是怎么想出
这个
策略的，你们在这节课上做什么，
所以我用奖励函数，然后
你用这个奖励函数做什么，
对吧，所以你可以有
奖励函数，试着
推断奖励函数，但是Alice知道
她的奖励函数就在这里，
Alice可以访问她的实用程序来获得
奖励函数，如果我
可以访问我的奖励函数，我
可以优化它，我可以运行R，
就像我可以用它来做强化
学习，我猜Alice
可以访问Alice不能访问
B，所以我建议
嗯，所以你可以
探索，然后采取行动，
试着探索Bob是什么，
Bob实际上是如何回应的，
你可以采用基于模型的方法，
尝试真正学习Bob的奖励
函数并使用它，你也可以
采取一种更无模型的
方法，然后提出
一些建议 而且你
知道你自己的奖励函数，但你会
采取探索性行动，看看Bob
对此有何反应，然后基于此
决定该怎么做，好吧，这
完全没问题，就是这样，所以
这是一种非常好的做法，
所以你可以像那样进行强化
学习，那就是
游戏，你也可以解决游戏，
就像你可以采取博弈论的
方法，你知道你的效用，你可以用强化学习来
解决
强化线问题，
强化
学习的问题是，如果你
在这种环境下进行强化学习，
你的Alice代理会有点
过于激进，你的Alice代理
会坚持要得到同样的
东西，Bing会纠缠不休，有点
咄咄逼人，而
强化学习的激进是公平的，
因为在
奖励函数中，我说过，当你
试图这样做的时候，也许要尽量礼貌一些，
或者尽量公平一些，或者不要太
激进，这听起来不太像人类，
所以有很多事情，
有很多目标，
就像我从未说过的那样，这是其中的一部分
奖励函数，Alice 没有理由
为此进行优化，好吧，
还有另一个极端，那就是
我可以收集一个数据集，然后我可以
进行监督学习，我可以进行
模仿学习，然后
实际上就像这种游戏
来自一篇名为 Deal or No
Deal 的论文，论文附带一个数据集，
你可以实际使用
该数据集进行训练，结果发现
由于某种原因，该数据集
非常好，所以监督
学习代理处于另一个极端，
非常赞同，所以
你的 Alice 最终只是同意
Bob 所说的一切，这也不是
很人性化，所以在某种意义上，
这有点像通常的价值
取向问题，这通常就像
通常的奖励设计问题，我实际上无法访问真实奖励函数，
这个实用程序并没有真正
捕捉到
我们真正追求的真实奖励函数，而
获得它的一种方法是主动
学习，所以正是
我之前提到的算法，你可以
应用该算法并尝试识别
新场景，然后从这些新
场景中，你可以咨询专家，比如
你会如何行动 在这种情况下，
尝试找到一个更好的
强化学习代理版本，使其能够
更好地了解
你实际
追求的一些属性，这是一种非常好的
做事方式，实际上
就像 May 所说的那样，他们在 2021 年的 IC 上做过这样的事情，
他们采用了一种
有针对性的数据指控方法，
你可以主动提问，最终得到
一个
比强化学习更好的谈判代理，再加上
启发式学习，可以捕捉
人类的
偏好，但有趣的是，
你可以做其他事情，而不是
问一个人，而不是像
从一个人那里进行主动学习，
嗯，你可以做的另一件事
是，你可以从技术上对一个大型
语言模型进行量化，然后询问
它的
想法，并将
大型语言模型视为此任务的代理奖励
函数，在
我们
考虑这个问题的时候，我们非常
担心这个想法，因为
使用
LLM 作为奖励函数听起来有点疯狂，但实际上
从那时起，有很多
作品使用 LLM 和 BLM 作为
奖励 函数或 Su 或成功
检测器，这在很大程度上
也是在尝试在谈判游戏中做同样的事情，
就像在这个
谈判游戏中一样，因为它是一个文本
空间游戏，我有很多关于
谈判和文本的信息，因此
我可以做的一件有趣的事情是，我可以简单地问
一个法学硕士，这个谈判可以吗？对
吧？我不需要让法学硕士帮
我写下“函数”这个词，但
我可以请法学硕士评估一下，这样可以吗？
这样礼貌吗？这样公平吗？
我们实际这样做的方式是
创建一个提示，假设
Alice 和 Bob 正在谈判，假设
要分割一套书、帽子和
球，然后我们要做的是举
个例子，所以
我们有 Alison Bob 进行谈判，
也许你在这里考虑的是灵活性的属性，
所以我们说这是
一次灵活的谈判，然后
我们有一个策略，我们的初始策略
是一个随机策略，我们将向
随机策略推出 策略，然后我们
将使用该策略与
其他代理一起玩，最后
你会问，这个策略是否
灵活，Alice 在最后一个 POS 中是否灵活，
提示看起来是这样的，所以
你会问一个 LLMS，
你觉得怎么样，从
LLMS 的输出中，我们可以得到很多
概率，或者我们可以简单地得到“是”或“否”的
答案，但这个输出是
关于报告功能的信号，
对吧，它不是为我写下报告
功能，而是一些
它可以实际使用的信号，我可以使用
它，并根据该信号进一步训练我的
代理，一旦我
接收到该信号，我就可以使用
该信号训练一个强化学习代理，
继续训练它，
生成一个新的策略，然后去更改
这个提示的粉色部分，
再次调用 LLMS，并完成这个
l，所以在某种意义上，这与
RLHS 相反，因为我通过在强化学习的
训练循环中调用 LLMS 来训练 RL 代理，
通过这样做，我得到了
信号 获得类似于
奖励正则化器或fullon报告
函数的功能，您可以以不同的方式使用它，
您可以用它来塑造您的Q
函数，也可以使用它
直接像VI一样塑造您的报告函数，
但无论哪种方式，它实际上
就像是一种奖励塑造
策略，让您的策略实际上
做同样的事情，事实证明，
在这种谈判环境中，它
实际上非常有效，所以我们
研究了许多属性，例如
多功能性，容易被说服，有
竞争力，固执等等，在
所有这些属性中，
我们都可以访问
ground truth奖励，我们可以做的
是，我们可以证明使用
大型语言模型可以充当一个不错的
代理，它实际上
在这些
不同的设置中与ground truth奖励相匹配，并且它的表现优于
监督学习基线，
老实说，这不是一个公平的基线，
因为您会为
监督学习代理提供多少数据，
但我认为更重要的一点是，
它实际上非常接近
ground truth奖励，并且在
我们无法访问ground
truth报告的环境中，我们可以进行用户研究，
我们可以问用户，
例如，这个使用 LM 作为
代理奖励的代理是否匹配 你的
期望，事实证明，
一般来说，人们喜欢呃，而且
你认为它符合
他们实际追求的正确风格，好的，
所以这一切都很棒，这
是一个谈判环境，有点
期望LLM擅长
谈判和评估谈判，这
对机器人技术意味着什么，
就像这告诉我如何将
LLM用于机器人技术，这听起来
有点值得怀疑，当我们
考虑这个问题时，我认为
其中很大一部分是接地问题，对，
有一个问题，是的，抱歉
打断一下，嗯，但是
你
使用的L本身是否涉及HF，它
不是，是的，实际上这是我们
和我最喜欢的，所以我们实际上
玩过像gptj和gpt2这样的，像
一些早期的，是的，是的，
假设不是，但是
他们今天确实像你一样
使用像调整模型而
不是你需要担心
复合
因素，
嗯，是的，所以回到机器人
设置，有一个问题，就像做
同样的想法作为转移 进入
机器人环境，还有许多
类似的后续工作，这项工作实际上
是由 Google 的同事
开展的，我们正在
研究一个非常类似的想法，即
使用大型语言
模型作为奖励设计器，
这里的想法是，你可以从
语言指令开始，使用非常
高级的语言指令，所以这与
公平性或多功能性或
谈判的属性无关，而是从
一种非常
高级的语义上有意义的
语言开始，比如在
傍晚时分，让机器人
面向日落，然后你可以从
这个开始，让你的大型
语言模型输出
奖励函数的权重，然后你就可以去
优化这个奖励，所以在这项工作中，
他们不是在进行强化学习，而是
在进行模型预测控制，使用
并行模型预测控制，
但事实证明，通过一定程度的
prom 调整，这很重要，但通过
一定程度的 prom 调整，你
实际上可以让机器人做出这些类型的
行为，或者你可以让它
像狗一样坐下，或者抬起前爪
我最喜欢的例子之一是月球
漫步，你可以告诉机器人做月球
漫步，它实际上可以
生成与
月球漫步相对应的权重，我认为这
实际上非常令人印象深刻，嗯，
是的，你可以
在模拟中查看许多其他任务，但
我想在这里提到的一点
是基础，就像很多
这些都是在模拟中，你
可以非常真实地访问这个
信息状态，
或者像方形的例子，它没有
与世界互动，对吧，它
只是移动它的爪子，所以它要
简单得多，但是当涉及到
实际进行正确的状态估计
和与世界互动时，
实际使用这些模型来
输出正确的奖励函数或
使用具有
正确奖励函数的视觉语言模型变得更加困难，我认为
即使在过去的几个月里，也
有很多工作在
使用VMS作为
成功检测器的奖励函数，但
根据我们的经验，通常很难使用
这些模型获得可靠的结果 它们正在变得越来越好，这很
令人兴奋，但总的来说，
我认为目前
人们
能从它们那里得到的结果有点值得怀疑，所以我只是
总结一下这里的一些关键要点，
到目前为止，我所谈论的是
学习人类偏好的想法，
比如奖励函数，其中一种方法，
也许一种更传统的
方式是主动向
人类提问，这是一种
利用人类
反馈信息的方式，嗯，除此之外，
这并不是你
真正利用
大型语言模型知识的唯一方法，并
尝试利用这些知识，呃，
尝试通过
询问法学硕士或要求虚拟机给
你一些反馈和
信息来了解人类的偏好，很酷，
所以我确实有一个简短的部分，我正在
考虑跳过，老实说，所以我要这样做，
嗯，让我这样做，因为我
认为那样可能
更容易，让
我快速跳过这一部分，有点像
回到
这里，希望这没有搞乱Zoom
或
任何东西，哦，我可以重置屏幕共享，
是的，
是的，分享
屏幕，
好的，好的，所以我们讨论了
学习人类偏好的想法，这
很棒，这
有助于我们利用
演示之外的其他数据源，就像我们可以，是的，
哦，是演示者视图
停止
共享，嗯，共享
屏幕在哪里，这是桌面2，那是桌面，是的，好的，很好，
完美，好的，就是这样，是的，嗯，
所以我
说的是我们可以打包
到
演示中的偏好，但我认为
我们
在最后一节开始看到的另一个有趣的数据源
是，我们可以
利用大型免费训练模型，我们可以
利用LMS，这往往也是关于
人们想要什么的有用信息来源，在
实践中，如果你看看
过去几年，我想
你们都意识到大型语言
模型现在很流行，我想一个
很好的问题是，这
对机器人技术意味着什么，所以，在
知道这一点的情况下，我们将如何继续前进，
嗯，我认为在实践中，
我觉得有两种
对此，我想谈谈这两种
看法。第一种看法是，我有一个
宏伟的愿景，即为
机器人技术构建一个类似于大型语言模型的东西。我
记得在
gp3 出现的时候，我们都在
思考如何正确
使用 gpt3，也有
一些
在机器人技术中直接使用 gpt3 的方法，但这些方法似乎
并不那么令人兴奋，真正
令人兴奋的是，在机器人技术中，与之类似的东西是什么？
我
认为这是一个非常宏伟的愿景，
尝试实现它是一件很棒的事情。那么，在
关键问题上，我们的
想法是什么呢？不是利用
偏好查询或演示，而是
我们能否利用大型
离线数据集，就像这样，
它的承诺是，存在着
大型离线数据集，
我们可以从这些数据集中尝试
训练一个模型，预先训练一个模型，然后利用
这些信息，
并能够在下游设置中使用它，
那将非常棒。
基础模型
存在一个问题，那就是
正确的看待方式是什么，所以如果
你看一下基础模型
论文，嗯，这个想法是，你有许多
不同的数据源，也许你有
一些机器人交互数据，
我们有人类视频自然语言
模拟，我认为一个有趣的
问题是，
如果我们有这些数据，那这很重要，
但如果我们有这些数据，那么
我们正在进行预训练的是什么，比如
我们应该
从这些数据中获得什么正确的表示，
松木调整是什么样的，适应是什么样的，
我该如何使用
该模型进行下游任务，就像
如果你看一下LM，
你可以将它用于许多
不同的下游
任务，语言的下游任务，还有一个
问题是，我们是否有许多
不同的机器人下游任务，
或者它只是模仿，
它只是控制，我确实认为
有很多有趣的
下游任务和机器人技术，我们
应该思考如何
在下游设置中使用这些模型，以及
我们如何考虑精细
操作，所以我们开始思考
这个问题 我们开始真正
从学习视觉表征的角度来研究这个问题，
部分原因是我们
最初没有其他资源，比如我们最初只有
人类机器人视频可以利用，
而且有
很多人在尝试
收集大量机器人数据，并
在机器人数据上训练模型，但
目前我们只有人类
视频，比如 YouTube 视频，那么
问题就在于我们能否学习
有用的视觉表征。
如果你考虑视觉
表征学习，比如 rep，
目前有两种极端情况，
如果你看一下
视野，从视野中我们可以看到，
光谱的两端是像
mask 自动编码这样的东西，你拍摄一张
图片，然后把它遮罩起来，然后尝试
重现被遮罩的图像，这
真的很棒，因为它能给你提供一些
局部空间
特征，如果
你想要掌握一个物体，这
真的很好，因为它实际上能给
你提供物体的所有细节，
你实际上希望
用这个模型来掌握它，它能给
你提供语法 你
真正想要的是什么，但
像 Mascato 编码这样的问题在于，
它们会破坏所有的语义。
假设你想拿起一罐
橙汁和一罐
牛奶，它们都是
从一罐液体中倒出的 P，你应该
执行相同的任务，但你
无法真正
获得那种相似性，因为像素
看起来
不同。然后，我们有
另一个极端，
比如 clip 或 r3m 这样的模型，我们
真正试图捕捉语义，你
使用对比目标来捕捉
语义，我们试图将语言
与图像匹配，这些都很棒，
因为它们给了我们可
推广的概念，但
对比目标实际上会
破坏所有的局部和
空间特征，所以很难
指望 clip 表示能够像
飞粒抓取那样，
它们实际上无法
捕捉到任何这些特征。
所以我们真正想要的是，我们能否
兼顾这两个角色，
我们能否尝试学习一种
可以真正
尝试的视觉表示？ 连接语法和语义，
我们的想法是，也许我们可以
用语言作为语法
和语义之间的桥梁，这样，我们就可以进行基于基础的
重建，而不是仅仅进行没有任何语言的重建，或者
仅仅通过
生成语言的字幕。我们可以做的是，
我们可以从大量的倾斜主干开始，
但我们可以以
语言为条件，这样我们就不会丢失语义。
这是
这个模型背后的一个关键思想，
他们开始在人类视频上进行训练，
除了语法
和语义之外，我们还需要
捕捉上下文和
语用学之类的东西，就像
你看一个机器人任务，里面有
相当多的交互，然后是
动态交互，
我们也需要捕捉这些动态交互。
所以，
除了
语法和语义之外，我们该如何捕捉动态和语用学？这些有点像
构建这个名为
Waltron 的模型的三个关键因素，它是一个语言驱动的
表征学习模型，是
与许多人合作的结果，
有点像主要的
领导这项工作的人，然后
Ultron 模型的想法是从
吉祥物编码开始的，对吧，
做吉祥物编码很棒，对吧，这
给了我们所有你
真正想要的细节，从吉祥物
编码主干开始，然后
除此之外，尝试做语言
字幕，所以你可以有
一个关于
用柱子把胡萝卜堆起来的图像，尝试
在这个图像上做语言字幕，这样你就有了
剥胡萝卜作为
标题，并尝试同时获得
语法和语义，但为了获得
这种语用学，额外的想法
是你还可以生成语言，
所以如果你进行语言生成，
尝试去
理解这项任务的真正含义，那就是
用削皮器拉一辆马车，
然后如果你进行多帧调节，
比如你进行的两帧调节，也会
获得一些动态
信息，所以把所有这些部分
放在一起，你就可以训练一个
大规模的模型，比如人类视频，
而且这个模型会比
通常的模型更扎实
怀疑你会在这个
领域有所建树，然后你实际上可以
在许多下游测试中找到调整该模型的方法。
我想我们之前做过
评估，所以有五个
不同的测试，嗯，然后我在
这里展示其中几个。
人们最终关心的
通常是控制和模仿
学习。在这种情况下，你关注的是
语言条件模仿学习。
这些是你真正想要完成的任务。
嗯，这里有一个机器人可以完成
这些任务。性能很低。
你看不到它的视频的原因是，
所有这些模型的性能都很低。但
基本上，训练就像再次采用这种表示
形式，这是一种视觉表示
形式。采用这种表示
形式，并在 20 次演示中进行微调。
你最终会得到这个
Fullron 模型，它的不同版本
以橙色显示，其性能往往优于
RM 或 mask
Visual 预训练等方法。我认为，这样做的
原因是它
更扎实，它试图
更多地捕捉语义和语用学等内容。 与
现有
模型相比，我认为
我们在 Win 模型上得到的另一个有趣的结果
是，如果你
给它一段视频，比如
一个人打开
水龙头的视频，你只需查看表示输出，你就
可以实现
零镜头意图 INF frence，就像
模型实际上是非常
一致的，就像如果你看一下
表示，它在
确定水龙头何时
打开方面非常一致，所以它实际上可以从这个视频中
找出这个人的意图，
就像零镜头，没有任何任何
微调，我认为
这是非常有趣的，
更有趣的是，如果你
给它一个机器人视频，即使它
没有看过任何机器人，像这样的只是
在人类视频上训练，它能够做
类似的事情，它能够对
机器人视频进行零镜头意图推断，而
无需任何微调和
任何这种形式，我认为这
非常令人兴奋，因为这再次表明该
模型在
我们实际
追求的目标方面更加扎实，所以这是开源的，你
可以用 pip 安装 waltron 机器人技术，
无论您在哪里调用 resonet，都
可以调用 waltron，请使用它，让我们
知道进展如何，但是我认为
这部分的重点
是，当您训练这些大型
模型时，我们
在这里训练的东西是可视化
表示，就像我们试图
通过训练来利用大型离线数据集一样。
我认为
我们可以谨慎对待
预训练目标，我们可以尝试
塑造
预训练目标，因此它实际上
对我们感兴趣的下游机器人技术很有用，
在这种情况下，我们将语言
和多帧条件视为
将语法
语义和任务动态结合
在一起的关键差异，以便这些表示
对机器人技术有用，就像
建立在我提到的那个想法之上，
我们没有机器人数据，就像
我们研究人类
视频的原因一样，我们没有机器人数据，
我对在
多个实验室中
收集大型离线数据集机器人
数据方面正在进行的所有这些不同努力感到非常兴奋 您可以在这些集合
上训练这些预训练模型，
所以这是 RTX 的努力，
Sergey 和伯克利的同事，还有谷
歌的同事，以及许多实验室
一直在为此做出贡献，
这里的想法实际上是
尝试训练一个跨实施
模型，该模型针对具有许多
不同实施、许多不同
技能、许多不同数据集的机器人进行训练，并拥有
一个可以真正
获取所有这些不同种类的
机器人数据的单一模型，然后充当
基础模型，然后有一个
问题，它应该输出什么，
就像我在 Voltron 中展示的那样，
它是一个视觉表示，它不是一个
动作，但这个 RTX 模型
可以输出一个动作，它可以是一个
视觉语言动作模型，我
认为这是一个有趣的问题，
就像
我们在训练
预训练这些模型时想要达到什么抽象级别，动作是
正确的表示，视觉表示是
正确的表示，
我们应该如何去做，以及在此
基础上进行构建，
还有一些努力，这是
R2D2 的努力再次体现了
多个实验室的合作，包括 Sergey 实验室、
Chelsea 实验室和斯坦福大学以外的许多实验室。
我们试图
在同一个平台上收集数据，
但数据种类繁多，比如
野外数据，比如
学生宿舍，这是
斯坦福大学宿舍之一。我认为这些数据在
数据集中非常常见，但实际上你可以
拿到机器人，像在野外一样远程操作它，
然后尝试根据收集到的这种类型的数据训练模型。
我认为，
当我们考虑训练这些
模型时，这也是非常令人兴奋的。好的，这是我的第一次
尝试，这有点像，这又是一个
非常活跃的研究领域，每个人都
对这些基础
模型很感兴趣。我知道我还有五分钟时间，但我
想在最后五分钟，我想
简单谈谈第二次尝试，
第二次尝试是我
最初非常怀疑的，让
我告诉你第二次尝试是什么。
接受是可以的，就像基础
模型存在一样，我不想去
构建一个听起来非常困难的机器人基础模型，
LLMS 和 VM
存在，并且有一个问题，我是否可以
以创造性的方式将它们用于机器人技术，
因此，我们的想法是，与其利用
偏好查询和
演示，不如利用
大型离线数据集，例如发现的
机器人基础模型，
我可以利用现有的大型
语言模型和视觉语言
模型的知识，而我
最初对此并不那么兴奋的原因是，
我可以用 LLMS 做什么，
但最初的努力是
使用大型语言模型作为任务
计划，就像如果你看看像
Sayan 这样的作品，就像最初我
就像好的说可以做什么，Sayan
试图说这是谷歌人的工作，
它试图做的是
尝试使用大型语言模型来制定
任务计划和
其他一些事情，但像撒旦的关键部分
是它正在提出任务
线，而
我和许多人的最初反应是，
好的，机器人技术中的问题
就像 我们真的很喜欢任务
规划，我不认为那是我
做的 我认为情况并非如此，但我也不
认为这是 Sayan 的重点，
而且随着时间的推移，我对
使用 LLM 和 BLM 的想法越来越持怀疑态度，我认为
思考大型语言模型和
视觉语言模型会
开辟出许多其他
思考机器人技术的方式，这些方式我们
以前不会考虑，例如，如果您
看一下像代码 AS 策略这样的工作原理，
您会使用大型语言
模型来生成机器人代码，但两年前我
并没有将其视为
扩展机器人学习的方法，
但现在我想知道这是否是
正确的方法，而且我确实认为，将
大型
模型用于各种下游
任务的做法，开辟了许多
有趣的下游事物，我们
可以实际研究，而这
正是我们在过去一年中
一直在研究的东西，我不会对
其中任何一个进行太详细的介绍，也许
只是简单地介绍一下，但只是为了
指出其中的一些，我们
已经讨论了奖励设计，  LMS 作为
奖励设计器，就像使用 DMS 作为
奖励设计器一样，我认为这是对
现有 LMS 或 VMS 的一个非常有趣的用途，
您还可以微调这些大型
语言模型和视觉语言
模型，使其更符合您的要求，
因此，我们正在研究的一些工作
有点像
微调 VL 灯，使其更加
物理接地或
空间接地，然后这是一种
使它们
在奖励函数方面更加一致的方法，
您实际上可以将它们用于诸如
常识推理之类的事情，例如，
您可以问 BLM，
我应该清理
右边的乐高积木还是应该清理
左边的乐高积木，例如，如果您
有这样的场景，人类会
立即知道您不应该
清理这张桌子，我花了很多时间
搭建这些乐高积木，对吧，但是
人类也会知道可以
清理这张桌子，并且
长期以来，这是
价值对齐人们
感兴趣的问题之一，就像这种
常识推理一样，您如何获得 机器人
或 II 代理拥有同样的知识，而这些知识
现在可以通过视觉
语言模型和 LMS 得到解决。如果你拍下
这两个模型的照片，然后把它
描述给一个大型语言模型，它就
知道答案，它
明白你不应该
扔掉这些类型的乐高积木。
有趣的是，我们现在可以做
很多常识推理和
社会推理，只需利用
大型语言
模型和视觉语言模型的知识，嗯，我们
可以看看其他东西，比如语义
操作，比如引用物体，
比如鞋带或鞋跟等部件，
我可以
使用 LMS 进行语义操作，同时
训练一个非常简单的基于 keyo 的模型，这个模型
可以对此做出反应。你可以看看教人类，这是我们
之前和你们讨论过的事情，
比如用黄色的 LMS 来
教人类，在练习的时候给人类提供纠正反馈，
然后，
我想用两张幻灯片来介绍的最后一件事
是，超越
这些应用程序，将 LMS 用作
模式识别机器，所以
我的每一个应用程序 到目前为止，我们
真正利用的是大型语言模型
和视觉语言模型，因为它们
拥有丰富的上下文，
可以访问互联网
规模的数据，这使我们能够利用互联网规模的
数据，这很棒，
就像我们可以利用互联网
规模的数据，我们可以利用社会
推理、语义推理和常识
推理一样，但我认为
我们最近有一个有趣的观察，那
就是你甚至可以超越这一点，
比如使用 LMS 和 blms 实际上可以让
我们超越语义和
上下文，具体来说，这些模型
可以充当非常好的模式
机器，它们可以找到非常
抽象的模式，甚至不是
语义上有意义的模式，
这种合作就像我们之前
说的，谷歌 seir 一直在领导
这项工作，但我的想法是，我只想
展示三个例子，然后结束我的
演讲，嗯，一个例子是
你可以进行序列转换，所以
你可以拍摄一张图片，这是一张
图片，如果你有这张图片，
然后你有一个输入输出，就像
红色杯子放在绿色盘子上，
你可以有一个测试示例，
你知道输出应该是什么，
输出应该是 红色杯子应该
放在绿色盘子上，好的，这就是我要
问的，我可以使用
大型语言模型，而不是视觉
语言模型，我能做的
是，我可以将其离散化，一旦我将
其离散化，我就可以将其转换为数字，
我可以将这些数字放在
大型语言模型输入输出输入的上下文中，
然后LLM实际上将
输出一组数字，如果我将
其反投影回高分辨率，它
最终就是我
实际上想要的东西，我不建议使用
LLM来完成这项任务，也不建议使用LM来
解决Vision，但实际上
非常有趣的是，我可以获得这些
类型的模式，而且它
绝对是令牌不变的，就像
令牌没有任何意义，但因为
存在模式，它能够捕捉
这些模式，并有点像预测
该模式的下一步是什么，它可以
继续模式，您可以简单地给它
像符号W的XY位置，它
可以继续那个xire位置，所以我们
可以试试这个，今晚试试这个，就像
给聊天GPT一样，就像
任何符号的xire位置 如果你愿意，
它实际上能够继续
这种行为，这可能
对诸如我不了解机器人技术
和数据收集之类的事情很有用，所以也许这有点
牵强，但我认为
你可以这样做很有趣，你可以给
机器人的末端 Defector 位置 XYZ，给
该末端 Defector 的操作控制，
这就是我在法学硕士的背景下提出的东西，
你可以给出这样的运动，
就像我
在 XYZ opol XYZ opol 等上下文中提到的那样，
然后机器人能够
继续，机器人实际上能够
发出一个可以
继续这种行为的控制，我认为
这很酷，我认为
最有趣的是它可以进行
一定程度的优化，
例如，如果你拿起倒立
摆问题，你再次给它像
倒立摆那样的控制
以及奖励，它能够
稳定下来，你可能会说好吧，
倒立摆就像互联网上存在的那样
所以它可能确实包含相当多的
知识，但是
我们输入到
LLm 中的东西只是类似于
最终 Defector 的坐标和
报告，就像在这个例子中，我看到一个
机器人试图够到一个杯子，我正在根据
奖励对上下文进行排序，所以我会把奖励和轨迹放在一起，然后我把
奖励和轨迹
排序好，然后机器人
实际上能够继续这个模式
并输出高奖励轨迹，这
也
很酷，嗯，这有点像
响片训练，对吧，
你实际上可以对机器人进行响片
训练，假设
你给机器人一个响片，当它
越来越接近物体时，这会给
它带来高奖励，
这就是你
在 LM 上下文中输入的东西，最终
它能够够到物体，
好的，让我就此结束，所以
最后一部分的关键点是，像 LMS 和 DMS 一样，
它们 太棒了，
关于我们应该如何
看待机器人技术，我有两种看法。我
认为一个宏伟的愿景是尝试构建
类似的东西，这
很棒。缺少数据，
预训练的内容仍然是
我们应该思考的问题。但
还有第二种观点，那就是
也许你可以利用现有的 LMS
和 DMS，因为它们有很多
上下文，它们可以进行社交推理，
可以进行语义操作，
可以教人类，可以利用
互联网规模的数据，这真的
很棒，但除此之外，它们还可以
充当模式机器，可以发现
模式，这又有点令人
惊讶。我并不是建议将其用于
视觉或控制或其他任何东西，
但这令人惊讶，这也许
告诉我们应该如何进行，也许可以
继续进行下去。我不知道如何根据
模式对这些模型进行微调，或者
我们未来可以
实际使用哪些应用程序，呃，当我们
考虑将这些大型预
训练模型用于
机器人应用时，我没有谈到喂养，呃，
这是我们正在研究的东西，我
只是要放一些视频在这里。
我们正在积极研究这个问题，
使用一些基于学习的模型，这些模型
试图拾取各种类型的
食物，比如扔
意大利面，实际上他们使用 LM 来
决定，就像你在这个 Shan 身上做的一样，
试图决定要
拾取的食物量，你有
新球和意大利面，目标是
拿到任何门 Dash 面条，然后能够
喂饱人们，然后我们喂
人们，展示他们喂人们，
嗯，是的，这也需要很多优秀的
工程设计，所以这不仅仅是一个学习
策略，还有一个
反应控制器，它具有不同
级别的反应性，但它会进入
数学运算并退出，嗯，我认为这
比我展示的第一个视频要好，有些人认为
for 有点太
深入了，嗯，所以我就
到此结束了，回答任何
问题，
好的，我想我们有几分钟的时间来
回答任何问题，
例如那几个镜头，所以你
提供了一些镜头示例，例如
你提供的初始示例
是地面上有一个机器人，
这不像移动 SP，
而像站在地上，这是站直的奖励函数，
该奖励
函数具有许多参数和
许多权重，此时，
您可以说移动 p，它有点像
生成行为，就像它
计算出与
移动 PO 对应的权重是多少，
您可以运行这个 par
NPC，这样您就可以看到哪些
行为实际上有效，如果它
没有得到零分，它可以以
交互方式使其变得越来越好，
因此在这项工作中，您也在
研究纠正性语言并
改进该行为，例如通过
选项我认为实际上
我给出的月球漫步示例是通过
多次互动得出的，我不
认为那是
零分，还有其他
问题吗，非常好，我喜欢这个
观点，听起来很像，是的，是
的，这是非常正确的，它与
我们的新词非常相似，嗯，是的，所以我们正在
研究一系列
围绕使用 RF 呃用于机器人技术的工作，
特别是我认为
RF 的一个区别是 很多
基于偏好的学习工作和
相关文献并不太关注
奖励，所以
人们通常支持的工作，
比如 Crisano 的欧洲 17 号
工作，原因是那项工作
是 CH 工作，试图使用一份
新的报告，而并不真正关心不确定性的
数学模型
或类似的东西，它
只是使用一个集成模型来
捕捉不确定性，这就是
现在的情况，
有很多相似之处，有一个
问题，
比如在这些设置中，主动学习又有多重要，
我认为在
机器人设置和顺序决策
设置中，它更重要，嗯，
我们实际上已经做了一些工作，
他试图将 RF 问题转化
为对比学习，并减少
它的卡片，这是 Rafael 的，嗯，所以我认为
我们正在研究这一点，
我认为你在你
之前的工作中提到过，你假设了一个嘈杂的
理性人类模型，它
完全包含在你的 L 和 LMS 中，
你认为这可能是超级嘈杂的
理性，我们得到了一个输入 从
你从人类那里得到的输入，
比如嗡嗡声，都是噪音，所以我应该
相信他们告诉我们的事情，所以
你是
说像奖励
塑造这样的模型是否考虑了
可能性，
不是，嗯，不是，你可以这样做，
你现在没有这样做，嗯，是的，
所以我的意思是，它不是，因为
模型正在继续训练，对吧，
它除了所以之外还有一个单独的报告，所以
报告不是
它使用的唯一报告，它只是将其用作正
则化器，它不会对它造成
太大的伤害，但是你可以使用
某种
解释
你的例子，
例如，依赖学习，比如给出一些
行为，它们是上下文学习，
它们正是在上下文学习中，
我想我在这里试图表达的观点
是，它不需要是
有意义的语义上有意义的
标记，这有点令人惊讶，
因为我认为很多
上下文中的Zing工作就像真的
试图给你语义上
有意义的标记或语义上有意义的动作，
如果你有语义上
有意义的动作，例如对于
倒置 钟摆，如果你说左，我
不知道，两次左，第三次左，如果你
真的喜欢给
你正在采取的行动赋予语言，它会
收敛得更快，但如果你给它
任何标记，它也可以识别模式，
所以我认为
有趣和令人惊讶的一点是
它是标记不变的，它是
它所拾取的模式，而不是像
互联网上那样有一些语义，它
现在明白了，让我们再一次给 G 一轮
掌声