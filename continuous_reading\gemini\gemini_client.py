#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini AI 客户端
负责与Gemini API通信，生成测试题
"""

import json
import time
import requests
from typing import Dict, List, Optional, Any

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .gemini_config import GEMINI_CONFIG, PROMPT_CONFIG
except ImportError:
    from gemini_config import GEMINI_CONFIG, PROMPT_CONFIG

class GeminiClient:
    """Gemini AI 客户端类"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        初始化Gemini客户端
        
        Args:
            api_key: Gemini API密钥，如果不提供则使用配置文件中的密钥
        """
        self.api_key = api_key or GEMINI_CONFIG['api_key']
        self.model = GEMINI_CONFIG['model']
        self.temperature = GEMINI_CONFIG['temperature']
        self.max_tokens = GEMINI_CONFIG['max_tokens']
        self.timeout = GEMINI_CONFIG['timeout']
        
        if not self.api_key:
            raise ValueError("Gemini API密钥未设置，请在配置文件中设置或传入api_key参数")
        
        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models"
        self.headers = {
            'Content-Type': 'application/json',
        }
    
    def generate_questions(self, text_content: str, custom_prompt: Optional[str] = None) -> Dict[str, Any]:
        """
        根据文本内容生成测试题
        
        Args:
            text_content: 要生成测试题的文本内容
            custom_prompt: 自定义提示词，如果不提供则使用默认提示词
            
        Returns:
            包含生成的测试题的字典
        """
        try:
            # 准备提示词
            if custom_prompt:
                user_prompt = custom_prompt.format(text_content=text_content)
            else:
                user_prompt = PROMPT_CONFIG['user_prompt_template'].format(text_content=text_content)
            
            # 构建请求数据
            request_data = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": f"{PROMPT_CONFIG['system_prompt']}\n\n{user_prompt}"
                            }
                        ]
                    }
                ],
                "generationConfig": {
                    "temperature": self.temperature,
                    "maxOutputTokens": self.max_tokens,
                }
            }
            
            # 发送请求
            url = f"{self.base_url}/{self.model}:generateContent?key={self.api_key}"
            
            print(f"正在向Gemini API发送请求...")
            print(f"文本长度: {len(text_content)} 字符")
            
            response = requests.post(
                url,
                headers=self.headers,
                json=request_data,
                timeout=self.timeout
            )
            
            if response.status_code != 200:
                raise Exception(f"API请求失败: {response.status_code} - {response.text}")
            
            response_data = response.json()
            
            # 提取生成的内容
            if 'candidates' not in response_data or not response_data['candidates']:
                raise Exception("API响应中没有生成的内容")
            
            generated_text = response_data['candidates'][0]['content']['parts'][0]['text']
            
            # 解析JSON格式的测试题
            questions_data = self._parse_questions(generated_text)
            
            print(f"成功生成 {len(questions_data['questions'])} 道测试题")
            
            return {
                'success': True,
                'questions': questions_data['questions'],
                'raw_response': generated_text,
                'text_length': len(text_content),
                'generation_time': time.time()
            }
            
        except Exception as e:
            print(f"生成测试题时出错: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'questions': [],
                'raw_response': '',
                'text_length': len(text_content),
                'generation_time': time.time()
            }
    
    def _parse_questions(self, generated_text: str) -> Dict[str, Any]:
        """
        解析生成的测试题文本
        
        Args:
            generated_text: Gemini生成的原始文本
            
        Returns:
            解析后的测试题数据
        """
        try:
            # 尝试直接解析JSON
            if generated_text.strip().startswith('{'):
                return json.loads(generated_text)
            
            # 如果不是直接的JSON，尝试提取JSON部分
            start_idx = generated_text.find('{')
            end_idx = generated_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_text = generated_text[start_idx:end_idx]
                return json.loads(json_text)
            
            # 如果无法解析JSON，返回错误
            raise ValueError("无法从生成的文本中提取有效的JSON格式测试题")
            
        except json.JSONDecodeError as e:
            raise ValueError(f"JSON解析失败: {str(e)}")
    
    def test_connection(self) -> bool:
        """
        测试与Gemini API的连接
        
        Returns:
            连接是否成功
        """
        try:
            test_text = "这是一个测试文本。"
            result = self.generate_questions(test_text)
            return result['success']
        except Exception as e:
            print(f"连接测试失败: {str(e)}")
            return False

def test_gemini_client():
    """测试Gemini客户端功能"""
    try:
        # 创建客户端
        client = GeminiClient()
        
        # 测试连接
        print("测试Gemini API连接...")
        if not client.test_connection():
            print("❌ Gemini API连接失败")
            return False
        
        print("✅ Gemini API连接成功")
        
        # 测试生成题目
        test_text = """
        人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。
        深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。
        """
        
        print("\n测试生成测试题...")
        result = client.generate_questions(test_text)
        
        if result['success']:
            print(f"✅ 成功生成 {len(result['questions'])} 道测试题")
            for i, q in enumerate(result['questions'], 1):
                print(f"\n题目 {i}: {q['question']}")
                for option, text in q['options'].items():
                    print(f"  {option}. {text}")
                print(f"  正确答案: {q['correct_answer']}")
                print(f"  解释: {q['explanation']}")
        else:
            print(f"❌ 生成测试题失败: {result['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    test_gemini_client()
