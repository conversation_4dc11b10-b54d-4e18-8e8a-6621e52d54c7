凯文，嗯，是的，所以这将是
一场
与你以前见过的讲座风格不同的讲座，
特别是它将
更加侧重于理解
你在课堂上看到过的一些强化学习算法和协议的理论基础。
如果我们退一步，
试着回顾一下你在
课堂上看到的所有算法，并
思考它们在
现实世界中的潜在应用，你会发现
仍然存在一些
挑战。一个挑战是，
例如设计稳定的
强化学习算法，
这可能需要一些
设计技巧来
确保强化
学习的稳定性。我的证据通常
转化为调整一些超
参数以达到一定的
性能。现在，将
强化学习
范式应用于现实世界的另一个关键问题是数据
效率。一般来说，强化
学习算法极其
耗费数据，它们确实需要比
我们
在监督学习中常用的算法更多的数据。
然后是
泛化问题，通常需要对特定
算法进行调整。 它可以
针对特定任务进行学习，但
设计通用算法要困难得多，这些算法
可以在
完全不同的任务中表现良好。
另一个问题是
计算效率。如果你尝试
在课堂上做一些作业，
你会发现，有时
训练需要相当长的时间。所有这些
问题都是
强化学习所特有的，
但它们在
某种程度上阻碍了强化学习在
现实世界中得到更广泛的应用。在现实
世界中，
稳定性、收敛性和样本
效率等问题变得非常
重要，特别是如果你
在现实世界中进行交互，
你会希望对算法将要执行的操作有一定的可预测性。
而且样本通常非常
昂贵，因为它们相当于
与现实
世界的交互。所以在这次演讲中，我们将尝试
退一步，尝试
理解
强化学习
算法的一些基础知识，以及我们为什么要
为强化学习开发一种理论。
也许最基本的动机
是，
人们在强化学习中使用的关键基本协议
实际上是
由理论驱动的算法。 例如，值
迭代、策略迭代、
置信区间上限探索、
策略梯度下降等算法，这些算法
至少在
某种程度上受到理论的启发，而且
通常
至少在最简单的形式下都带有保证。
也有一些成功案例，将
理论发展而来的算法转化为
更实用的东西。一个例子是随机
平方值迭代，你
可能知道它是 bootstrap
dqn。此外，理论可以为你提供一些
考虑，这些考虑不仅适用于
你
试图解决的特定问题，而且可能更广泛地适用于
各种各样的问题，
我想更广泛地说，适用于
我们应用的领域。它们还有助于
揭示根本的限制，
例如你做不到的事情，
我们今天将看到一个例子。
现在，
从理论的角度来看，我们想问什么问题？
理想情况下，我们希望
对我们正在研究的算法有某种形式的保证，
例如，如果你
提出了一种新算法，你想
了解它是否收敛，
这可能是
你接下来要考虑的主要问题，当
你想把它应用到你的
问题中时，你会想了解如何
选择超参数，是否
有任何方法、公式或
权衡，你需要
提出的另一个问题是，
为了达到一定的
性能水平，算法需要收集多少数据，需要多少次
交互，你可能还会
关心诸如计算
复杂度和算法运行时间之类的问题，
这是你想
研究的，但
实际上，回答这些问题
极具挑战性，非常
困难，例如，对于大多数
深度
算法来说，甚至不可能
证明收敛性，因为
归根结底，基本的时间差分
方案，或者带有经验回放的电视
和目标网络，它们并不
总是能保证收敛，所以
我们立即面临着某种挑战，
事实证明，回答这些
问题通常非常
困难，所以你今天会看到，
你在课堂上看到的实用算法
和一些 我们
今天要讨论的这些考虑，
但无论如何，我将主要关注
统计
方面，也就是
你需要多少样本才能学习某个问题。
我将研究三个不同的
宏观主题，首先是尝试
理解强化学习中哪些问题
容易，哪些问题
困难，以及我们能否
在较简单的
问题上学得更快。然后，我将重点关注
算法和函数逼近之间的相互作用，也就是
泛化问题。我将简要讨论
统计限制，以及
强化学习算法无法实现的功能。此外，我还会
简要讨论离线
强化
学习。现在，让我们进入
第一部分，理解哪些问题
容易，哪些问题
困难。我们
在这里考虑的场景是探索问题。我
认为
你们学习的大部分课程都是关于
探索算法的，思考
标准在线设置 dqn 所有这些，
因此在这种设置下，你有一个
强化学习代理，它
从一个空数据集开始，并且与
AG 步骤进行交互，
直到游戏结束，例如游戏的结束，
这个交互函数 正在进行中，它会
持续多集，
你想测量
强化学习代理
现在的学习速度。直观地说，
强化学习代理一开始会采用一种
可能不是最优的策略，如果它在
玩 Atari 游戏，那么
如果从空
数据集开始，第一个策略会很糟糕，但随后它会逐渐
学习和执行越来越好的策略。
我们想要做的是
测量算法的性能
，
标准方法是
定义一个叫做
遗憾的量，你可能在课堂上见过，
它实际上是代理所
执行策略的次优差距的总和，
直观地说，至少
问题很简单，一个正在
学习的算法在性能方面会接近
最优策略的值，
但它会以一种
不太了解的方式开始，所以它所
执行策略的初始值会很低。如果我们把
所有的次优差距作为
情节的函数加起来，嗯，那就相当于
计算 这条曲线，
橙色阴影部分，
嗯，这就是我们所说的算法的遗憾，
我们的目标是尝试
设计一种
算法，嗯，最小化
遗憾。现在，在大多数情况下，你可以做到这一点，
例如在深度学习中，目前还
不清楚你是否能做到这一点，所以我们
将重点关注
今天第一部分的问题，这些问题涉及小状态和
动作空间，即我们有一个
表格
表示的问题。如果我们回到
2010年、2011年以及随后的几年，嗯，
在基础上，人们大力
推动设计算法，使其
尽可能高效地解决
表格问题，并且
已经提出了几种算法，
它们具有某种形式的遗憾
界限，它与状态
和动作空间有关，特别是基数、
地平线和
情节数量。嗯，这些遗憾界限很有
用，因为它们广泛应用于
任何mro决策
过程的问题。你不需要担心
问题的具体细节，只要
你知道动作空间中的有限状态，
你就可以保证 这些
算法的
局限性在于，
如果问题具有特定的
结构，我们无法确定某个算法的性能会更好还是更差。我们在实践中看到，
即使是同一种算法，在处理
完全不同的问题时，强化学习算法的性能也会有很大差异。
因此，我们想开始尝试
系统地理解
强化
学习中哪些问题比较难，哪些问题比较容易。从历史的
角度来看，人们已经付出了很多
努力来改进这些遗憾
边界，直到我们最终得到了一种
算法，就最坏情况的
性能而言，它是无法改进的，这意味着
它
在所有问题上的性能保证尽可能好，
前提是我们
已知的下限，这意味着性能是无法
改进的，
没有任何限制，存在一个根本
无法
超越的极限。同时，我们知道，有些
类型的问题与
创建下限的忏悔结构类型非常不同。
一个例子是
没有动态或弱记忆的问题，
弱记忆的问题是指
动作 你过去采取的措施
对你的状态影响不大。
想想推荐
系统，这是一种上下文
Bandit 问题，嗯，
弱记忆的情况
出现在推荐系统中。想想
亚马逊的一位顾客，如果你
直觉地给出不好的推荐，
你可能会让某位顾客
不满意，但这不会影响
你看到的下一位顾客，所以这是一个
弱
记忆的问题。对于 Bandit 问题，我们
知道有一些特定的 Bandit
算法可以利用这种
结构，而且它们的学习
速度比经典的 Markco
决策
过程快得多。同样，
确定性问题通常更容易解决。
它本质上是一个搜索
问题。同样，你
只能在状态和动作空间中局部移动的问题
通常更
容易解决，因为如果你犯了错误，
你仍然可以以某种方式恢复。一个
例子是 Mon
Car。现在我们要问的问题是，如果
我们将这些问题视为表格
问题，其中我们有
状态和动作空间的明确表示，那么
Dynamics 会做什么？ 这些
简单问题都有一些共同点，我们能否
找出一些共同的
特征，并尝试衡量
它们的难度？如果问题属于……我们能否更快地学习？
如果
我们面临的实际问题实例属于
这些子类，我们对此给出了
肯定的答案。我们
首先提出了问题相关的复杂性，
然后基于此提出了一个具有某些
特定
属性的算法。首先，我们提出了一些
问题相关的复杂性度量，它
表征了
不同强化学习
问题的复杂性。它
由系统
动力学和最优
策略值的相互作用定义，它被定义为
下一状态最优值
函数的方差。
如果你
不知道实际的Mark决策
过程，算法就无法计算这个变量，因为最优值
函数是未知的，动态
也是未知的。但尽管如此，你可以
设计一个具有
性能界限的算法，该界限会随着这个
量而变化，这个量通常是未知的，
算法不需要知道这个量，
因此该算法能够
匹配
表格MRO决策过程的最佳性能，这意味着
它是极小极大最优，它是一个
可改进的…… 但与最
先进的算法相比，它也能达到
最佳
性能。如果问题属于
某一类较简单的问题，
例如，如果它是一个上下文带状
问题，那么该算法会自动
匹配，本质上与
基本UCB在上下文带状问题上的性能相匹配，
并且除了
在某些问题子类上分析较小之外，
您还可以用
数值方式评估数量，它将出现在
人们
之前考虑过的问题上，它取的值
比先前界限所建议的最坏情况值小得多，所以本质上它是一个
数量，嗯，它
在我们关心的问题上分析较小，但在
之前考虑过的问题上数值也较小。现在，我想停顿一下，
问一下这部分是否有任何技术
问题，在我继续之前，是的，
嗯，
学费，嗯，这实际上取决于
问题的类型，
例如，如果一个问题的记忆性较弱，
那就是一个上下文带状
问题，那么
你在某种状态下可能犯的错误
实际上不会产生长期
后果，
所以 下一个状态值
函数，嗯，它在不同的状态下不会有太大的
差异，
所以本质上这个量
必须很小。你可能会犯错，
但你只会损失当前
客户，对吧，你不会搞砸
整个长期计划，对吧，嗯，所以
这个量最终会变得更小，想想
在估计
转换的影响方面有一些挑战，但
转换可以是高度
随机的，例如在 Bandits 游戏中，
它们是高度
随机的，嗯，但你最终得到的状态值仍然没有
太大的变化，
在这种情况下，
它会
很小，你可以把
它放宽为
轨迹的期望，在实际工作中它是至上确界，
但你可以
放松，好的，嗯，我想给出一张
可能更技术性的幻灯片，嗯，
关于我们如何实现
这样的目标，嗯，嗯，探索通常是
通过
在经验奖励中添加探索奖励来实现的，至少对于有效的算法来说是这样，
想想 dqn 探索，
至少在 Epsilon 贪婪算法中是这样
最基本的
形式，嗯，但是如果你想要更
复杂的方案，嗯，想想
带状算法中的UCB，通常
会添加一个奖励，现在奖励
可以采取不同的形式，
现有技术使用的最基本的形式是与
它来自的样本数量的倒数成比例的不等式，
嗯，但是这种类型的
探索奖励本质上是问题
独立的，这意味着它与MDP
的任何特定特征无关，
因此算法将以
相同的方式进行探索，而不管问题是什么，
这不会引起与问题
相关的
界限，现在人们
想要做出的理想选择是使用某种形式的
基于伯恩斯坦的集中
不等式，它确实包含与
我们
想要的数量非常相似的东西，嗯，它会引起与问题
相关的界限，但有一个问题，
一般来说，最佳动作值
函数你不知道它是什么，
过渡动态你也不
知道它是什么，所以虽然这种
奖励的选择是
理想的，嗯，但它实际上不会
引起什么，就像它不是某种东西一样
在实践中，你可以这样做，解决
这个问题的直观方法
是尝试使用经验
动力学和一些经验估计的
最优值函数，但
如果你尝试这样做，会出现几个挑战，
主要的挑战是，
通常这些量是
未知的，想想当你
刚开始的时候，你
对动力学知之甚少，所以你
基本上没有办法猜测这些
量是什么，如果你做了
错误的猜测，
算法可能不够乐观，
可能没有进行足够的探索，
它就找不到一个好的策略，
所以你要做的是
引入一些修正项，
谢天谢地，这些修正项
试图纠正你的错误
估计，它们衰减得非常快，所以
它们衰减得更快，所以这
就像代理应用了一种
正确的基于伯恩斯坦的
浓度不等式，但有一个
修正项衰减得非常
快，这里的挑战在于
估计
修正的大小，特别是因为我们必须
修正一些
不同于最优值函数的值函数 一是
估算误差，它需要
估算误差如何
通过
DMDP 从我们可能
很少访问的状态传播，
而这种选择本质上会
引发那些与问题相关的
边界。
现在这很好，因为它确实能让
你
初步
理解是否有可能
适应问题的难度，以及
是否有可能在我们感兴趣的各种问题类别
上同时达到最大最优和实例
最优。
但
这里最大的限制是，
这当然只适用于较小的状态
和动作
空间。在实践中，我们希望
解决具有非常大的
潜在连续状态和
动作
空间的问题。要清楚，你
在类中看到的总是属于第二
类，一旦你开始使用任何
形式的函数逼近，你就属于
这一类。所以
我们接下来要尝试理解的问题
是，关于函数逼近的强化学习，我们能说些什么？
答案会比我们在
这里做出的某种假设更负面一些。 嗯，这是一个
积极的进展，但在这里我们会看到
，当你开始讨论使用
函数逼近的强化学习时，
即使是看似简单的问题，也
可能非常具
有挑战性。所以，快速回顾一下
实际问题，它们总是有一个
非常大的状态空间，
大多数状态从未访问过。我们
想要做的是引入某种
形式的函数
逼近，它可以将
广义知识从
我们已经看到的状态添加到我们
尚未观察到的状态中。希望
我们不需要学习
在每个状态下要做什么，而是只需要一些与
我们模型中的参数数量大致相同数量的样本。
现在，我们可以观察到，F
Clore 的观察结果
是，强化学习
算法使用函数
逼近，与监督学习相比，它们仍然需要大量
样本。
因此，我们
想问一个非常基本的问题：
强化学习是否
比传统的监督
学习更难？为了研究这个
问题，我们考虑一个与离线
非常相似的设置。
你在课程第二部分看到的强化学习设置，
嗯，在离线强化
学习设置中，你有一些
可用的数据集，它由
状态、动作、奖励和
状态的成功组成，我们尝试提出一些问题，
例如，
与生成
数据集的策略不同的策略，你可能想要
尝试识别最优策略，或者
你可以尝试进行离线策略
评估，
嗯，具体设置 我们考虑的 g
是允许在 um 之前进行某种具有
静态分布的数据收集，
这样做的原因
是为了提供一定的灵活性，因为如果
数据集直观上很差，我们就
无能为力，这不是
算法的问题，只是数据集的问题，
也许我只有单一状态的数据，
所以我们确实考虑这样一种情况，你
可以事先用静态策略进行某种形式的数据收集，
然后我们
尝试了解我们是否能够
成功预测 um
不同策略的值，或者提取
最优
策略的值。现在我们的期望是，
如果动作值函数有一个
简单的表示，例如，如果
动作值函数 um 具有线性
展开，并且我们甚至知道
特征提取器，那么这应该
是一个简单的
问题，为什么呢？这只是
与线性回归的类比，如果你正在
解决回归问题，我给
你一个特征图，我保证
问题是可实现的，所以目标确实
有一些线性展开，也许还有
一些噪音，那么你可以打开
统计学教科书，你会
发现 um 标准线性回归
可以非常快速地学习这个问题，
然而在 强化
学习，即使是线性问题，也
似乎不那么容易解决，特别是有
经典TD和拟合Q的发散性的例子，
即使是
在线性
可实现的问题上也是如此。事实上，如果你
看一下对
一些基本算法和
协议的分析，你会发现它们都
做出了一些假设，这些假设似乎
比可
实现性强得多。所以事实上，
我们不知道在2020年到
2021年，即使是最简单的线性
设置，我们是否可以提供
稳定的算法，我们是否可以提供一种
算法，例如收敛
和收敛，是的，因为你可以使用
LSD，但是我们能否保证，
例如，即使
在这种
简单的设置中，学习所需的样本数量，这是
表格问题之后的第一步，真正要
理解正在发生的事情，你需要将
监督学习与
强化学习进行比较，关键
区别在于你是试图
对一个时间步长还是
多个时间步长进行预测，
这是因为如果 你试图
对一个时间步长进行预测，你
从一个你可能已经
以一种智能方式回忆起来的数据集开始，
嗯，而如果你只是试图
预测第一个奖励，并且你
保证奖励函数是
线性的，那么我们知道线性
回归嗯可以非常快速地解决这个问题，
所以我们知道一种算法，
我们也知道保证，这是
你能想到的最基本的U型机器学习算法，
然而我们的问题是，
如果我们想要预测一个
策略在多个时间步长中的值，并且
保证
这个值实际上是可实现的，这意味着
我们有一个特征提取器
嗯，它可以正确地预测
某些数据参数的目标策略值，事实
证明，这个问题在
最坏的情况下是极其困难的，这
意味着嗯，与监督
学习相反，你会发现有一些问题，你
有一个漂亮的线性模型，但是
嗯，任何算法都会采取大量
样本来做出正确的预测，这些预测
在
特征提取器的维数上呈指数级增长，
当我说预测嗯，你可以
广义地理解为
答案是一样的，比如，如果你想要
找到一个
最优策略，我想要一个
比随机策略更好的策略，你仍然需要
一些样本，在最坏的情况下，这些样本
在特征提取器的维度上可能是指数级的。
所以我们看到，监督学习与预测之间存在着很大的
区别，
如果你想要一步预测，那么
强化学习会
考虑连续的过程，随着
时间线变长，问题的难度
会呈指数级增长。这
并不意味着所有问题都会呈
指数级增长，但它确实告诉
你，即使是看起来很
简单的问题，应该是
线性的，所以它们应该
很容易学习，你也无法
找到一个
对这些问题有保证的算法。所以
为了学习，或者为了让算法
学习，必须有一些
额外的特殊
结构。事实上，
我们看到，独立的PO
样本复杂性是强化学习中的一个主要问题，
这个问题是 也与
散度有关，但这里的贡献
实际上是要确定这些问题与
算法无关，它们是
信息论的，这意味着
强化学习问题中存在一些基本的难题，这些难题
广泛适用于
您能想到的所有算法，您将无法
找到一种能够
解决所有问题的算法，即使它们像
简单的L
线性问题一样，这个问题也已被
其他一些
重要的论文更广泛地研究，有些论文有
类似的结果，如果您想重新
解释第二
部分，您也可以
从在线RL的角度来看待它，
我可能有一个动作值函数，
想想你在dqn中有什么，
它没有深度无网络，而
只有一个简单的线性映射，我
向你保证，问题确实
有一个线性动作值函数，
它仍然
无法找到一种
可以快速学习的算法，
所以这里的主要要点是
线性回归在开始时很容易，
但在强化学习中等效
从无模型的角度进行学习
已经遥不可及，所以我们
必须
对能够解决的问题类型保持一定程度的乐观。我们
确实付出了巨大的
努力，试图理解
为了获得多项式
样本复杂度，例如统计
中的许多统计算法，需要哪些额外的条件。在
我们继续之前，
关于
第二部分还有什么问题吗？是的，
[音乐]
是的，我认为真正
棘手的是，
这真的是一个无模型的
观点，所以我们在看
我们是否拥有足够的关于
Q
值的信息。但不知何故，你可能会遇到
极其
复杂的问题，但动作值函数
最终会变得简单，它最终会变得
有点线性，所以实际的
例子
本质上是一个
非常复杂的奖励函数，就像一个Ru随机
网络，它只在
状态空间中一个非常隐藏的区域为零，这个区域呈
指数级增长，
动态非常复杂，而且
动态的设计方式是
它们将奖励函数线性化，
也就是说，一旦你执行了
BMAN 备份的许多步骤，你最终会得到一个
看起来是线性的动作值函数，
所以看起来问题很简单，
因为它确实是线性的，但
你真正想要做的是
确定奖励函数
在一个指数级
更大的
球体中不为零的位置，嗯，我不知道我是否可以说得
更多，但嗯，这真的
与高
维有关，嗯，你在
高维中有很多空间，
如果你在高维中取随机向量，
它们几乎总是
AOG，
所以你可以
在非常高的维度中隐藏信息，这不是在
二维或三维中显而易见的东西，
你真的必须去高
维，是的，策略 p 是固定的，
嗯，它可以是
预测最优策略的值，它
可能是固定的，也可以是
最优的，
嗯，我确实想用一张
幻灯片来谈谈，嗯，实际上，如果使用
更一般的函数
近似，
嗯，就积极的结果而言，如果
你打开 一些关于统计学的书，高
维统计学，至少对于
回归分析来说，你会发现有一些
性能保证，这些保证取决于
你正在使用的函数类，所以如果你使用 K
方法、凸函数或其他
东西，你会有一些性能
限制，在
近似误差和统计
复杂度之间进行一些权衡，统计
复杂度通常用
R 等概念来表示，市场
复杂度 B 维和其他东西，
但在 EV 中，这还不够，
所以看起来
bman 运算符和函数之间的相互作用，这个
函数
类与你用来
为 TD 方法建模动作值函数的函数类相同，但
相互作用真的很重要，
所以人们关注的重点是
理解 L 的一些基础知识，
不仅仅是函数类的复杂性，
这还不够，
就像我们之前看到的，我们有一个线性映射，
这已经太难了，
必须有一些东西使
问题变得可学习，这就是
bman
运算符和动作值函数之间的相互作用，
这对 TD 方法至关重要的原因
是你 采取一个行动
价值函数，你
用它创建bman备份，你正在拟合类似的函数，
你希望它为零，所以
交互变得
至关重要，所以人们提出了很多概念来
尝试理解，在
什么情况下你可以
以一种稳定
且统计
有效的方式进行这种学习，但我不会深入探讨这个
问题，而是直接转到
一些离线强化
学习，离线强化
学习，你已经在课堂上看到过了，
但只是快速回顾一下，
现在已经有很多数据了，
所以我们想利用它们，
我们如何在不收集更多
数据的情况下做到这一点，嗯，设置和
你在课堂上看到的一样，我们有
一个状态动作的历史数据集，
其中包括奖励状态和后继状态，
任务是如何找到具有
最高值的策略，在
给定数据集的情况下找到具有最高值的策略意味着什么，当然，最高值是
具有最优策略的策略，但是你的
数据集 可能不包含
关于最优策略的任何信息，所以
我们必须尽最大
努力，
尽力
找出一个好的策略，降低
期望值，但可能还是找不到
最优策略。我
认为你在课堂上也讨论过这个问题，主要的挑战
是分布偏移，
也就是说，
数据集，一个最好的
情况，也就是从未发生过的情况，就是
你的数据集
在整个状态和动作
空间中作为均匀的样本，在这种情况下，你可以尝试
评估策略 Pi 1、Pi 2 和 Pi
3，然后选择
最好的一个。但通常你给出的是
轨迹，它们可能
来自人类，所以它们
通常
集中度很低，这就是我们所说的
部分
覆盖问题。在这个例子中，数据
集可能包含很多关于
Pi 1 的信息，没有关于 Pi 2 的信息，但有一些
关于 Pi 3 的信息。你
必须想出并在这三个策略之间做出选择，
找出哪个
策略是
最好的。他们
今天想要强调的是，我们如何
衡量这个覆盖率，有多少
信息 数据集包含
寻找一个好的策略，
直观地说，解决这个
问题的方法正是你在课堂上看到的，
或者实际上有两种方法，
一种是尝试接近
生成数据
集的策略，某种形式的行为克隆，
另一种是尝试估计
预测的不确定性，
所以通常你的数据
集是
由策略生成的，某些
策略的集中度比较高，
它们会给你
关于状态、动作、奖励和
转换的数据，你会尝试
拟合某种形式的模型，并尝试使用
该模型来预测
其他策略的价值。现在，模型
实际上不需要是一个模型，你
可以用一种不受模型约束的方式来做到这一点，但
你仍然在使用一些由
某些策略生成的数据，这些数据
对其他策略做出了预测。
当然，你
想要做的是选择
具有最高价值的策略，但这是
未知的，相反，你想要
返回一个看起来具有
良好价值的策略，但你也
对此有相当的信心。所以，一种看待 好的，
嗯，就像一个程序，试图在
返回的策略值和
该策略的不确定性之间找到某种最佳权衡。想想
一些Biance偏差方差权衡。
嗯，在统计学中，你想要
一个具有最佳
Biance方差权衡的算法。嗯，Biance
通常是未知的，你可以
尝试估计方差。在离线强化学习中，有
一种类似的概念，如果你
想要平衡
你返回的策略值（它对
你来说是未知的）和它的不确定性，
从而保证离线强化
学习算法。嗯，它们通常
看起来像
这样：一个算法应该以
非常高的
概率返回
它返回的策略值
和不确定性之间的最佳权衡，这本质上
相当于找到具有
最高下限的点。在某种意义上，离线
强化学习就是
你在课堂上也看到过的那种，他们以某种
方式试图达到这种最佳
权衡。现在一个大问题是，
这种依赖于策略的常数C是什么？
嗯，如果你见过
统计学中的浓度不等式，
你可能会 我们已经熟悉了“
1 除以 n 的平方根”这个术语，它来自
于
不等式，但这里有一个额外的
系数，它取决于策略，该策略
应该包含
分布偏移。现在，这个系数
取决于实际算法，它
取决于例如
你正在使用的函数类以及
与
bman
运算符的交互。作为一个具体的
实例，你可以采用例如
soft Max 策略，考虑那些
来自自然策略梯度的策略，以及
Simplicity 线性动作
值函数，它们是两个
不同的参数，你可以设计
算法，本质上尝试
解决这个离线强化
学习问题，并且会有一些
保证，这些保证正是这种
形式的，其中覆盖
系数具有一定的解析
表达式，解析表达式
实际上突出了数据
集中包含的信息
与
你试图估计的目标策略之间的相互作用，特别是
数据集中包含的信息
反映在协方差矩阵中，协方差
矩阵是统计线性回归中一个比较熟悉的
对象，
你可以计算一些协方差矩阵，
协方差包含的
信息量 你知道这个
问题，它与某个
范数相互作用，这个范数的逆是预期
特征，也就是
你正在考虑
优化的目标策略，嗯，这个量你知道，
但这个量一般是不可计算的，
对吧，所以这告诉你两者是如何
相互作用的，嗯，
为离策略评估创建置信区间，
你可以用它来找到一个好的
策略，这有点令人惊讶，
也许并不令人惊讶，但
重要的是，这种
覆盖率，也称为集中
能力，嗯，它实际上
并没有在状态
和动作空间中表达，
如果你打开一些做
统计分析的论文，你经常会
发现
目标策略与行为
策略的繁忙分布（折扣 Vis 分布）之间的比率，这是
状态和动作空间中的比率，这个没有这些，
它都被投射到一个
较低
维度的特征空间，在那里
收敛确实可以
大得多，想想有一个 Coan 矩阵，
它是身份，嗯，
这肯定会使
覆盖系数非常
小
[音乐]
嗯 我不想谈论
我们如何实现这一点以及
技术细节，我认为
重要的是，例如，
在飞行强化学习中，保证在
实际陈述方面是什么样的，就像
你在上一张幻灯片中看到的一样，但是如果
你想在非常高的水平上，嗯，我们
试图避免
直接惩罚嗯动作，
我们希望保持非常低的
统计复杂度，我们
在参数空间中操作以
计算这些置信区间，
所有这些都被放入一个大的
策略算法中，该算法使用嗯自然
策略梯度和一些悲观
版本的嗯TD与目标网络，
其中参数以
计算悲观
解决方案的方式移动嗯，我是GNA，保留
算法嗯，现在你已经看到的这项研究的一个局限性
是它
适用于线性设置，当然
问题是，如果我使用一
组更丰富的函数，例如
离线强化学习，使用更
通用的函数近似，比如
你在课堂上看到的那些，
我们能否对这些函数近似做出任何保证
嗯，答案是，不幸的是，存在
一个巨大的差距，因为
你在课堂上看到的那些算法
很难证明
它们的有效性，因为它们可能不会收敛。
有一些变体在
某种意义上可以提供保证，
但最大的问题是，
你不清楚如何实现
它们，嗯，这是一个关于
函数广义函数逼近的问题，
嗯，如果你想要保证，你
不清楚如何想出一个你可以
实际
实现的算法，所以很多时候，所
分析的是一种概念版本，与实际实现的算法
不一样。
现在，在我得出
结论之前，关于这种第三部分有什么问题吗？
是的，当然，Orizon，现在没有，嗯，
如果这是一个有限的Horizon问题，那么
cians真的可以随着时间
步骤而改变，嗯，是特征的ciance，
所以费用
转移的总和，特征，
例如，与线性
回归相同，会出现相同的对象，是
的，你怎么办？  Epsilon 最优策略的意思是，
这不是
最优的，对吧，因为它是离线 RL，所以
它实际上取决于你的数据
集，
如果你的数据集没有
好的
信息，你找到的策略可能会很糟糕，假设你的数据集
只是来自一个狭窄集中的策略，
而行为策略很糟糕，呃，
这个特征矩阵就像秩一一样，在
一个方向上非常集中，那么
它并没有真正告诉你
很多，所以你将无法找到
一个好的策略，但不知何故，它们在
某种程度上反映在了那句话的摘要中，
对吧，因为
非常好的策略会有
非常大的覆盖系数，所以
你不会知道它们的值，也没有
算法会返回
它们，
是的，嗯，不，我想，如果你想要的话，
这就是你所说的 Epsilon，对
吧，这就是我们
将要
返回的策略，嗯，你可以一直思考上确
界中的最优策略，我想
在最优策略下评估这个表达式，
我 可以做到这一点，但随后这个值
将成为
关于最优策略的某种覆盖范围，所以
这是你的 Epsilon，对吧，
与最优策略相比，Epsilon 是次优的，
但 EP 可能很大，
基本上，我告诉你，
给定我们拥有的数据集，Epsilon 的值。DC
实际上是一种衡量数据
集中包含多少信息的方法，
嗯，以及因此，
在初始状态下，你可以预期的性能是什么，我会
说初始状态下的策略值，那是
你关心的，你的
估计值可能更多地取决于
你不访问的州，甚至取决于
你访问的州，但它们可能会有所
补偿，但你真正关心的是
起始点的性能，
好的，
嗯，对吧，
对吧，所以我认为你在
cql 中看到过类似的东西，我想
嗯，
基本上在 nexer 上，让我们绘制
不同的策略，以便明确你
将拥有无数的策略，对吧，但
让我们把它们放在图表上
，在 Y 轴上，我们将绘制值
政策，是的，是的，是的，是的，预计这个
国家/地区和世界的总和，现在您
希望知道的是政策的实际价值，
对吧，绿线就是
政策的价值，所以如果您知道
实际的MDP，您可以进行价值迭代，然后
您会找到这个最优
策略，不幸的是，您拥有的是一个
数据集，现在如何使用数据集取决于
您自己，如果您是
基于模型的，您可以尝试拟合某个
模型，并尝试使用它来进行
预测，您的模型可能很好，也可能
不好，通常可以很好地
预测
生成数据集的政策价值，但
预测不同政策的价值可能非常糟糕，
您可能不会使用基于模型的
版本，而可以采取
其他方法，例如，您可以采用
无模型方法，例如
cql，嗯，
直观地说，如果可以的话，您会做的是
尝试对
不同政策的价值进行估算，
并尝试衡量不
确定性，不确定性实际上就是
这里的这个带，
对吧，并且这条曲线可能会
根据数据集上下移动，嗯，但
您想尝试估计
预测的不确定性
直观地讲，对于生成数据集的策略而言，策略评估的不确定性会
更小。
你拥有大量数据，只需
取平均值即可。但对于
访问状态
和动作空间完全不同区域的策略而言，这将非常糟糕。
你的数据
集中度很低。你对在动作空间中
完全未知的状态区域执行操作的策略一无所知。
所以，即使该策略通过
拟合队列看起来
不错，你也必须考虑到
你对这个值非常不确定，
所以你想以某种方式
惩罚它，所以你会说，
这个策略，就像我太不确定了，我
会给它一个非常
低的值。如果我执行此过程，
最佳权衡是最大化这个
下限，即
策略性能的下限，这为
你提供了抽象的
表达式，
一旦你考虑
特定的算法和特定类型的
函数类和MDP，它就会变得具体，
这将决定
Cy，但是，是的，是的，是的，是的，是的，是的，
当然。 谢谢，
我认为这里最重要的一点是，
你想要一个要点，那就是
离线算法的保证究竟是什么样子的，
它看起来像这样，在
价值之间进行一些权衡，当然
你想要最高的价值，但是对于具有
高价值的策略，你可能
对它们非常不确定，所以会有
一些
权衡，嗯，只是为了让你
更
清楚地了解，嗯，对于
数据集中的策略，通常你有很多
数据，所以这个 C piy 会
像 1 一样，
你有很多数据，所以 n 很大，但是
这个数量
很小，所以这个表达式告诉
你，你应该比
生成数据集的策略做得更好，
如果你正确设计了算法，
这有点像你
期望的最小值，你想要
比克隆做得更好，对吧，这个
表达式准确地告诉你，如果我
把 pi 作为
生成数据集 C 的行为策略，pi 会
很小，它会是 1，
这个表达式会更小，这
告诉我我比克隆的行为做得更好，
这也是我们所
期望的，好吧，嗯，总结一下
嗯，我们已经看到了三件事，
第一，强化学习中的大多数问题都不是最
糟糕的情况，它们属于更容易解决的
问题。嗯，我们已经看到，
一旦我们转向函数
近似，强化学习就会
比标准监督学习困难得多，
然后我们就会看看
我们可以为
离线强化学习
算法获得什么类型的保证。总而言之，我认为在
演讲之后，这一点更加清楚，
理论和实践之间存在巨大的差距。嗯，我认为
在
交叉领域工作当然很困难，
因为你必须同时取悦
两个群体，但它可能会使
强化学习更具
适用性，因为需要做出
妥协。你
不会击败任何基准，或者
你知道在任何基准之上，但你
也许能够想出一些
算法，这些算法有一些
分析，嗯，至少在简单的情况下，
一些稳定性保证
是至关重要的，
为了将强化学习应用于
非常不同的问题，你会
更有信心应用它，
如果它有某种形式的
保证支持 即使在受限的集合中也适用，嗯，
一般来说，理论上他不会
告诉你如何调整超
参数等等，所以它们没有
必要，它不一定会告诉
你任何特定
应用程序的具体情况，但它可以给你
更广泛的见解和基础，
这些应用程序可以更广泛地应用于
该领域，我们之前已经看到了一些基本的
下限，嗯，是的，我
想就这样结束了，这就是
我今天要讲的全部内容，所以谢谢
你的
[掌声]
关注，
我想问一下是否还有最后一个
问题，谢谢你来到
这里