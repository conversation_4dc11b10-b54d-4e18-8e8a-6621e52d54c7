嗯，他之前
在加州大学伯克利分校和我们一起做过演讲，嗯，实际上，
我们离线强化学习讲座中涵盖的很多材料都是
由 AVL 部分完成的，而且
我们强化学习理论讲座中的几乎所有材料都
只是从一个讲座中复制而来，
所以他为
这门课做出了很大贡献，
今天他将向我们介绍如何通过
离线
强化学习进行预训练和利用大型模型，谢谢。嗯，是的，今天我将
与你们讨论离线强化
学习，用于预训练和利用
大型模型，嗯，这些大型
模型不属于 NLP 或
语言的范畴，而是用于机器人技术，用于解决许多
决策问题，
嗯，是的，所以你们都知道你们都
学习过离线强化学习，我
想现在有两节课是关于这个的，
嗯，离线 AR 背后的想法很
简单，你不想
通过在线试验和
与环境的交互来学习，而是
使用现有的交互
数据来学习可以
最大化奖励的策略，所以这是
你看到的标准范式 有很多算法
可以实现这种基于模型的算法，比如 Q
学习等等，但我不会在这里深入讨论这些
算法，但这是
所有这些算法都
试图
遵循的通用范式。嗯，这让
我想起了，当我在
通用机器学习的背景下思考这个问题时，
标准的机器学习流程是获取
一些训练数据，在这种情况下，它是
现有的离线数据，在
这些数据上训练一个模型，然后将这个
模型部署到实际任务上，所以这是一种
离线 RL 的范式，
添加到标准或典型的机器
学习流程中，即数据模型，
用一些目标来训练它，然后
部署它。但如果你看看
我们目前在机器学习中所做的事情，嗯，什么
变得越来越流行，
越来越被利用，就会有一个
稍微不同的范式，其中的
情况发生了很大的变化，所以
不是直接从数据到数据，而是
使用数据来训练，
然后部署模型，现在我们想要
做一些叫做预训练的事情，也
就是获取大量的数据，这
可能与你的下游任务没有太大关系，
但我们想要训练一个
通用模型。 这些数据，
然后当使用这个模型时，
我们不想直接
在现实世界中或直接在
实际问题上运行这个模型，而是想根据我们关心的
下游任务对这个模型进行微调，
所以我们想
通过预训练来学习通用模型，
然后通过某种微调将这些通用语言模型应用于
下游任务，你知道
这方面的典型例子是大型语言模型，
你知道所有类型的基础
模型，所以
我今天想谈的
是，当
你考虑
离线时，当你考虑决策
问题时，如何转向类似的范式？离线
强化学习如何为你提供一种
实现这种
决策和控制
任务范式的方法，所以我现在想做的不是
摆脱这种获取
现有数据并制定
最大化奖励策略的范式，而是将
离线 r 视为另一种范式，它
不仅可以获取给定的任务数据，
还可以获取与你的问题相关的所有可能数据，
这可能包括
任何现有的机器人数据
任何现有的游戏数据，
任何
来自医院的数据，用于医院
决策问题等等，所以现在把所有这些数据
放在一起，不仅可以生成擅长
最大化
奖励的策略，还可以生成良好的预训练
初始化、良好的特征、
良好的表示，任何你能
想到的对现在有用的东西，找到
你关心的下游场景，
所以
在今天的演讲中我想谈谈如何实现
这样的流程，你如何考虑
离线 AR 方法，以及如何扩展你
在这门课上学到的所有方法，以实现像
这样的东西，我的图片，而不是
上面的图片，所以更具体地说，让
我们把
这种高级目标分解成三个
不同的部分，为了激发这
三个不同的部分，我们看看
这张图片中不同的组件是什么
样子的，所以如果我想从这张
图片开始，在上面训练一个
模型，并生成一个已知的
下游策略，到第二张图片，
我的组件是什么样子的，
第一个组件是能够
从中学习的 任意数据源，所以
如果我想实现这个图景，我
不仅需要为给定任务获取数据，
还需要任意数据源并对其进行训练。
今天我们要讨论的一个具体例子是，将
人类视频数据用于
机器人技术。如果我想训练一个机器人，
但我不只想使用机器人数据，还想使用
大量
基于互联网的视频数据，我该如何使用这些数据来
学习好的策略？我会讨论
这个问题，我也会讨论扩展，
所以当数据越来越多时，
你会想要使用越来越大的
模型，那么如何让这些
离线强化学习方法
扩展，以便能够训练
越来越大的模型？我会讨论
一些与AIS相关的内容，
我还会讨论
你为构建微调
算法所做的一些初步工作，这些算法可以
让我们进行通用初始化，
然后使用
有限量的数据针对给定的
下游问题对它们进行训练，从而使你的
初始化在
你关心的给定任务上越来越好。 关于
数据、缩放和算法，这些就是
我们今天要讨论的三个主题，
让我们开始吧，我
决定稍微调整一下顺序，我
先从缩放开始，
然后我们再讨论数据，
在缩放比例那里找到你的名字，嗯，
嗯，如果
你看一下强化学习中的缩放比例，嗯，
这是我
从S Clear论文中复制的一张图，现在看来，它已经
非常非常古老了，嗯，这些
你
在这个位置上看到的所有这些点基本上都是
一些超级学习
方法，这些方法在
S Clear论文发表的时候是最先进的，
嗯，如果你看看
强化学习社区中的大多数工作，嗯，
它们通常都训练
非常小的模型，所以它们训练的模型都
位于这个图上的灰色小框内，
而
监督学习或只是普通的单元
监督学习已经远远超出了
这个特定的框，所以我
今天要谈的是，嗯，你知道，
显然它并没有那么革命性，它
并没有移动这个
从这里一直到大规模模型，但
它确实允许我们从
当时强化学习社区所拥有的模型扩展
到更大的模型，
大约是
我们可以交易的模型的两倍，记住，
这一切都是在没有那么多
计算资源的情况下完成的，所以我们显然没有足够的
计算资源来训练非常大的
模型，但你知道一些比
我们当时做这项
工作时更好的东西，所以嗯嗯，在我进入
技术内容之前，我快速
设置了一些符号，然后我们再
讨论技术内容，所以请
记住，我们在这里处理的是
这个离线数据集或
已经存在的交互数据，
我们假设这些数据
由四个元素组成，所以这
四个元素是观察值 xay 或
学习算法 AI 可见的状态，这是
行为策略采取的操作，因此
数据集中的数据收集策略
rfx 是你的瞬时奖励
值，所以这是
你数据集中针对此
特定转换的奖励值，
XI +1 是下一个观察值或
下一个状态，嗯，这是
在
特定状态 xti 执行此操作时获得的，所以这只是
存在于典型重放缓冲区中的转换，
在非策略 Q 学习
风格算法中，所以我们可以访问以
这些转换 tobs XI AI R 和
XI 形式组织的数据集，好的，现在回到扩展，所以
开始激励为什么，或者你知道什么是扩展，
比如我是否应该将更多
数据放入更大的模型中，该方法
是否有效，以了解这个简单的
recip 是否有效，我们所做的是
尝试进行一个简单的实验，
我们说好的，而不是
做 Atari 游戏的标准领域，
我相信你们肯定在家庭作业中做过，而
不是为
单个 Atari 游戏训练策略，如果我们尝试使
问题更难，以至于
需要更大的模型才能
执行，
我们现在使这个问题更难了，而
不是为一个游戏训练一个策略，
我们正在训练一个
策略来同时玩多个游戏，
所以这只是一个简单的
小步骤
这个问题的实例化，简单地尝试训练
一个策略来同时玩多个游戏，
嗯，现在这种放大
实际上会给你带来相当大的
复杂性，所以你现在需要一种
能够处理或训练大约
20 亿个数据点的方法，因为你可以
连接每个游戏的所有数据集，
你可以轻松获得 20
亿个数据点，嗯，所有
这些数据都不是最优的，所以你
希望能够利用这些数据，并
制定一个好的、更好的策略，
希望比你
在这些数据中看到的任何策略都要好，所以这是一个具
有挑战性的问题，事实证明，如果
你解决这个问题，嗯，并对
一些非常简单的 U RL 和
控制方法进行基准测试，你会发现，当
你简单地进行模仿学习或
过滤限制学习时，
当你扩大模型规模时，你实际上会获得相当多的好处，
所以这个图绘制了
当你从较小的模型过渡到
较大的学习算法模型时的性能提升，
你
在这里看到的蓝色条是一种监督学习风格的
算法，它基于
模仿，嗯，你会看到，当你
从较小的模型过渡到较大的模型时，性能
会有巨大的提升。嗯，
在
这种特定
情况下，使用模仿学习风格的方法，性能会有巨大的提升。但是，如果你使用现成的
离线强化学习，在这种情况下，它是
保守的 Q 学习。嗯，我没有在
这里列出它的等式，但因为我
知道你们已经在讲座中讲过了。
你会发现，实际上，使用
离线方法，
由于
扩展模型容量而导致的性能提升幅度相当低。
嗯，事实上，如果你经常这样做，
当你使用更大的模型时，你的性能甚至可能会开始下降。对于
那些对
细节感兴趣的人，这里的模型基本上是
从较小的重置过渡到较大的
重置，因为嗯，这些游戏要求你在
像素图像上进行训练，并将
其转换为离散动作，所以你可以使用
重置架构。在这种情况下，这是非常
清楚的。嗯，你知道
这里可以看到的是，当你为离线强化学习方法扩展模型容量时，
训练算法的性能或
你获得的策略的性能开始
下降，
而
这在
模仿学习中并非如此。 正如你在上面看到的方法，
嗯，这是一个
违反直觉的发现，我们不
知道该怎么做，嗯，所以我们试着
问一些基本问题，这些问题
可以帮助我们理解为什么
监督学习方法可以
很好地扩展，而强化学习方法可能不行，
所以特别是嗯，你知道，
而不是去想为什么强化学习
或离线强化学习方法
很难在大型
模型上工作的绝对原因，我们试着做一个比较
理解，为什么监督学习
方法可以很好地扩展，而离线
方法可能不太擅长扩展，
所以我们试着做一个比较
分析，希望如果我们
能理解为什么存在这种差异，
我们就能在运行这些离线 ARS 时以某种方式
弥补我们的学习算法的差异，
所以如果你看到嗯，
理论机器
学习文献中的一行，他们提出了一个
解释，为什么对于非常大的
模型，非常大或
过度参数化的模型，这意味着有比
数据固有维度多得多的参数，为什么
即使在这种情况下，监督学习
方法也可以工作得相当好，
这就是呃这个理论
隐式正则化，这个
想法就像你用
监督学习训练一个模型，
假设一个
模型有参数Theta，你
最小化某个损失函数L Theta，嗯，
这里最大的假设是
Theta非常大，所以你有很多
很多参数，这意味着你有一个
非常大的模型，你
用你的数据训练这个模型，
如果你用梯度下降法
，更具体地说是
监督学习中的随机梯度下降法，
你最终找到的
解决方案不仅仅是最小化
损失L Theta，而且最小化
这个损失的正则化版本，所以你
最终找到最小化L
Theta加上R Theta的方案，而这个R Theta
不是你在训练期间添加的，
而是一个所谓的
隐式正则化器，它是一个正
则化器，因为你的
学习过程，在这种情况下，
随机梯度下降最终会
在你的参数分布图中优先选择某些解决方案，
所以这样想，有
很多可能
你正在学习并收敛到的解，
但你最终会找到
最小化这个正则项的解，因为
在这种情况下，你的优化器 SGD 最终会
找到这些解，而不是其他
可能
解。所以，隐式正则项
的一个直观特征
是 U，
它倾向于
简单的解。所以，如果你处理一个
非常大的、
参数化的线性回归问题，
假设它是一个监督学习
问题，最终通常会发生什么情况
是，你最终会找到具有
低参数范数的解，
你正在训练的线性回归参数的范数也很低。
这就是隐式
正则化现象的一个例子。所以你会
找到某种程度上
简单的解，这些简单的解往往具有
很好的泛化能力，即使
你的训练模型中有很多参数，
嗯，你知道，你知道在你的模型中，
这就是
为什么你仍然可以
通过监督学习找到好的解决方案，
即使你有一个非常大的模型，
没有过拟合，也不会遇到
任何其他问题，因为这个
隐式正则项最终会找到
好的解决方案。 顺便问一下，这里有任何问题吗？
嗯，我意识到这可能是
这门课没有讲到的内容，所以任何解决方案，这里有任何问题，
实际上，好的，嗯，太好了，所以是的，所以有
一个答案可以解释为什么
监督学习方法可以
很好地应用于大型模型，那就是
这种隐式正则化，
最终会得到
表现良好的解决方案，
所以我们在这一行中尝试做的
就是现在回过头来
看看这个理论对于离线强化学习的情况是怎么说的，
所以我们的希望，正如
我之前所说，如果我们能
找出离线强化学习和
监督学习之间的差异，我们就可以希望通过从
监督学习在
大型模型中表现良好的原因中获得见解，来解决离线强化学习和滑板的这个问题，
所以我们所做的就是，我们试图从
理论上推导出
隐式正则化器在
离线情况下的样子，特别是我们正在
考虑
训练 Q 函数的方法，在这种情况下，所以
你有一个新的网络，它产生
一个 q 函数作为输出，以状态作为输入，
我要在这里定义一个动作和一个符号，我
将考虑
这个网络的最后一层特征，这个网络
的最后一层激活是
X的feta，所以它们是
你学到的特征，这可能在
某种程度上是采用
不同的方式，但为了便于
理解，今天我们继续讨论这个
定义，我们称之为
特征，所以事实证明，在
强化学习的情况下，
监督目标和Q
学习目标之间存在很大差异，这导致这种
正则化非常不同，这种
正则化效果非常
不同，在强化学习和监督学习中表现不同，
为了理解
这种差异来自哪里，我将在
这里写下几个方程，
通常当你用Q学习进行训练时，
你会取一个q函数，一个网络Q Theta，
然后你尝试将它回归到一些
目标，这些是
你的监督
学习网络的目标值，你知道这些
目标值只是常数，对吧，
它们就像 想象一下一个回归
问题，你只需要回归到
一些常数值，假设在这种
情况下它只是对给定
问题的奖励，但对于Q学习或
离线ARL，这些值实际上是
用你之前训练的Q函数的副本计算出来的，这个Q函数
位于目标
函数的左侧，所以在某种程度上，你的
目标值也取决于
你正在训练的Q函数，而
你正在训练的Q函数会影响
后续的目标值，从而形成一个
循环，这将使
事情与
监督学习的学习动态非常不同，在监督学习中，
你试图通过回归来修复
目标，因为这个差异
实际上，嗯，如果你写下或者
计算我所说的这个正则化器，
它最终
会与Q学习或
离线和标准监督
学习非常不同，所以对于标准监督
学习，举个例子，你知道在
某些建模假设下，你可以
证明正则化器最终会更
喜欢具有低n和
低 特征 n，所以你的正则化器是
一个函数，它惩罚
特征的范数。5 Fe 来自你的
网络，嗯，这实际上很好，因为如果
你考虑像权重衰减这样的东西，
你知道它们在动机上非常相似，
这有点像权重
衰减，但不是针对权重，而是针对特征，
另一方面，对于离线，
现在你有一个更复杂的
正则化器，嗯，特别是有
两个术语，我们稍后会讲到，
但这个正则化器
与监督学习不同，
原因是由于这个回归
到目标
PRS，所以这个 RL 正则化器的第一个术语
实际上是相同的，它是一个
试图惩罚
你正在训练的特征的范数的术语，所以
这在某些方面是一个很好的术语，因为
你知道你正在尝试学习低范数
特征，你不会试图以
奇怪的方式对你的数据进行过度拟合，但是
这个第二个术语现在嗯，它是一个
实际上嗯的术语，如果你
更仔细地看一下，它看起来像
点积 b 介于 XI 处的 5 个 Theta（即
当前步骤的状态）
和 XI + 1 处的 5 个 Theta（
即下一步的状态）之间，所以它就像
某种东西，将给定
状态
动作 X 的逗号 a 与 X 素数或
下一个状态 x i + 1（在本例中）的特征结合在一起。如果
您考虑一下，虽然推导过程还
没有讲到，但如果您考虑一下，
这恰恰就是为什么
您可以从目标值之类的东西中得到的东西，
因为
目标值在 Q 函数下有 x i + 1 排队，而
上面的 Q Theta XA 在
您的当前状态和当前 F 上排队，所以
这就是这个术语的来源的直觉。
但是现在，
如果您看一下这个术语的作用，它实际上看起来像两个向量之间的点积，即 XI 的 5et Ai 和 XI + 1 AI + 1 的 5 thet 之间的点积，并且在这个前面有一个负号，嗯，记住，如果您
回想一下之前的等式
嗯，在这里，
当你对那个损失运行 SGD 时，你试图最小化正则项，
所以等效地，在这里你
将尝试最小化 RL 正
则项，最小化那个正则项，这
对第二
项意味着什么？它基本上意味着我要
最大化第二项，因为
前面有一个减号，呃，这是什么意思？
基本上意味着我要最大化
XI Ai 和
XI + 1 ai + 1 处特征的点积，这意味着我
实际上要尝试增加
这些向量的长度，因为
最大化两个向量点积的一个简单方法是
增加它们的长度（如果它们的长度
没有限制）。所以在某种程度上，这个
第二项
与第一项相冲突，
第一项试图降低
特征的幅度，第二项
试图增加
这些特征的幅度，嗯，在这种情况下，你知道，
因为 XI 中的 5 个出现在两个
项中，所以
这个 R 正则项
与监督学习相比有一些明显的区别 学习正则化器，
你在上面看到的，呃，有什么问题吗？
我想我口头上做了一些
推导，所以有什么
问题吗？是的，这就像
隐式正则化大约
等于什么，就像实际上基于什么一样，
这是一个很好的问题，所以你应该
看看这篇论文，
呃，它实际上是基于呃，所以记住，
我们在这里处理的是随机的，从
某种意义上说，它隐含着一些噪音，
所以你不能写下
精确的正则化，但你可以说，
整体正则化效果是在解
周围的一个波段中，
如果你要最小化
这个波段，你会得到这个解，这个波段取决于，你
知道，这个
波段的音量取决于学习
率和其他
由噪音决定的东西，谢谢，是的，
好的，呃，太好了，
所以，总的来说，
你知道rizer有两个术语，其中
一个术语现在开始与
你通过
监督
学习获得的学习相冲突，好的，所以如果这是正在
发生的事情，嗯，你知道，我们有
明确的理由 好吧，
监督学习正则化器实际上并
没有那么有害，因为超级
学习方法显然可以扩展，
而在 R 中，我们发现这些方法，
或者至少我们的经验结果表明
这些方法没有扩展性，那么
我们能做些什么呢？所以我们在这里采取了一种非常
经验主义的方法，而
这部分实际上不是
我可以证明的，但
经验主义的方法非常简单，它
说的是，如果这个第二项是
强化学习中唯一存在的其他项，而不是监督学习中存在的其他项，
并且这与强化学习
至少到目前为止扩展性不太好
而监督学习可以扩展的事实有关，
那么如果我们只是尝试撤消这个项，如果我们只是尝试通过
在我的训练算法中添加一个显式损失来删除这个项，从而
撤消这个第二项，那么如果我只是
简单地将这个项加回去，这样
我的正则化，净正则化
在监督学习
和强化学习中看起来是一样的，我们就是
这样做的，所以我们所做的就是我们
简单地采用了 q 学习算法（在
这种情况下是任何离线算法），
并将第二项作为
在本例中，将正则化器添加到我的训练方法中，事实
证明，仅仅在我们讨论的这个特定示例中这样做，就
帮助我们提高了可扩展性。所以，
你知道，这些离线方法最初会
随着模型容量的扩大而降低性能。
现在，如果你将这个
正则化器添加到训练中，并运行
相同的离线算法，你
实际上可以从模型容量的
增加中受益。所以，这是通过
扩大
模型容量而实现的性能提升，百分比提升。你可以清楚地看到，
它现在看起来非常类似于当今
监督学习的性能扩展方式。
不仅如此，如果你
看一下
不同方法的绝对性能，那么这些
方法包括模仿学习，
你知道基于决策 Transformers 的最佳先验方法，
这是一种
过滤或花哨的模仿，
以及
从数据中重放轨迹的方法。
与所有这些相比，
我们发现，将
正则化的思想与保守的 Q
学习（一种
基于它的离线方法）相结合，可以提高行为策略的平均性能。 帮助我们
在这个特定的基准测试中获得了更好的表现，
事实上，这是
第一个基于离线 R 的方法，它
基于 Q 学习，
在这个特定任务上使用足够大的模型来超越行为策略的性能，
所以这里的高级直觉
就像即使是
像 Atari 游戏这样简单的任务，
这对 RL 来说是如此标准，但当
你考虑扩展时，它们会非常困难，
如果你只是将它们组合
在一起，并且令我们非常惊讶的是，
没有现有的方法能够
训练足够大的模型，即使
在单个游戏设置中使用人们在这个领域发现的所有最佳调整，
嗯嗯使用离线 AR，
但有了这个正义正则化器和
完全相同的其他设置，我们能够
在这个设置中使用足够大的模型获得更好的性能，
好的，有什么问题吗，
是的，这是个好问题，嗯，所以我们
确实尝试了一些小模型，嗯，我想我
会说，
嗯，嗯，所以这取决于
你使用的大小，所以如果你
玩一个游戏，你使用一个
常规网络，我认为它确实有帮助，但是
如果你玩很多游戏和一个
常规网络 它并没有太大帮助，就像
常规网络一样，意味着一个
足够小的模型，所以我认为这
有点难以准确描述，但是，
原因是当
模型容量
大于数据的初始容量时，很难精确量化，
所以我认为
当你有一个足够大的模型时，这更可靠地提供帮助，
但这很难量化，
或者如果我要做
反事实分析，我认为我很难在
模型容量超过数据的地方划出界限，所以就是这样，是的，
这是一个很好的问题，所以我
认为我们从未发现它对
性能有害，嗯，还有
一个细节我没有在这里涉及，那
就是，如果你加回这个项，你
可能在那个超参数中有一个等待项，
它是一个正则化器，对吧，嗯，
你也可以用
一种方式来实现它，你可以把它用作
模型中的规范化层，
这是我
在这里没有涉及的，我稍后再讨论。
呃，在这种情况下，你没有超
参数，它不会有帮助，呃，它不会
损害你的表现，所以
如果
没有发生任何事情，它会很中性，或者只有当事情进展得更好时，它才会改善，好的，好的，好的，好的，
让我们
继续，所以本节的要点是，
你知道，大型模型，如果你只是
实例化离线RL，它们实际上可能会
造成伤害，但是你知道这些
简单的正则化方案或
等效方案可以消除这些不好的
部分，在这种情况下是隐式
正则化，或者更一般地说，我认为
任何解释
监督学习
和离线R风格目标之间的区别的东西，消除
不好的部分，你知道，
从这样的分析中得到的术语已经可以帮助
我们获得可以
很好地扩展的方法，在这个例子中，嗯，
好的，这就是这部分的结束，
嗯，现在让我们继续下一部分，
嗯，到目前为止，我们讨论了
扩展，对吧，所以我们在一个我们
正在考虑
Atari游戏的环境中，我们仍然拥有所有
看起来像转换和
动作的数据， 奖励一切都给了
我们，但现在我们想讨论一个
不同的问题，当
你有任意数据时你能做什么，所以如果
你正在学习
算法，它不仅仅看到数据以
转换的形式组织起来，还可以看到
其他东西，为此，我将
讨论一个使用人类视频的特殊情况，
那么你如何用
人类视频对机器人进行预训练，所以在
我进入人类
视频之前，让我们看看
基于离线 RL 的预训练或
机器人预训练方案是什么样的，
它看起来是什么样的，所以如果
你只有机器人数据，
你会用离线 RL 来免费
训练婴儿车，一个简单的方法是
获取一堆机器人数据，你
可以获取已经
在各个实验室收集的数据，你可以，
这些数据可能来自人类
告诉操作员收集，你知道
为你收集推出的数据，你可以
获取这些数据，你可以简单地
注释最后的状态 每条
轨迹或每次滚动都会获得
额外奖励，因为请记住，这些
都是人类实际演示
如何解决任务的演示，你
现在可以简单地获取这些数据并在
其上运行离线增强现实 (AR)，所以这只是巨大的
多任务数据，你可以将其放入
重放缓冲区，进行任务 ID 调节，
这样你就可以为每个特定的
转换定义它来自哪个任务，作为一个
独热向量，然后从
另一个向量运行，一旦你有了这个，你就知道
你将拥有一个通用的网络
策略，这样现在
当你获得一些给定
数量的数据时，这些是针对
你关心的目标域任务的目标域，所以这些
可能不是你关心的任务，
这些是仅存在于
数据集中的任务，当你拥有一些
你关心的任务的数据量有限时，你
现在可以混合一些
来自预训练的批次和这些新数据，
然后简单地微调这个模型，
所以这是一个非常简单的基于离线 AEL 的
预训练机器人
策略的方法，获取一些广泛的机器人数据，通过离线 AEL 对该
数据进行预训练，
然后继续运行相同的离线
RL 算法或输入，你可以
如果你愿意的话，也可以进行在线微调，
虽然这与
股票的这一部分不太相关，但你可以采用
这种离线方法，并
在测试时对你的任务特定数据运行该方法，
那时你应该使用这个
预训练
初始化，所以这是一个非常
通用的预训练方法，Vi
离线 AR，到目前为止，它只使用机器人数据，
它有显示动作的数据，它有
显示奖励的数据，它
以投影仪的形式组织，
但对于这一部分，嗯，我们
想要做的是，我们想要采用这些
方法并将其扩展到使用人类视频，
为什么我们要使用人类
视频，如果你考虑一下所有
存在的数据，嗯，即使
你包括来自其他机器人的多任务机器人数据，与
YouTube 上或精心
策划的数据集（如 ego 4D）中存在的人类视频数据相比，它也非常小，所以
利用人类视频数据是有意义的，
因为它展示了人类如何
与现实世界互动，这
对机器人控制很有用，但
现在出现的挑战是，大多数
存在的视频数据中都
没有动作，而人类
不是机器人，对吧，人类有五个手指，
或者机器人不是人类，人类有
五个手指，典型的机器人大多
在平行地理学家的指导下运行，所以
在
能力和具体化方面存在巨大差异，
那么如果我们
得到这样的数据，我们能做什么呢？我还能继续
在这些数据上运行离线 R 吗？嗯，答案
并不十分明确，但在这项工作中，
我们所做的就是，我们展示了你
仍然可以继续运行离线 R 方法，
此时它们不是学习策略，而是
学习有用的
表示或特征，所以事实
证明，同样的 Q 学习价值
学习算法，我们
到目前为止讨论过的算法，它们仍然可以为你提供
有用的特征或表示，用于
现在进行机器人控制，如果你
愿意在视频上进行预训练，然后
继续在机器人数据上运行相同的方案，
那么这是什么样子的呢？
这里有一个你
可以遵循的通用流程，你可以首先
从所有现有的视频数据开始，
在数据上训练某种价值函数，我将讨论什么是
价值函数，这个价值
函数将为你提供一个有用的图像编码器，
或者 将视频帧转化为
表示，所以一个有用的编码器可以将
信息和图像压缩
成有用的向量，现在你可以用
这个编码器来
初始化一个现成的
离线 R 算法，用于预处理机器人，所以
你可以在
这个编码器之上拥有 q 值和策略，
这样
你获得的策略就可以
在你想要的任务上找到你，
所以记住
我之前展示的预训练方法，在 Broad 机器人上训练，
然后在目标数据上找到你，你可以在
它之前添加一个新的阶段，现在
用
视频训练你网络中的视觉编码部分，这里的最后一个函数
只是简单地训练一个价值函数，
它不需要动作，因为
价值函数只是
你的状态或观察的函数，而不是
动作，在这种情况下它不是 q 函数，
好吧，嗯，
现在的问题是，为什么
这会给你好的特征，对吧，为什么
这会给你有用的编码，
嗯，答案是，如果你
用价值函数训练你的视觉编码器，你就
隐式地考虑了
世界的动态，价值函数 当
你用 bman 训练它们，并
用 bman 方程进行备份时，它们会考虑
世界的动态结构，即序列结构，这
大概很有用，因为你
训练的是类似的对象，也就是 q
函数或策略，
所以我们希望
使用价值函数进行训练
能够为你提供有用的表征，
即使你没有
在视频
数据上采取任何行动，也能为下游策略提供有用的特性。在我深入探讨如何
训练这些价值函数之前，我想
暂停一下，看看大家是否有任何
问题。
是的，这是一个很好的观点，我
稍后会讲到。
嗯，嗯，所以现在的重点
是，好吧，你知道我谈到了
价值学习的所有直觉，
但是你能做什么呢？
你可以
在视频上训练什么样的价值函数？对吧？一个简单的价值
函数，从技术上讲，看起来像一个
状态函数，它会告诉你
通过执行给定策略可以获得的一些累积奖励，在这种
情况下，对于给定的奖励函数，
价值函数是 Pi。 总结给定
奖励函数的累积奖励，
现在，在这种情况下，我可以
选择定义某种奖励
函数，可能的话，我可以说，
我的奖励函数是，每当
人手靠近某个特定物体时，都会产生一些特定的
函数，
但在这里不太好，因为
它仍然会给我策略，或者
仍然会给我
非常特定于特定
奖励函数的价值函数。我定义我可以选择在
这里定义任意的奖励函数，
但它们将非常特定
于一种特定的能力，而在
下游，我的机器人
需要执行许多其他
机器人无法完全覆盖的任务，所以我想以某种方式摆脱
这种特定的价值定义方式，
我根本不想使用奖励
函数，因此，为了做到这一点，
我可以做的是，我可以尝试
采用价值函数的一般公式，
例如黄金达到价值，我
可以说，我想为视频数据训练一个价值
函数，
告诉我，
如果我通过执行给定的
策略来达到任意
帧或达到视频中的任意目标，我将获得的总价值是多少，
这是 一个非常通用的
奖励函数，因为它是一个
目标达成奖励函数。我可以将
目标定义为视频中任意的帧，
即使这些
帧跨帧出现，它们根本不出现在同一个
视频片段中。但这最终也会变得
非常具体。之所以如此
具体，是因为它只
考虑一种策略。我只
训练一种策略的价值函数，
这意味着我只学习
能够代表
一种特定策略Pi的价值的特征，即使
在这种情况下它适用于多种奖励函数。所以
我也不想
这样做，因为这太具体了。所以
显而易见的下一步是，
如果我训练这些可以很好地
适用于所有策略的目标达成价值函数，那么我会想
训练一个价值函数，它可以代表所有可能策略的价值函数，我想
训练这个网络，它可以代表
所有可能策略的价值函数，
并且针对所有可能的目标，
在这个空间中，所以对于所有
可能的目标达成奖励函数，
这最终会变得过于宽泛。
原因是，当你想到
机器人时，它们不会操作，也
不会运行任何
随机动作序列。
他们要做的是
运行一些非常具体的操作来
完成任务，而
不是任意的随机操作，而
这个特定的公式
仍然会考虑这些操作，因为它会为
所有可能的策略训练价值函数，
所以在这个例子中，我们最终做的
是一个稍微不同的
公式，我们现在建模一个价值
函数，它只模拟
你
达到特定目标所能获得的总奖励，
当你的策略是最优策略时，也就是
你关心的某些子任务的最优策略，想象一下，如果
你只有最优目标达成
策略，也就是说，给定一个目标，这些
策略可以尽可能高效地达到这个目标，
你只需要
为所有目标
达成奖励
函数的特定策略建模价值函数，所以在这种情况下，
奖励函数的选择是每个目标达成
奖励函数，而策略的选择
只是
在某种程度上有用的最优策略，而不是
任意的随机策略，所以这达到了
正确的平衡，或者至少
从经验上来说，这在
广度和深度以及
我们正在
训练的价值函数的特异性之间达到了良好的平衡，嗯，我鼓励你
查看更多作品，所以这个
实际上是受到了这项工作的启发，嗯，
这里的第一个工作，嗯，它实际上将
其建模为一个意图条件值
函数，嗯，但你应该检查一下 t
了解更多细节，还有很多
其他相关工作，
但总体思路是，你想
选择一组奖励函数和
一组策略，为它们建模，
这样你就可以得到很好的特征，这些
特征对广泛的
下游任务都有用，但它们的范围不会
太广，也不是任意的，
也不是特别有用，这
回答了你的问题吗？是
的，好的，嗯，所以我们所做的就是我们
简单地使用了这个价值函数，你
知道我们把它放进了这个管道，
所以你的价值函数现在是这个价值
函数，用于达到 OD 黄金目标的任务，
对于某些最优策略，你得到
你的视觉编码器，把它扔进
离线 RL 预训练流，所以现在你
用这个视觉编码器初始化你的离线方法，
你得到了训练器的 Q
函数和策略，在这种情况下，它是
保守的 Q 学习，嗯，现在
你可以准备好进行
初始化，在你的目标任务上找到你，
你将在这个任务上衡量
性能，好的，嗯，是的，所以在
我讲一些机器人视频和
定量评估之前，事实证明，
这个主题实际上为
你提供了有用的功能，所以如果你
绘制轨迹图，例如，如果您要从
数据集中获取轨迹，以及
数据集中分布不均匀的轨迹，那么如果您
有一个机器人数据集，您可以从中获取一些
轨迹，也可以获取一些
不包含在数据集中的轨迹，如果
您将学习到的值绘制为
时间步长的函数，则
轨迹中会出现特定状态，因此，请将其视为
特定状态出现在您的
机器人
数据中的时间步长，y 轴绘制的是
该状态的值函数或 Q
函数，在这种情况下，您会
发现 Q 值要
好得多，那么您如何解读这个图呢？在
这种情况下，Q 值，地面
FR 的 Q 值实际上应该随着
您进一步深入而增加，因此它们应该
始终具有增加的趋势，
即随着
x 轴值（时间步长）的增加而单调增加，在
这种情况下，我们发现，您知道我们使用
视频的方法（第一
列），因此 vptr 最终学习了
这些值函数，这些函数
与单调
增加非常一致 即使在 OD 数据或
输出分布数据上，趋势也会有一些
峰值，但这比
完全不使用
视频数据进行训练要好得多，这是这里，
还有其他使用视频数据进行训练的方法，这
是另一个，所以在这里你可以看到，
当你增加 x 轴值时，值具有这种非单调形状，但在
这种情况下，使用 r 方法，它只会
增加，所以它确实学习了有用的
特征，这些特征非常适合表示
机器人数据上的下游值，
你也可以定量测量
它，这样你就可以实际测量
地面真值函数和
你学到的值之间的某种均方误差，再次你会
发现，使用这种方法
对视频进行训练确实有效，并且与本例中的其他方法相比，它能提供
最小的均方误差，
所以总的来说，这些特征
对于学习下游
值
函数非常有用，当我们在
真实机器人上进行一些实验时，
当我们采用这些策略时，在给定的
目标任务上你会发现
这些策略推广得
很好，嗯，你知道，
在对象和 gper 可
变性方面推广得很好，我想我需要 播放
这些视频，
好的，我想我可能需要
从这里开始播放，
嗯，是的，所以我的意思是这些策略最终的
表现
比以前的方法要好得多，也更稳健，嗯，好的，
我想，嗯，
好的，所以嗯，是
的，所以这里的其他方法嗯，
你知道前三种基本上是
在视频上进行训练的不同方法。PDR
只是不使用视频，只使用机器人
数据，这是我们的方法，使用视频
和机器人，你可以看到，与
其他
现有的在视频上进行训练或根本
不使用视频数据的方法相比，这种方法通常比稳健得多，
嗯，当
场景中有其他干扰物体时，干扰物体也是如此，嗯，
在这种情况下也是如此，
嗯，从定量上讲，你可以做一个
全面的评估，我们发现，
实际上使用视频和我们的
方法训练这些价值
函数实际上比
不使用视频（这是本专栏）或
使用视频的其他方式要好，这些
都是来自 PRI 的不同方法，
嗯，你知道这些不同的方法
使用像视频上的自我监督学习，
所以 你像一个
AM自动编码器那样进行训练，
通过简单的压缩并
尝试重建
视频帧中的图像来获得一些有用的表示，或者你可以进行
某种对比学习，所有
这些方法实际上的表现都比什么要差得多，嗯，好的，有
什么问题吗？
好的，我想我有9
分钟的时间，嗯，让我们看看
在第三部分我们可以涵盖多少内容，第三部分
是关于利用离线
预训练初始化进行微调，
嗯，这将
更具算法性，所以我很乐意
在高层次上讨论一些事情，
然后我可以在
6点之后回答问题，因为我
认为我们需要在6点离开房间，
但是，这部分是基于一篇
名为cql的论文，
它将在下个月的欧洲会议上发表，
所以我们
关心的设置是在线改进
离线RL策略，所以如果我给你一些
数据，你可以在其上运行离线RL，这
是巨大的预训练数据，所以你可以得到一个
预训练策略初始化现在我
想通过与给定感兴趣的任务进行有限量的在线交互来改进这个策略，
所以我想通过有
限量的在线主动
收集
交互来专门针对该任务来优化这个策略，所以在这种情况下，如果你
要进行第一次尝试，嗯，说
一个解决这个问题的简单方法
是简单地采用
正在运行的相同离线 R 方法，然后
继续使用进入重放缓冲区的新在线或策略数据运行它，
如果我这样做，你会
发现大多数方法类的
结果都会显示两种不同的趋势，嗯，为了理解
这个图的含义，我解释一些
符号，所以我在
x 轴上绘制了在线发现
期间收集的环境样本数量，
所以离线预
训练已经完成，现在你
只需收集新的在线数据来
改进你的策略或完善你的策略，
y 轴绘制性能，
在这种情况下，它是得分或回报，它们都是
相同的，并且
性能零开始很高，
因为你已经完成了离线免费
训练，并且你的策略取得了一些不成功
的结果 所以你的策略成功率大约是 50%，
我正在绘制两种算法：
iql 隐式 Q 学习 cql 保守
Q 学习。我想这两种
算法我都已经介绍过了，
这些算法只是在
你开始收集在线数据时运行，
在线数据被放入
离线算法的重放缓冲区，所以它
应用到 TR 的数据集，
所以你可以清楚地看到
这里发生了两种事情，首先是 cql 或保守 Q
学习，你会看到在
find 时性能会大幅下降，所以你的 find
模型最初得到的结果
大约是 0.5，它最终经历了一次大幅
下降，然后恢复过来，最终获得了
整体的改进，而
对于 iql，你会
发现改进是
一致的，但速度要慢得多，
斜率不是很高，最终比 cql 要低得多，
所以显然
这里有些不对劲，因为
理想情况下我期望的曲线是
超越两者的 这些
曲线改善得相当快，
尽快达到最佳性能，
但嗯，它
也不是很慢，没有
任何问题，所以根据这个观察，我们说
这里出了问题，我们
想更深入地
了解为什么这种行为会发生在
这些不同的方法上，以及
我们可以做些什么来构建一个更好的
函数
算法，所以呃，具体来说，当你把
自己限制在 cql 的情况下，
我们所做的就是试图理解
这种反学习现象，我们试图
理解为什么这种性能会发生在，
为此，我们绘制了
一系列指标，嗯，其中包括很多很多
指标，并非所有指标都在
这里分开，但我们发现一个
与性能下降非常相关的指标
是平均 Q 值，所以如果你拿出
你的数据集，其中包括离线
和在线数据，现在你
在该数据上绘制 Lear q q 网络的平均 Q 值，
你会发现这个 Q 值有一个
急剧的修正阶段，在这个阶段，它的
幅度恰好在
回报出现这个下降和
回报出现这个下降的同时发生变化，
所以这是一种初始
信号，表明这
反学习与
这两个值的变化和幅度有关，你
看，嗯，这是精确的，这
很像一个调整和缩放，所以你
的值是40，现在你的值
在训练时变成了-10，
所以我们所做的就是试图
理解为什么会发生这种情况，我
想首先给你一些直觉，
这有点不精确，但它完成了
直觉的工作，然后我会向
你展示一些具体的证据，
所以如果你想想cql做了什么，
你知道我想解释为什么
这些Q值非常低，以及为什么
它们会进行调整，所以第一个问题
为什么这些值非常低，如果你
看看cql做了什么，它基本上
训练你的Q函数来拟合目标
Q值，也就是这个时间
差分误差项bman误差，它会
下推外部分布上的高Q值，
在这种情况下，TD项是
一个相对损失，它说的是你的Q
值必须符合目标Q值，而目标Q值
来自
同一函数的先前快照，
而这个和这个
特定的期望值仅通过计算
你的数据集并不是
针对所有可能的状态动作对进行计算的，
因此最小化损失函数会给
你唯一的最优函数，它只
针对有限的样本集进行计算，
所以如果你要绘制
特定状态下的 Q 值作为
动作函数的图表，假设现在的动作是一维的，
并假设这是
基础 TR Q 函数，你最终会
看到许多不同的 Q 函数，
许多不同的学习到的 Q 函数（
以红色显示）可以达到相同的 PDL，它们都会
达到相似的相对损失，
因为你知道它们与自己的
目标值有相似的
差异，但在这种情况下，cql
不会
找到任何这样的函数，它会找到尽可能
最小的 Q 值，具有
尽可能最小 Q 值的 Q 函数，从而
达到相同的
gen，这是因为你添加的这个正则化器
是一个绝对损失，
它只是简单地最小化，
所以这里的高层次要点是
这些 Q 值
非常低的原因是因为许多解决方案
都可以具有相似的
形状，这意味着 Q 各个
动作的值看起来相似，嗯，cql 或任何
悲观算法都会找到
绝对值最小的那个，
在这种情况下，所以
当你有这个最小的可能的
Q 值时，它是最小的那个。假设你进行在线
数据收集，所以你要
查询一个新动作并
在现实世界中运行它，你
现在要收集数据，你
会看到
这个特定动作的实际奖励，当你根据
这些数据更新你的 Q 函数时，
会发生什么？你最终会引入
错误的峰值，这些峰值在
你的 Q 函数下看起来非常高，这是
因为当你在
到期期间执行这个动作时，你会看到
实际的地面 TOS 奖励值，这是你在
离线数据集中没有看到过的，
因为你的离线数据集实际上没有
显示任何这些，
所以考虑到这个 Q 函数，
你知道你的坏动作在
你的 Q 函数下看起来非常好，
这样现在如果你
运行你的策略优化，找到
这个 Q 函数中的峰值，你会
发现一个错误的动作，一个
实际上不是的坏动作 非常好，嗯，会
降低你的策略表现，所以
之前的策略是在
这个红色曲线中找到峰值，也就是
这个蓝色曲线的峰值，也就是
通过 Q 函数的底线，现在你的策略
将在这条红色曲线中找到峰值，而
不是蓝色曲线的峰值，这
是底线，所以很明显
发生了某种反学习，你已经相当
不错了，现在你不会
那么好了，因为你看到了一个
[音乐]
呃，我想我的时间非常紧迫，
好的，再等一分钟，好的，
任何问题，顺便说一下，
嗯，
好的，呃，太好了，是的，
所以基本上我们的
方法非常简单，它说，你
知道如果我们能以某种方式防止
出现这样的峰值，只需
确保我的训练过程，我的
离线训练过程永远不会找到
如此低的 Q 值函数，所以永远不会找到
达到
最小 Q 值的函数，就像我在这里看到的，如果我
要找到一个看起来像
这个特定曲线的函数，就像这个第一条
红色曲线，那么这些都不会发生，
因为我的 Q 值没有那么小，
所以看到这里的奖励值会
更新我的
函数，所以这正是 我们所做的，
我们说，嗯，与其
用 cql 进行训练，不如用
某种东西进行训练，以防止 Q 值
过低。我们这样做的方法
是，我们采用相同的
cql 算法，并施加一个约束，即
您正在学习的 Q 值
永远不应低于某个
参考 Q 函数，您可以
精确指定该函数，您只需约束
降低 B，使值始终高于
橙线即可。在这种
情况下，现在不会发生任何问题，
因为即使您
根据这些动作更新 Q 值，您也会
经历一个小的下降，然后又
回到那里，但这仍然
会低于模式，也就是
您训练的 Q 函数中的最高点，这里用红色表示，
所以不好的动作
看起来并不像前面那样更优，
事实证明，
对于这个 Q 函数，一个非常简单的选择，
这个参考 Q 函数就是
行为策略的 Q 函数，所以您可以相对轻松地计算
数据集或数据
生成策略的 Q 函数，而
无需
任何 Q 学习，或者所有这些加在一起，
只需计算 返回到 Go
估计值，并用它们来降低
图中 Q 函数的边界，
效果
很好。嗯，好的，我想我刚才得到了结果，
嗯，是的，所以结果是，
你知道，这在某种程度上缓解了
这个问题，所以现在你得到了曲线，
然后在训练过程中得到了改进，
没有那么
多的下降，嗯，并且比
其他方法更好更快，包括这些方法，
比如我第一次描述的 iql，
嗯，是的，然后你可以在
这里使用更多的梯度步骤，嗯，我想我
将跳过细节，但这将
进一步提高效率，嗯，
事实上，这会让你在训练过程中获得最小的
遗憾，最小的累积遗憾，
这是
衡量不同算法样本效率的标准指标，
如果你也感兴趣，
你应该看看
斯坦福大学最近发表的一些其他研究，它们
采用了这种方法，并将其
与 VMS 结合使用，
等等，嗯，你知道，实际上我们在
真实的机器人上做了这个实验，我们也在
真实的机器人上做了一些实验，我们
完成了这个微波炉门打开任务，
你知道我们尝试了 在线查找单元，所以
离线策略还可以，它
到达了抓手，但无法
打开它。如果你现在
用 gal 来做这件事，在这种情况下，你可以慢慢地
得到现在的行为，你知道，它真的会
触摸门，然后最终
在 20K 步内完全打开门，
所以这是完全在线的，策略
在线运行，收集自己的
数据并进行改进，但你
从离线
初始化开始，所以这里的反学习
非常重要，因为如果你反学习，
那么在 20K 步内，
你将无法恢复并得到
一个好的解决方案，但现在你可以，你
没有反学习，因为你在
不断改进，这表明了
这种方法的设计初衷，
是的，嗯，好的，这就是
结局，所以你知道
微调的挑战通常源于缓慢的
策略改进或最初的
反学习，你知道
解决这个问题的一个简单方法，并获得一条
看起来不像缓慢或未学习的曲线，
而是看起来像这条蓝色曲线是为了
校准比例 这些 Q 值
不应该
太低，就像 cql 能得到的结果一样，
嗯，这样就能得到最好的
算法，嗯，就像这条
蓝色曲线，
是的，基本上就是这样，嗯，我觉得
我有点快，但很高兴回答
任何问题，嗯，之后，是
的，看起来我们没有被
挑出来，这意味着我们还有一些时间回答
问题，是的，部分是说给出
一些隐式，这是否
适用于随机
优化器，
同样，是否有任何针对
RL 的特定优化器可以
更好地用于 CL regiz，这是一个很好的
问题，嗯，所以我不完全确定，
所以它通常适用于
随机优化器，是的，我个人
不太确定针对
RL 的随机优化器的具体工作，但我认为
这可能是一个很棒的
研究方向，嗯，我认为我
个人更大的问题是，嗯，
首先，我想你需要对
哪些确切的术语很重要进行描述，嗯，我
认为我们的是 可能是为数不多的几部首部作品之一，
它在任何 RL 设置中都具有类似的特征。当时设置还比较简单，有
一些假设，
但我认为你需要这些假设才能
推导出正确的优化器。
是的，我认为现在有一些关于
隐式正则化器特征化的工作，所以你
可以从构建新的优化器开始。
当然，
还有其他
问题吗？是的，
隐式正则化的两个部分
相同只是巧合吗？这是个好问题。嗯，是的，我
想我可能有点强调了，
所以那里有一个伽马折扣，
现在可能说得通了。
嗯，是的，所以你知道我
没有把它放在这里，因为我
根本没有引入伽马符号，所以
那里有一个伽马，但
基本上发生的事情是，嗯，你
知道如果你在这里玩了游戏，
还有其他
问题吗？让我们热烈
鼓掌，我们最后一位
嘉宾将于本周三与大家见面，来自桑福德的 D City
将谈论
交互式
学习。