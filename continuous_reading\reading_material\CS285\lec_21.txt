### [第一部分] CS 285 讲座 21：基于序列模型和语言模型的强化学习

好的，今天我们将讨论使用序列模型的强化学习。首先，我们来讨论一下，当我们超越常规的马尔可夫决策过程（MDP）时会发生什么。在课程开始时，我们看到，除了完全可观察的 MDP 之外，我们还可以开始考虑部分可观察的马尔可夫决策过程（POMDP），在这种情况下，我们只能对环境进行有限的观察。这将引导我们开始思考强化学习中的序列模型。

观察的问题在于，与我们在大多数基于价值或基于模型的算法中使用的马尔可夫状态不同，观察结果不遵循马尔可夫属性。这意味着仅通过当前的观察，您不一定有足够的信息来推断环境的完整状态，因此先前的观察结果实际上可以为您提供更多信息。相比之下，状态则不同。如果您观察到当前状态，那么了解先前的状态并不会为您提供更多预测未来的信息，因为当前状态已经包含了从过去预测未来所需的所有信息。所以，当您在部分可观察的环境中操作时，状态是未知的。实际上在大多数情况下，您甚至没有状态的表示。所以您不仅不知道当前状态是什么，甚至不知道状态的数据类型是什么。

回顾一下我们在课程开始时讨论过的部分可观察性。假设环境是一只猎豹追逐一只瞪羚，但你的观察结果是场景的图像。现在，观察结果背后是一些真实的状态，比如动物的位置、动量和身体姿态。状态完整地描述了系统的形态。如果你知道当前状态，它能告诉你预测未来所需的一切。这并不意味着未来是确定性的，未来可能是随机的。这只是意味着，如果你已经有了当前状态，那么先前的状态将无法帮助你更好地预测未来。

但如果你只有观察结果，那么这些观察结果可能是部分的。比如，一辆汽车在猎豹前面行驶，所以你看不到它。状态并没有真正改变，但现在的观察结果不足以推断出当前状态。如果你回顾之前的观察结果，你可能会得到更多信息。问题在于，大多数现实世界的问题都是这样的。我们讨论的很多算法都假设你有一个完整的状态，但并非所有算法都是如此，我们稍后会讲到这一点。但大多数现实世界的问题实际上并没有给你一个完整的状态，现实中，它实际上是一种部分可观察性。也就是说，所有问题实际上都是部分可观察的，你永远不会真正得到系统的完整配置。但有时部分可观察性非常微小，以至于你可以假装观察结果就是一个状态，一切都会正常进行。

例如，Atari 游戏就是这样。在很多 Atari 游戏中，即使它们在技术上是部分可观察的——因为系统的状态就像 Atari 模拟器的内存一样——实际上，屏幕画面包含了几乎所有的信息。但在某些情况下，它们就是明显部分可观察的。例如，如果你在开车，你的盲点中可能有另一辆车，比如这辆红色的车，你可能看不到蓝色的汽车或卡车，但它们与未来状态非常相关。所以在这些情况下，部分可观察性真的很重要。如果你在玩一个第一人称视角的电子游戏，游戏中可能有很多非常相关的东西，也许是你过去见过的，为了有效地玩游戏，记住它们非常重要，但在当前的观察中你看不到。

另一个部分可观察性极其重要的例子是与其他智能体的互动。如果你有一个应该与人类互动的机器人，人类的心理状态实际上是状态中未被观察到的部分。你可能会观察到他们说了什么或做了什么，但你不一定能观察到他们的想法、他们的愿望、他们的偏好，以及他们想从互动中得到什么。这是一个非常复杂的部分可观察性例子。

另一个部分可观察性的例子是对话。如果你的观察结果是文本字符串，那么它可能用于人际互动，也可能用于你正在与基于文本的游戏互动，或者甚至是像 Linux 终端这样的工具。在这种情况下，交互的历史记录确实很重要，而仅仅当前的短语（例如你看到的最后一个单词）本身并不能真正传达那么多信息。

这些都是部分可观察设置的示例。现在，部分可观察的 MDP 可能会真的很奇怪。嗯，我们可以通过稍微简化来使它们不那么奇怪，但是如果我们只是天真地对待它们，那么在部分可观察的 MDP 中会发生很多在完全可观察的 MDP 中根本不会发生的事情。一个例子就是信息收集动作。因此，在部分可观察性下，最佳做法可能是做一些本身不会带来更高奖励，但可以为您提供有关环境的更多信息的事情。

例如，如果您正在穿越迷宫，如果您只是将其视为完全可观察的任务，也许您的状态就是迷宫中的位置，您只需在这个迷宫上运行强化学习，直到您解决了这个迷悟空。那么一切都很好，最佳动作始终是朝出口移动。但是想象一下，你正在尝试解决一个迷宫分布问题，也就是说，你试图获得一个可以解决任何迷宫的单一策略。现在，如果你一开始没有看到整个迷宫，那么这是一个部分可观察的问题，如果你只是从第一人称视角观察，因为现在，未观察到的状态是你所在迷宫的配置。在这种情况下，最佳做法可能是从迷宫的某个高处窥视，并尝试观察所有交叉点的位置，即使这种信息收集行为本身并不能让你更接近出口。所以信息收集行为是在最优策略和 POMDP 中出现的东西，在完全观察到的 MDP 中永远不会出现。

另一种奇怪的特性是，部分观察到的 MDP 可以导致随机最优策略。而在完全观察到的 MDP 中，总是存在一个最优的确定性策略。这并不意味着所有最优策略都是确定性的，可能存在一个同样好的随机策略。但在完全观察到的 MDP 中，你永远不会陷入只有随机策略才是最优的情况。而在部分可观察的 MDP 中，这实际上是可能的。

这里有一个非常简单的例子。假设你有一个三状态 MDP，你可以处于状态 a、b 或 c，奖励总是在中间，所以状态 B 的奖励是+1。你在状态 a 开始的概率是 0.5，在状态 C 开始的概率也是 0.5。所以你有 50% 的可能性从左边开始，50% 的可能性从右边开始。假设现在你处在一个部分可观察的 MDP 中，你的观察结果不包含任何信息。所以本质上，在一个你根本没有得到任何观察结果的部分可观察 MDP 中，你基本上只需要承诺向左或向右的一个动作。而一个确定性策略必须选择总是向左或总是向右。如果它选择总是向左，那么如果它从状态 C 开始，它最终会到达好的状态 B，但如果它从状态 a 开始，它永远不会到达状态 B。如果它承诺总是向右，那么如果它从状态 a 开始，它会获得奖励，但如果它从状态 C 开始则不会获得奖励。因为这里的确定性策略必须是观察的函数，而观察结果没有信息，所以确定性策略的唯一选择是始终向左或始终向右。但是，如果你有一个以 50/50 概率向左或向右的策略，那么无论它从 A 还是 C 开始，最终都会到达 B。所以这是一个随机策略实际上比任何确定性策略都好的例子。

好的，现在我们可以问一下，我们之前学过的强化学习算法中，哪些可以真正正确地处理部分可观察性？现在我们必须非常小心地处理这个问题，因为“正确处理”它意味着什么？嗯，我们稍后会讲到这个问题。但是现在，首先让我们回顾一下不同的方法，然后我们将讨论这个问题。我将讨论三种方法：策略梯度，我们之前讨论过的第一个方法，它使用某种优势估计来构建策略梯度的估计器，使用我们之前看到的熟悉的梯度对数 Pi 公式。所以，在策略梯度下降中，我们能否只需用观察值替换状态，只需将观察值输入策略，并使用完全相同的梯度估计器？这是一个很好的问题。对于基于价值的方法，我们能否简单地取 Q 学习方程，然后将 S 替换为 O，这样做有效吗？对于基于模型的强化学习方法，比如最简单的基于模型的强化学习方法，我们训练一个模型，根据当前状态预测下一个状态，然后通过该模型进行规划。在这种情况下，我们能否简单地将 S 替换为 O？

当然，这是一个有点棘手的问题，因为在我们开始回答这三种方法中的每一种之前，我们必须理解“处理”实际上意味着什么，“正确处理部分可观察性”意味着什么。现在花点时间思考一下，你希望从这种方法中得到什么？假设简单地用观察值替换状态是有效的，你希望从正确运行的方法中得到什么，以及运行不正确的方法可能出现的问题。

在所有这些情况下，我们都会尝试获得一个关注观察结果而不是状态并产生动作的策略。如果方法正常工作，我们希望获得的是最佳策略。因为我们只能看到当前的观察结果，所以在具有三个状态的示例中，最佳策略是以 50/50 的概率向左或向右移动的策略。这是最佳的反应式策略。当然，你无法获得更好的策略。如果你获得一个非反应性的策略，即一个具有记忆的策略，你可以做得更好。但现在我们只是问这个问题，我们能否在我们所处的表征约束下获得最佳策略？这意味着在约束下，策略只能查看当前的观察结果。所以它是无记忆反应策略类中的最佳策略。除非我们真正改变策略类别，否则我们不能指望做得更好。现在我们不改变策略类别，我们只是改变算法并尝试直接用观察结果替换状态。所以，“处理”意味着在无记忆策略类中找到最佳策略。

好的，对于这个“处理”的概念，请花点时间想想看，我们是否可以通过简单地用策略梯度的观察值替换状态，从而获得无记忆策略类别中的最佳策略？对于基于价值的方法和基于模型的方法呢？

让我们从策略梯度开始讨论。所以我们很容易说，如果我们想要一个接受观察值并输出动作的策略，那么让我们使用相同的梯度对数 π 方程，然后简单地将 S 换成 O，这样对吗？有趣的是，我们对策略梯度的推导，回到课程的开始，从未真正假设过马尔可夫属性。它假设了分布分解，这意味着可以应用概率的链式法则，但这始终是正确的。它实际上并没有假设进入策略的状态将未来与过去区分开来。所以使用这个梯度对数 π 方程是完全可以的。但是优势估计器需要小心一点，因为有多种方法可以估计优势和策略梯度，其中一些方法可能会给我们带来麻烦，而其他方法则完全可以使用。

关键点是优势是状态 St 的函数。优势不一定是观测值 Ot 的函数。所以优势不依赖于 St-1，但如果没有状态，你可能会遇到麻烦。所以完全可以使用 R(t) 加上下一个值减去当前值作为优势估计器，并使用 V 的某个函数逼近器。因为当你训练 V 的函数逼近器作为状态函数时，你基本上是在利用这个属性：每次看到状态时，你都期望得到相同的值，无论你是如何到达这个状态的。所以这就是为什么 V-hat 只需要是当前状态的函数，它不需要考虑过去的状态，因为马尔可夫属性告诉我们，这个值只会是当前状态的函数，而不依赖于你是如何到达那个状态的。但这当然不适用于观测值。所以你不能简单地将 V-hat 的参数中 St 换成 Ot。所以训练一个依赖于 Ot 的 V-hat 是不行的，因为价值可能取决于过去的观察，因为当前状态可能取决于过去的观察。

所以这意味着，如果你要使用策略梯度，如果你使用常规的蒙特卡洛估计，即你只是简单地插入奖励的总和，这是可以的，因为这个推导实际上并没有使用马尔可夫属性。但如果你尝试放入一个价值函数估计器，那就不再可以了，因为优势函数中的价值函数估计器不是观察的函数，而是状态的函数，而状态取决于过去的观察。所以这种类型的估计器现在不行了。

作为一个突击测验，我建议大家想一想，在我们开始讨论价值函数估计器和基线之前，我们学到，我们可以简单地将那些奖励乘以梯度对数 pi，并使用因果关系技巧，将梯度对数 pi 乘以从时间 T 到结束的奖励总和，而不是从一开始到最后。当你具有部分可观察性时，可以使用因果关系技巧吗？花点时间思考一下。

好吧，我会给出答案。答案是，这实际上完全没问题，因为因果关系技巧也没有使用马尔可夫属性，它只是利用了未来不会影响过去的属性。现在，未来不会影响过去，即使你在部分可观察性下行动，所以这实际上是可以做到的。事实上，可以通过证明梯度对数 π 乘以过去时间步长的奖励的期望值实际上平均为零来证明这一点，就像它对状态所做的那样。不可以做的是使用 V-hat 作为优势估计器。你也可以考虑是否可以使用一个依赖于观察函数的 V-hat 作为基线。这也是一个有趣的问题。事实证明，这实际上也是可以的，原因很简单，我们可以使用任何我们想要的东西作为基线，而估计器仍然是无偏的。使用仅依赖于观测的价值函数作为基线可能不会像我们希望的那样减少方差，但它始终是无偏的，因为所有基线都是无偏的，无论它们是什么。这就是策略梯度，简短的版本是，它们可以使用，但你必须小心使用优势估计器。

那么基于价值的方法呢？你可以简单地采用 Q 学习更新规则，天真地用观察值替换状态，这实际上会给你最好的无记忆策略吗？嗯，这里的答案遵循上一张幻灯片中的相同逻辑。原因同样是，不能将价值函数仅作为观察值的函数。同样的原因，也不能将 Q 函数仅作为观察值的函数。基本上，Q 学习依赖于一个假设，即每次访问一个状态时，无论你如何到达那里，你的价值对于所有不同的动作都是相同的。这在马尔可夫状态中绝对正确，但对于观察值则不然。因为如果你观察到给定的观察值，你对不同动作的价值可能取决于之前的观察值，所以它可能取决于你是如何到达那里的，这实际上使得这个 Q 学习规则无效。所以基于价值的方法在没有马尔可夫属性的情况下无法工作，你不能简单地用观察值代替状态。当然，如果观察值本质上是一个马尔可夫状态，就像在大多数 Atari 游戏中一样，这可能足够接近，结果可能不错。但一般来说，你拥有的部分可观察性越多，效果就越差。

一个非常明显的理解方法是，注意从 Q 函数中提取策略的方式是采取具有最大 Q 值的操作，这始终是一个确定性策略。但我们之前看到，POMDP 有时可以具有随机最优策略。因为 Q 学习永远不会产生随机策略，所以它绝对不可能产生最优策略，例如在那个三状态 MDP 中，随机策略是更优的。

好的，那么基于模型的强化学习方法呢？我们可以简单地用 O 代替预测模型中的 S，然后得到正确答案吗？结果答案非常清楚是不行。这里有一个例子来说明为什么这是一个糟糕的想法。假设我们有以下环境：我们有两扇门，我们开始处于一种状态，我们会随机地接近其中一扇门，然后尝试打开那扇门。如果它锁上了，我们应该尝试另一扇门，而哪扇门是锁着的或没锁的是随机的。所以状态的一部分是哪扇门是锁着的或没锁的，你无法观察到这个状态。你只能看到你在左门前面，或者在右门前面。所以这是一个部分可观察的问题，你无法观察到哪扇门是锁着或没锁着的状态，直到你尝试打开它。

这里有一个最优策略，甚至是一个无记忆策略，也就是说，如果你在一扇门前面，你应该先尝试打开它，如果不行就去下一扇门。或者如果你必须是无记忆的，你不能记住你是否尝试过这扇门，那么就像在三状态例子中那样，随机决定是换到另一扇门，还是继续尝试。所以有一种方法可以解决这个问题，即使你不记得你之前做过什么，也不观察门是否锁了。

假设你对在左门有一个观察，对在右门有一个观察，对穿过门有一个观察。然后你想训练一个模型。所以这个模型会预测你到达“通过”这个观察点的概率。假设你当前的观察点是左门，你的动作是“打开门”，那么你通过门的概率是多少？假设在每一轮游戏中，每扇门都有 50% 的概率被打开，也就是说 50% 的概率是左门打开，50% 是右门打开，而且它们是互斥的。所以你只需要抛硬币来决定打开左门或右门。所以在一半的游戏中你会通过，另一半你不会。

这意味着如果你尝试实际估计这些概率，如果你尝试训练一个模型，你会得到 0.5 的概率。但是，如果打开门的概率是 0.5，那么一个好的策略是什么呢？好吧，如果你每次尝试都有 50% 的概率打开门，这就是这个模型实际上试图表示的，那么你可以通过反复尝试来通过门。如果每次都是 50% 的概率，如果你继续尝试这扇门，最终你会通过它。但世界当然不是这样运转的。如果你尝试打开左边的门，门没有打开，那是因为门是锁着的，无论你尝试多少次，它都会保持锁着的状态。但这个马尔可夫模型根本无法表示这一点。它无法表示这样一个事实：如果你之前尝试打开这扇门失败了，即使你再次尝试，它也不会打开。因为 O' 的概率只是当前观察和当前采取的行动的函数，它并不依赖于这个模型中之前采取的行动。

所以这个马尔可夫模型根本不能用于非马尔可夫观察，因为它会导致这些荒谬的结论：如果你不断尝试打开门，最终它会打开。问题是，这个模型的结构与环境的结构根本不匹配。实际上，如果门之前没有打开，你通过的概率实际上是零。但你无法用这个模型来表示，因为这个模型没有将过去的观察和行动作为输入。

好的，到目前为止，我们讨论了无记忆策略，但这当然是一个非常人为的限制。尤其是在门的例子中，希望能够说明这一点。事实上，如果你尝试过这扇门，你会记得你以前试过但结果不理想，所以你以后会知道该做些不同的事情。当然，在实践中，如果我们想要获得好的方案来解决部分可观察马M决策过程，我们真的应该采用非马尔可夫策略，将观察历史作为输入。

有几种方法可以解决这个问题。一个简单的方法是使用所谓的“状态空间模型”。使用状态空间模型，我们本质上是在学习一个仅给出观察值的马尔可夫状态空间。我们之前在讨论变分推断时就看到过这种情况。如果我们训练一个序列变分自编码器（VAE），其中可观测量是观察值的序列，而隐藏状态是潜在状态的序列，其中潜在状态的动态变化，初始状态的先验概率可能是零均值单位方差，还有一些学习到的转移概率（也就是动态），以及一个观察概率（它模拟了给定当前隐藏状态的观察值的分布），还有一个编码器（用于将观察值的历史编码到当前隐藏状态中），那么这些潜在状态 Z 实际上代表了环境的马尔可夫状态。

这实际上可以很好地工作。所以如果你可以学习一个序列 VAE，就像我们在变分推理讲座中讨论的那样，如果你不记得它是如何工作的，请回到变分推理讲座并回顾一下。如果你可以学习这个，那么你实际上可以直接用 Z 代替 S。所以你不能直接用 S，因为你没有状态；你也不能直接用观察 O，因为那是不正确的；但你可以用 Z 作为 Q 函数的状态输入，这实际上是有效的，因为我们训练 Z 来遵循马尔可夫属性，因为它们具有马尔可夫动态。

嗯，现在为什么这个方法本身可能不是所有 POMDP 问题的足够好的解决方案？所以这是正确的，它是有效的，但为什么可能不够好？嗯，它不够好的原因是因为在某些情况下，训练这个预测模型实际上非常困难。事实上在很多情况下，没有必要能够完全预测所有观测值才能运行强化学习。所以，如果你能预测所有观测值，比如我们在 VAE 课程中讨论的论文中提到的，你可以直接预测那些 MuJoCo 环境的图像，那么你实际上可以使用底层隐藏状态作为马尔可夫状态空间。但这可能比解决强化学习问题更难，比如实际生成这些图像，生成所有这些像素可能比恢复最优策略更困难。所以也许我们不需要好的预测就能获得高回报。

我们可以做什么呢？我们可以观察到状态空间模型在运行推理时实际上使用历史观测值来推断 Z。所以编码器会获取所有先前的观测值并计算出 Z 的分布。这就是序列 VAE 运作良好的方式。如果我们要获取历史观测值，我们注意到 Z(t) 是历史观测值的函数，所以它不可能包含比历史观测值更多的信息。所以如果我们使用历史观测值本身作为状态表示，它将包含与我们推断的 Z(t) 一样多的信息。

那么，如果我们只是定义我们的状态，如果我们说我们的状态 S(t) 只是所有从 o(1) 到 o(t) 的观察结果，如果之前从 o(1) 到 o(t) 推断出 Z(t) 已经足够好了，这意味着 o(1) 到 o(t) 包含了我们需要的所有信息来获得一个马尔可夫状态，这本身就意味着它应该是一个马尔可夫状态。那么这是否有效？问这是否有效，基本上相当于问历史是否遵循马尔可夫属性。马尔可夫属性只是说，在给定当前状态 S(t) 的情况下，状态 S(t+1) 与状态 S(t-1) 条件性地无关。

现在，当前状态 S(t) 是到时间 t 为止的所有观察结果，前一个状态 S(t-1) 是到时间 t-1 为止的所有观察结果。嗯，这表明，先前的观察结果没有告诉我们任何我们不能从 S(t) 本身推断出来的东西，对吧？因为 S(t) 包含了 S(t-1)。从 o(1) 到 o(t-1) 的观察结果包含在序列 o(1) 到 o(t) 中。这意味着如果你已经知道 S(t)，那么找出 S(t-1)——也就是找出所有先前的观察值——并不能告诉你任何新信息，因为该序列已经包含了所有先前的观察值。这基本上就是为什么历史状态遵循马尔可夫属性的原因。也就是说，到时间 T 的观察值序列包含了到时间 T-1 的观察值序列，所以你不需要再单独考虑 T-1 的序列。这意味着如果我们对这些历史状态应用 Q 学习，也就是说我们的 Q 函数是所有从 o(1) 到 o(t) 的观察值的函数，这实际上会起作用。

所以我们当然需要设计能够利用这些历史状态的模型架构。那么我们如何表示一个接收整个观察值历史的 Q 函数呢？如果我们有一个传统的 Q 函数，就像你在作业三中为 DQN 做的那个，它只接收一张图像，你可以简单地连接一堆图像并将它们输入到 Q 函数中。这实际上并不像现在看起来那么糟糕。你只能使用一个固定的短历史，假设你要使用四个观测值作为输入。这不是完整的观测值历史记录，但在某些情况下，如果前四个观测值已经包含了你需要知道的大部分信息，那么它可能足够好了。

但这是否不好呢？有时候确实有点糟糕。因为你可能会遇到像上面提到的那种病态情况，比如你观察迷宫，你必须在到达某个高点后记住整个迷宫的布局，然后在整个过程中记住它。在这种情况下，简短的历史记录是不够的，你真的需要记住所有内容。所以在最普遍的情况下，我们需要使用一个序列模型，该模型可以接收可变长度的观测值历史记录作为 Q 函数的一部分，然后在最后输出 Q 值。这可以用任何序列模型来实现，比如 RNN、LSTM 或 Transformer。在这种情况下，我们的 Q 函数、策略或动态模型必须用 RNN、LSTM 或 Transformer 来表示。

这是非常合理的做法，你可以直接用一种显而易见的方式训练它，和你训练序列模型的方法一样。现在我们需要记住一些实际细节，这些细节与计算效率有关。让我们通过一个带有历史记录的深度 Q 学习算法的例子来说明。常规 Q 学习会收集一个转换，将其添加到重放缓冲区，从重放缓冲区中抽取一个批次，更新此批次的 Q 函数，然后重复。如果你想使用历史状态，你需要做的是收集一个转换，现在是一个元组 (o(t), a(t), r(t), o(t+1))。你将通过连接所有先前的观察结果来创建时间步长 T 和 T+1 的历史记录，然后将这些历史记录添加到缓冲区。然后你将抽取一批历史-动作元组，然后更新此批次的 Q 函数。

这是使用历史状态进行强化学习的有效方法，但它非常昂贵，因为现在你存储的信息量将随着序列长度的平方而增长。因为对于每个序列长度，假设为大写的 T，你就有大写的 T 个时间步长，每个时间步长中都有最多大写的 T 个观测值，因此这非常昂贵，内存成本会呈二次爆炸式增长。但这仍然是正确的，只是计算和内存成本昂贵。

所以你可以做的事情之一是，假设你正在使用 RNN 或 LSTM，其中 Q 的神经网络有一些隐藏状态用于读取这些观测值，那么你可以做的就是存储 RNN 状态本身。这样你就不用存储整个历史记录了。你可以说，观测值 o(1) 和 o(2) 完全由 RNN 的隐藏状态 h(2) 总结，而观测值 o(1), o(2), o(3) 完全由 RNN 状态 h(3) 总结。所以你可以做的是，基本上每次加载历史记录时都可以重用 RNN 隐藏状态。你不需要加载整个序列，而是从某个中间点开始加载，然后你实际上会在该点恢复 RNN 隐藏状态。你可以用 RNN 或 LSTM 来做到这一点。我不会详细介绍这种方法，它的基本思想是本质上使用 RNN 状态，就好像它们是系统的马尔可夫状态一样，但有一点需要注意，即 RNN 状态会随着 RNN 本身的更新而变化。如果您想了解更多信息，请查看论文《分布式强化学习中的循环经验重放》。您可以将这个技巧与 RNNs 和 LSTMs 结合使用，它对于获取非常长的历史记录非常有效，例如在 Atari 游戏中，它实际上获得了非常出色的性能。目前尚不清楚如何使用 Transformers 做到这一点，因为 Transformers 没有单个隐藏的马尔可夫状态。所以据我所知，没有人想出如何使用 Transformers 做到这一点，但对于 RNNs 和 LSTMs 来说，这是一个非常有效的策略。因此如果您想要一些实际细节，我鼓励您去查阅相关资料。

总结一下，POMDPs 很奇怪。有些设置会发生在 POMDPs 上，而永远不会发生在 MDPs 上，例如随机策略和信息收集操作。有些方法，比如策略梯度，在它们能够恢复最佳无记忆策略方面是有效的，但最有效的方法（例如基于价值的方法）却无法做到这一点，因为它们需要使用价值函数。即使是那些有效的方法，它们仍然会得到无记忆策略，这可能不如具有记忆的最佳策略那么好。我们可以使用像序列 VAE 这样的模型来学习一个马尔可夫状态空间，这是有效的。我们也可以只使用历史状态，这意味着使用序列模型来读取观察历史，这是一种有效的方法。此时，你需要使用序列模型来表示价值函数、策略和模型。

### [第二部分] 使用强化学习训练语言模型

好的，在上一节中，我们讨论了如何使用序列模型来帮助处理具有部分可观察性的强化学习。在下一节中，我们将反过来，讨论强化学习如何帮助我们训练更好的序列模型，特别是用于语言建模。在讲座的第三部分，我们将把这些结合起来，同时介绍部分可观察性和语言模型。

那么什么是语言模型？为什么我们应该关心它们？嗯，语言模型在其基本层面上是一个预测下一个 token 的模型。你可以粗略地将 token 视为单词，尽管实际上它们不是单词，它们更像是字符的组合。嗯，关于 token 是什么，实际上有点复杂，但粗略地说，它是自然语言的一些粒度表示。

通常，我们使用 Transformer 来表示语言模型。它的工作原理是，我们取 token 序列 x0, X1, X2, X3，在每个位置，我们都有一个小型编码器，它将离散的 token 编码到连续的空间中，同时也会编码它们在序列中的位置。基本上，它们在序列中的位置（整数 0, 1, 2, 3, 4）被编码成连续的表示，然后传递给所谓的“掩蔽自注意力层”，这本质上是一个 Transformer，它可以在每个位置上基于前一个时间步骤的表示来产生一个新的表示，这就是“掩蔽”所指的。然后使用一些逐位置的非线性变换对它们进行转换，这个自注意力块会重复多次，这本质上就是 Transformer 的核心。然后在每个位置的最后，我们读出一个在所有 token 上的分布来预测下一个 token，这基本上只是一个 softmax。

例如，在第一个时间步，我们读入 x0，然后我们对 X1 做出预测。如果我们正在解码，那么我们会从一些 token 开始，比如单词 "I"，我们解码出一些单词，比如 "like"，然后将其用作第二个时间步的条件信息，你对下一个词进行解码，比如 "POMDP solvers"，在那里你可以进行解码，最后模型会输出一个序列结束 token，以指示它已完成此特定序列的生成。所以这基本上是一个 Transformer 语言模型。

现在，就本课程而言，你实际上不需要了解 Transformer 的工作原理，因此你可以将此图简化为某种黑盒，我们称之为 Transformer，它按顺序读取 token 并预测下一个 token。因此在每个时间步长上，它都会对给定 X(1) 到 X(t-1) 的 X(t) 的分布 P 进行建模，并通过从该分布中反复采样，你最终会得到一个句子，例如 "I like POMDP solvers"。请注意，这个模型不是马尔可夫的，所以每个 token 都依赖于所有先前的 token。当然，众所周知的 ChatGPT 系统以及所有这些系统都是语言模型的例子，而所有这些系统在内心深处所做的就是逐个 token 地生成语言，其中你为提示指定 token，然后它会为响应生成 token。

现在，语言模型通常是用监督学习来训练的。也就是说，你给它们大量的英文文本或其他语言的文本，然后让它们使用所有这些数据，根据前面的所有 token 来预测下一个 token。但是，如果我们想要的不是匹配数据中的分布，我们也可以使用强化学习来训练它们。也就是说，我们不只是希望它们输出我们在训练数据中看到的相同类型的文本，而是希望它们最大化某些奖励函数。

这在很多情况下都是非常可取的。例如，你可以使用强化学习来让语言模型满足人类的偏好，从而生成人们喜欢看到的那种文本。你也可以使用强化学习来让语言模型学习如何使用工具，比如调用数据库或计算器。你还可以使用它来训练语言模型，使它们能够更好地与人类进行对话并实现对话目标。我们将讨论所有这些，这些都不同于简单地匹配训练数据，这些都需要强化学习，而不仅仅是监督学习。

但是，为了能够将强化学习应用于语言模型，我们必须回答一些问题：与语言生成任务对应的 MDP 或 POMDP 是什么？MDP 由状态、动作、奖励和转移概率决定，我们必须为语言生成任务选择这些内容。现在有一些明显的直觉，比如你知道，如果你正在生成语言 token，那么你的动作可能与语言 token 有关；如果你的目标是最大化用户偏好，那么你的奖励可能与用户偏好有关。但实际上，正确处理这些细节有一些有趣的设计决策。所以奖励是什么？我们应该使用什么算法？我们在上一节中学习到，某些算法可以处理部分可观察性。在之前的课程中，我们看到有些算法适用于离线策略，有些适用于在线策略。所以我们必须做出一些选择。

我们将从强化学习用于语言模型的训练开始，有时也称为单步问题，这是强化学习在语言模型中最广泛的应用。例如，ChatGPT 就是这样训练的。然后在下一节中，我们将讨论多步骤问题。

这里有一个基本的公式。我们有一些提示，也许提示是“法国的首都是哪里？”。而 Transformer 会进行预测。现在它实际上并不是预测提示的 token，但这仍然是其训练数据的一部分，它预测的是“完成”（completion）。所以它会预测像“巴黎”这个词，在生成过程中，它会在下一个时间步作为输入被输入，然后它会预测序列的结束。所以在大多数应用中，语言模型会完成一个句子，而不是从头开始生成一些东西。提供的前缀就是提示，然后完成就是所需的输出。

好的，所以我们要说，一个基本的公式是：完成是我们的动作。所以动作 a 由两个 token “巴黎”和“序列结束”表示，通常这可以是可变数量的 token。提示或前缀或上下文是状态 s。所以我们的语言模型表示给定 s 的 P(a)。现在它表示的方式是通过每个时间步的概率乘积。因为它不生成 X1, X2, X3 和 X4（这是提示），它只生成 X5 和 X6。所以给定 s 的概率 P(a|s) 由给定 X1 到 4 时 X5 的概率和给定 X1 到 5 时 X6 的概率的乘积给出。我将 X1 到 4 与 X5 分开，因为 X5 实际上是动作的前一个时间步，而 X1 到 4 是状态。所以给定 s 的 π(a|s) 本质上是我们的策略。

现在需要注意的是，本节中有两个“时间步”的概念，非常令人困惑。时间步 1, 2, 3, 4, 5, 6，这些是 Transformer 语言生成的时间步。就强化学习算法而言，只有一个时间步：你观察一个状态，然后创建一个动作。所以这很令人困惑，因为在常规强化学习中，时间步长总是意味着同一件事，现在实际上有两种时间步长：一种是语言时间步长，然后是强化学习的时间步长，它们不一定相同。所以对于强化学习的目的来说，这里实际上只有一个时间步长，这是一个“赌博机”（bandit）问题，它是一个单步 MDP。就语言生成而言，有很多时间步长。

好的，所以现在我们定义了时间步长，定义了动作，定义了状态，定义了策略。我们的策略概率由所有完成步骤的语言时间步长概率的乘积表示。现在我们可以定义我们的目标，即最大化策略下的预期奖励，就像在常规强化学习中一样。这使得它成为一个基本的单步强化学习问题，它是一个赌博机问题。

好的，所以让我们从使用最简单的强化学习算法开始，即策略梯度。这是我们的目标，我们将采用它的梯度，我们将使用策略梯度讲座中的公式。所以我们知道预期奖励的梯度是梯度 log π * R 的期望。现在我们之前看到 π 只是完成过程中所有 token 概率的乘积，所以当我们采用 log π 的梯度时，也就是所有完成 token 的对数概率的梯度之和。好的，这很简单，因为这些正是你从交叉熵损失进行反向传播时计算出的梯度。

当然，我们可以用样本来简单地估计它。所以如果我们使用标准的 REINFORCE 估计器，那么样本需要来自策略 π(θ)。所以你实际上会从你的语言模型中采样一个完成，你会告诉它“法国的首都是什么？”，让它生成一个完成，它会生成“巴黎”和序列结束的 token，然后你会评估该样本的奖励，并将其用作梯度估计器的一部分。

你也可以使用重要性采样估计器。你可能从其他策略生成完成，然后使用重要性权重来获得当前策略的评分。样本可以来自其他策略 π_bar。π_bar 例如可以是监督训练模型。第一个估计器是 REINFORCE 风格的估计器，第二个是重要性加权估计器，比如 PPO。第二类在语言模型中更受欢迎。你可以花点时间思考一下为什么重要性加权估计器在语言模型中更受欢迎。原因是从语言模型中采样需要相当长的时间，并且非常不希望每次都生成样本来采取一个梯度步骤，特别是因为评估这些样本的奖励可能很昂贵，我们稍后会讨论这个问题。所以在现实中，通常更倾向于从语言模型中生成一批样本，评估这些样本的奖励，然后使用重要性采样估计器采取许多梯度步骤，然后重复。

好的，所以一个特定的算法是：让我们采用这个重要性采样估计器，我们称之为 grad_hat 作为简写，并注意它是 θ, π_bar 和一组样本 a_i 的函数。你可以这样做：你为特定状态采样一批完成（实际上你会有很多状态，但我只为单个状态写了这个），你会采样一批完成，你会评估每个状态的奖励，然后你可以将 π_bar 设置为之前的策略，也就是生成这些样本的策略。然后你会有一个循环，在这个循环中，你会对一个小批量进行采样，然后在这个小批量上，你会使用 grad_hat 进行梯度下降，然后重复这个过程 K 次。所以你的批量可能是 1000 次完成，然后你的小批量可能是 64，然后你会采取一些梯度步骤。然后每隔一段时间，你会回到模型中生成更多样本，将其设置为 π_bar 并重复。所以这是非常经典的 PPO 风格的循环。这是一种非常流行的用强化学习训练语言模型的方法。

但有一个大问题是奖励。请注意，每次我们从语言模型策略中生成一批完成数据时，我们都必须评估每个完成数据的奖励。我们从哪里得到这个奖励？因为通常如果我们要训练比如“法国首都是哪里？”这样的问答类问题，我们可能有一个真实答案的数据集，但在这里，策略可能会生成数据集中没有的答案。所以我们需要它有一个奖励函数，这个奖励函数需要能够对给定问题的任何可能的完成情况进行评分。

所以很多时候，当我们这样做时，我们希望将奖励 R 本身表示为一个神经网络。因为我们不仅要弄清楚“巴黎”是正确答案，并且应该获得 +1 的奖励，我们还必须弄清楚当语言模型说“哦，这是一个叫巴黎的城市”时会发生什么。这是一个相当不错的答案，它是正确的，但它可能不那么简洁，所以我们可能会给它一个稍微低一点的奖励，也许我们会说那是 0.9 而不是 1.0。它也可能说“我不知道”，这可能实际上不是错误的，就像它真的不知道，但这是一个更糟糕的答案，所以我们可能会给它一个 -0.1 或类似的。然后如果它说“伦敦”，嗯，那太糟糕了，应该是 -1。但它是一个语言模型，所以它可以说出任何话，它也可能会说“哦，你为什么问这么愚蠢的问题”，这也许是非常不可取的，你给它一个 -10，让网络自己表现。

所以你的奖励模型不仅需要知道正确答案是什么，还需要能够理解如何将奖励分配给那些只有一点点偏差的答案，或者那些完全不同的答案，比如超出问题范围的答案。这是一个非常开放词汇的问题，所以你需要一个非常强大的奖励模型。

那么我们可以做什么呢？我们可以把所有这些潜在的答案都拿出来，我们可以从一些语言模型中抽取样本，也许我们有一个监督训练的语言模型来开始，我们抽取一些答案，然后把它们交给人类，我们让人类生成这些数字。所以也许人类会看这些答案，然后给所有答案分配数字。这会创建一个由句子组成的数据集，比如“法国的首都是巴黎”，标签就是数字。然后我们进行监督学习，我们训练一个模型，它基本上会查看句子，然后输出这个数字。这可能是一种训练奖励模型的方法。我现在要用下标 ψ 来表示这个奖励模型的参数。

但当然问题是人们如何知道这些数字？人们如何真正将数字 -10 分配给“为什么这是一个如此愚蠢的问题？”。也许有些人可以做到这一点，也许你实际上可以在某些情况下执行一个有明确正确性单位的任务。也许它是一个教学应用程序，奖励是学生回答测试的正确程度，或者也许是一些销售员应用程序，奖励是赚取多少收入。在这些情况下，奖励可能非常量化，人们实际上可以标记它。但在非常主观的情况下，比如“为什么这是一个如此愚蠢的问题”应该是 -10 或“伦敦”应该是 -1，在这些情况下，也许人类很难为这些东西分配明确的数值。

对人类来说可能更容易的是比较两个答案。所以如果你告诉一个人问题是“法国的首都是什么？”，你有答案 A 和 B，A 是“巴黎”，B 是“为什么这是一个如此愚愚蠢的问题”，人们很容易说“哦，我更喜欢 A”。所以在某些情况下，偏好可能更容易表达，特别是当效用非常主观的时候。所以这里有一个想法，我们可以使用这种偏好来设计奖励函数。

现在奖励函数必须为特定答案分配一个数字，而偏好是两个答案的函数。所以给定 A1 和 A2，一个人有多大可能更喜欢 A1 而不是 A2，这是一个定义明确的概率。所以如果状态是“法国的首都”，行动是“巴黎”和“为什么这是一个如此愚蠢的问题”，偏好 A 是标签，我们可以简单地模拟 A1 优于 A2 的概率，我们可以学习这一点。但是因为我们最终想要的是一个奖励函数，我们可以做的不是训练一个预测 A1 是否优于 A2 的神经网络，而是我们可以将这个概率描述为奖励的函数。

我们必须在这里做出一个选择。一个非常流行的选择实际上是从与我们在逆强化学习课程中讨论过的最大熵逆强化学习相同的数学基础中得出的，其原理是将 A1 优于 A2 的概率建模为 A1 奖励的指数除以（A1 奖励的指数加上 A2 奖励的指数）。粗略地说，这意味着 A1 优于 A2 的概率与其奖励的指数成正比。这意味着如果一个奖励明显优于另一个，那么这个奖励肯定会被优先考虑，但如果它们的奖励大致相等，那么它们被优先考虑的可能性大致相同。指数变换的数学原理与我们在最大熵逆强化学习课程中看到的非常相似，我不会深入探讨其中的数学原理，但这基本上是直觉。

所以现在我们实际训练这个模型的方法就是最大化人类对这些 (s, a1, a2) 元组表达的偏好的可能性。但该偏好的预测器由我们的奖励模型 R(ψ) 参数化，使用幻灯片底部的这个比率。然后我们取它的对数，最大化对数似然，这是一个定义明确的监督学习问题。所以我们可以从成对的偏好中获得数值奖励。

顺便说一下，你可以很容易地将其扩展到偏好表达在两个以上项目上的情况。所以你可以向人们展示四个完成项，让他们说出他们更喜欢哪一个。在这种情况下，你的分母中会有四个值的和。你也可以进行四向比较，并将它们转换为所有可能的成对比较，这也是你可以表达这一点的另一种方式。所以你可以说，如果向某人展示 A1, A2, A3, A4，他们更喜欢 A1，那么你会说 A1 比 A2 更好，A1 比 A3 更好，A1 比 A4 更好，并将其转换为三个成对比较，这也是有效的。

所以这里有一个我们可以在这个方案中使用的总体方法。这种方法在两篇论文中有描述，《根据人类的偏好对语言模型进行微调》和《训练语言模型以遵循人类反馈的指令》。这些基本上是指导 ChatGPT 等的基础。整体方法是首先进行监督训练或通常进行微调，以获得初始策略 π(θ)。这是语言模型的监督训练。然后针对数据集中的每个问题，从策略中抽取 K 个可能的答案，并构建数据集，该数据集包含一个带有提示 s_i 的元组和 K 个可能的答案 a_i1 到 a_ik。然后让人类标记每个点，通过指示他们喜欢哪个答案。然后您将使用该标签数据集来训练奖励模型 R(ψ)。然后使用强化学习更新 π(θ)，其奖励为 R(ψ)。然后您会重复这个过程几次。

通常来说，当你在第五步中这样做时，你实际上会运行许多策略优化步骤。所以在第五步中，你不会只针对那个奖励进行一次重要性采样优化，你实际上会从 π(θ) 优化中生成样本，生成更多样本并重复。所以实际上这里有两个嵌套循环：外层循环用于生成更多样本，并要求人类表达偏好；然后是另一个循环，你实际上在其中运行这个策略梯度；在里面还有另一个循环，你为多个步骤运行重要性采样更新。所以这就是整体方法。

现在我们必须首先解决一些挑战。人类的偏好非常昂贵，因为这实际上涉及到将大量数据发送给人类标签员，可能需要几天甚至几周才能得到回复。当然，如果你有一个非常好的众包系统，也许你会在几小时内得到答案，但这仍然比在 GPU 上进行梯度下降步骤慢得多。所以你想尽量减少你将数据发送给人工进行标记的次数。因此在实践中，大多数偏好数据通常来自初始的监督训练模型。因此，尽管我将其写成一个循环，在其中反复查询更多偏 B好，但实际上，第一次进行第二步时，您会标记很多偏好，然后在后续尝试中，您获得的偏好会少得多。事实上，如果您想要一个“穷人版”的偏好收集，您可能甚至不需要那个外循环，您可能只需执行步骤 1, 2, 3, 4, 5 一次。

因此人工偏好的代价很高，您还需要进行多次强化学习迭代，包括每次偏好收集迭代时从策略中生成新样本。这实际上使它非常像基于模型的强化学习方法。由于这是一个单步问题，因此没有动态模型，但有一个奖励模型。该奖励模型的训练频率要低得多，然后许多强化学习更新都是在同一个奖励模型上进行的。事实上，如果您没有那个外循环，而只需执行步骤 1, 2, 3, 4, 5 一次，它实际上是一个基于离线模型的方法。

那么，这有什么问题呢？我们为什么要担心呢？当然，问题就是我们之前在基于模型的强化学习讨论中看到的，问题在于分布偏移。在强化学习中，对于语言模型来说，这有时被称为“过度优化”，这基本上意味着你利用了奖励模型一段时间，虽然策略最初会变得更好，但后来会变得更糟。

另一个问题是，奖励模型必须非常好。所以过度优化通常可以通过简单的修改来解决。我们只需在预期奖励目标上添加一个惩罚，以惩罚策略 π(θ) 偏离原始监督策略。而这个 KL 散度可以方便地写成，只需将原始监督训练模型的对数概率添加到奖励中。这只会改变奖励函数：你采用奖励模型，然后加上原始监督训练模型的对数概率，乘以一个系数 β。通常当你要做到这一点时，你需要一个非常大的奖励模型，通常是一个巨大的 Transformer，它本身已经预先训练成一个语言模型，然后进行微调以输出奖励。因为奖励模型需要非常好，它需要足够强大，能够抵抗来自强化学习的所有优化利用压力。

好的，回顾一下概述。我们可以使用策略梯度来训练语言模型。我们通常使用重要性抽样估计器。目前这是一个赌博机问题，尽管我们将在下一节中将其扩展为多个步骤。我们可以使用一个可以从人类数据中训练出来的奖励模型。通常我们会用偏好而不是效用标签来训练它，使用这个等式作为用户喜欢 A1 而不是 A2 的概率，这比直接监督奖励值更方便。现在，所有这些在技术上最终都是一种基于模型的强化学习算法，因为我们将奖励训练成一个模型，然后我们针对该模型针对许多强化学习步骤进行优化。如果我们实际上没有从策略中获取额外的样本并将它们发送出去进行更多标记，那么它可能是一种基于离线模型的强化学习算法。有一些细节需要注意，比如尽量减少人工标记和过度优化。我们应该使用一个非常强大的大型奖励模型，这样才能处理这个符号。而我们解决过度优化的方法通常是添加这个 KL 散度惩罚，以确保策略不会偏离监督训练模型太多。

### [第三部分] 基于语言模型的多步强化学习

在今天讲座的第三部分，我们将讨论基于语言模型的多步强化学习。我们将结合 POMDP 讨论中的一些想法以及之前关于语言模型的讨论。

这里有一个基于语言模型的多轮强化学习问题的例子。这是一个叫做“视觉对话”任务的例子，它是 2017 年一篇论文中提出的基准。这里的想法是，有一个提问者（也就是机器人），而答案被视为环境的一部分。答案者会根据特定的图片进行回答。提问者需要通过提问来猜测图片的含义。所以，对于提问者来说，这纯粹是一个语言任务。提问者需要选择合适的问题来收集信息，这样他们最终才能弄清楚答案者想到的是什么图片。

现在，你可以想象将其构建为一个 POMDP 模型，其中观察结果是答案者所说的内容，而行动是提问者选择的问题。这是一个连续的过程，有多个时间步骤，最后会有奖励。所以动作就是机器人说的话，观察就是答案者或模拟人类说的话。现在的状态是一个历史状态，就像我们在第一部分讨论的那样，所以这将是过去观察和动作的序列。而奖励是对话的结果，即提问者最后是否猜出了正确答案。

所以这项任务的多步骤性质非常重要。现在我们回到完整的强化学习设置，因为提问者不仅仅是为了贪婪地问问题才能得到答案，他们还会问问题来收集信息，这样他们就可以在最后猜出正确答案。显然他们不应该多次问同一个问题，他们应该思考他们已经收集了哪些信息，哪些信息仍然未知，并据此进行处理。

现在这些多轮问题出现在很多地方。它们当然出现在对话系统中，你可能与人互动以实现一些最终的、有延迟的目标，比如助手聊天机器人。你可能有多个回合，通过交互来获得解决方案。在工具使用设置中，你不是与人交谈，你可能会输出文本，这些文本会进入某些工具，例如数据库、Linux 终端、计算器，然后使用该工具对给定查询生成答案。玩文本游戏时，你可能会产生进入文本冒险游戏的动作，然后游戏会以程序化的观察进行响应。所以这些都是多回合强化学习问题的例子。

现在这与我们上一节看到的 RLHF（来自人类反馈的强化学习）不同。在上一节中，我们从人类偏好中学习，但那里的奖励只在多回合交互之后出现。上一节中的“回合”（episode）只是一个答案，所以那是一个带有状态和动作的单回合赌博机问题。这里我们有多个回合，多个观察和动作，部分可观察性现在很重要，因为我们不仅需要关注来自人类的最新反应，还可能需要关注所有先前的反应和我们之前问的所有问题。

现在我们面临一个不同的问题，我们如何训练策略来更好地处理这个问题？我们可以使用策略梯度，就像之前一样。策略梯度是一种可行的多轮策略方法，这就是我们引入它们的原因。在第一部分中，我们也了解到策略梯度实际上可以处理部分可观察性。我们可以为策略提供观察历史，这样我们就可以使用这些历史状态，这是非常可行的。

然而，在使用策略梯度时，我们遇到的一个问题是，如果我们正在训练一个与人交谈的对话代理，那么我们需要在每次轮次中从人那里获取样本。这与我们之前使用的人类偏好设置不同，因为在那里我们有奖励模型，我们可以通过多次迭代（包括采样和优化）来针对奖励模型进行优化，并且只是偶尔获得更多的偏好。但是，如果我们将策略梯度用于对话任务，而每个回合都需要与人交谈，那么现在我们需要与人进行更多的互动。因此，即使有了偏好，我们仍然需要人工输入。如果我们想用策略梯度来优化对话代理，我们需要更多的人工输入。虽然这种方法可行，但成本很高。当然，如果你不是与人互动，而是与数据库之类的工具互动，那就容易多了。

然而，基于价值的方法是一个非常有吸引力的选择，因为有了基于价值的方法，你可以使用离线强化学习技术，就像我们之前在课程中学到的那些，然后直接用数据来训练你的对话代理，比如人与人之间的对话，或者过去部署的机器人的数据。所以，基于价值的方法对于对话来说是一个非常有吸引力的选择。因此在这部分讲座中，我将重点讨论基于价值的方法。不过我会说策略梯度方法可以直接使用，嗯，关于这一点，我没什么可说的，因为它们的工作方式和以前完全一样。

所以我们来谈谈基于价值的方法。对于基于价值的方法，我们必须做出一个选择，也就是构成时间步骤的因素。所以在上一节的开头，我讨论了如何将语言问题转化为多任务处理（MDP）。这里有一个特别微妙的选择，我们可以做出这个选择，这个选择可以双向进行。

第一个选择是让每一个“话语”（utterance）是一个时间步长。这意味着人类说的第一句话，比如“两只斑马在动物园的围栏里走来走去”，这是观察一；提问者说的第一句话，比如“镜头中的任何人”，这是动作一。所以动作和观察都是完整的句子。这也许与我们上一节的设定最直接类似，这是一个自然的选择，因为观察总是在代理的控制之外，动作总是完全在代理的控制之下。序列长度通常相对较短，所以如果对话涉及 10 个来回的问答，那么我们将有 10 个时间步长。问题是动作空间非常大，动作空间是机器人可以说的整个话语空间。

另一种选择是将每个“token”视为一个时间步长。所以在这种情况下，对于机器人的整个话语，例如“镜头中的任何人”，这个话语中的每个 token 都是一个单独的动作时间步长。这之所以有点奇怪，是因为每个动作都在它的控制之下，所以在动作一之后，它立即就可以选择动作二，没有额外的观察。我们仍然会将动作一连接到状态历史记录中，然后根据整个历史记录选择下一个动作。响应中的每个 token 都是一个单独的观察。现在，这有一个非常大的优势，那就是现在在每个时间步，我们都有一个简单的离散动作空间。所以任何时间步的动作空间都只是一组可能的 token，它是一个很大的集合，但很容易枚举。而在每个话语的设置中，动作集是所有可能序列的集合，在序列长度上呈指数级增长。当我们使用每个 token 的时间步长时，问题在于我们的序列长度现在要长得多。之前我们的序列长度可能是 10 个步骤的数量级，现在即使是相对较短的对话，也可能有数千个步骤。

这两种选择在文献中都已被探索过，没有一个既定的标准来决定哪一种更好，所以我将讨论它们，也许可以稍微介绍一下它们的优缺点。

那么让我们从基于价值的强化学习开始，每个话语的时间步长为一个步骤。这里是我们对话的一个示例片段，假设我们处于这一步，机器人询问“他们是否面对面？”的阶段。我们要做的是获取对话的历史记录，直到此刻，这构成了整个对话历史记录 S(t) 的状态。我们会将它传递给某种序列模型，嗯，它可以是一种预先训练好的语言模型，可以是像 Bert 这样的模型，有各种各样的选择。序列模型将采用某种嵌入，然后我们还会采取候选动作“他们是否面对面”，我们也会将它传递给序列模型，这可以是单独的序列模型，也可以是同一个序列模型。我们将获得这两个对象的嵌入，并将其输入到某个学习函数中，以输出 Q 值。嗯，也许最简单的方法是为状态和动作设置两个独立的编码器，但它们也可以用同一个编码器进行编码。最后我们必须为它们预测一个数字，也就是 Q 值，所以这就是“评论家”（critic）。

通常，在这个设计中，我们会使用一个 Actor-Critic 架构。我们会有一个单独的 Actor 网络，该网络经过训练可以最大化这个评论家，可以使用例如上一节中的算法之一进行训练，将 Q 值代替奖励作为单步目标。或者我们可以直接从 Q 函数解码以找到具有最高 Q 值的动作，这有点棘手。如何做到这一点呢？我们可以使用像集束搜索（Beam Search）这样的方法来做到这一点。我们也可以从监督训练模型中采样，并取具有最高 Q 值的样本。然后我们将使用我们对下一个时间步的最大 Q 值的估计来训练这个 Q 函数。这样下一个时间步的最大值可以来自集束搜索，也可以来自使用 Actor，也可以来自从监督训练模型中采样，然后取最大 Q 值的样本作为最大值的近似值。所有这些都是有效的选项，文献中的不同方法也探索了不同的选择。我将在本节末尾总结几篇以前的论文，并告诉您具体的论文实际上是怎么做的。

所以没有一种方法可以做到这一点，每个 token 的时间步长都有多种选择，事情可能稍微简单一些。假设我们处于解码过程的这一点，我们正在生成与“facing”对应的 token。记住，当然，实际上单词不是 token，token 实际上对应于多个字符，但不是整个单词。但让我们假设 token 是单词。假设我们处于“facing”这个词，所以我们要对单个 token 进行贝尔曼备份。现在它的工作原理更像监督语言模型，我们有这些 token，并且我们在这个时间步长中为每个可能的 token 提供一个数字。只不过这个数字不是对应于该 token 成为下一个 token 的概率的数字，而是数字实际上是它的 Q 值。所以与“face”相关的 token 关联的数字就是 Q 值。如果你的历史记录是之前整个对话的历史记录，然后你选择“face”作为下一个动作，那么你的损失函数会在下一步中最大化所有可能的 token。

如果代理选择了这个 token，或者如果环境选择了数据集中的 token，则直接取数据集中的 token，加上奖励，然后将其用作损失函数中的目标值。这本质上实现了每个 token 的 Q 学习。再次解释一下，对于每个 token，它们的输出是下一个时间步中选择的每个可能 token 的 Q 值。为了计算该 Q 值的目标值，我们实际上会在下一个时间步输入该 token，查看所有可能的下一个 token 的 Q 值，如果代理可以选择下一个 token，则取最大值，或者如果环境选择了数据集中的 token，则取数据集 token 的值，加上奖励，然后将其作为目标值。从某种程度上来说，这更简单，但请记住，我们的序列长度会更长。所以我们有简单离散动作的规则可能比每个话语的规则简单，因为我们不需要处理 actors，也不需要处理所有其他东西，但是我们的序列长度很长。

把它们放在一起，通常的基于价值的细节适用。所以我们通常需要一个目标网络，用于每个话语或每个 token 版本。我们通常会使用重放缓冲区，我们通常会做一些事情，比如使用双 Q 学习技巧等等。所以所有相同的考虑都适用于常规的基于价值的方法。我们可以将它用于在线或离线强化学习。据我所知，这些方法主要是为离线强化学习而研究的，在这种情况下，你会使用像 CQL 或 IQL 这样的工具来使其正常工作。嗯，细节基本上需要以某种方式处理分布偏移。所以你可以使用策略约束，如果你有一个 Actor，那么你会对 Actor 使用 KL 散度约束。如果你只是使用基于价值的方法，你可以对 Q 值使用 CQL 风格的惩罚。

这对于每个 token 版本来说，基本上相当于将标准监督交叉熵损失放在目前还不清楚为什么会这样。你可以算出来，只需写下 CQL 目标，然后通过离散操作，你会发现它实际上和交叉熵损失是一样的。你也可以做一个 IQL 风格的备份，这也是一个不错的选择，但是目前还没有一个最好的答案来决定哪一个是更好的选择。所以这在很大程度上处于当前研究的前沿（截至 2023 年）。

这有点抽象。为了看到你可以实际实现的具体算法来实现这一点，让我们来看一些例子。其中一个例子是 Natasha Jaques 的一篇较早的论文，名为《通过离线强化学习进行以人为中心的对话训练》，它使用了 Actor-Critic 加策略约束架构。因此有一个 Actor 网络，它有一个 KL 散度惩罚，以保持接近数据分布。这方面的奖励来自人类用户情绪分析，所以聊天机器人实际上是在尝试优化从人类引发的情绪，奖励是自动计算的，使用情绪分析器来分析人类的反应，并使用每个话语的时间步长公式。

另一个例子是，一个聊天机器人 AI，用于面向任务的对话，并由 Verma 等人进行离线强化。这个例子使用带有类似 CQL 惩罚的 Q 函数，它使用来自任务的奖励，在本例中是 Craigslist 谈判任务，因此奖励仅来自销售一件商品的总收入。这里的时间步长是一次话语。因此在下一个时间步长中实现最大化的方式实际上是从监督训练的语言模型（在本例中是 GPT-2 风格模型）中采样多个可能的响应，然后对这些采样话语的 Q 值取最大值。因此这不是精确的最大值，而是使用来自预训练语言模型的样本的近似最大值。

另一个更近期的例子是 Snell 等人在 2022 年提出的《使用隐式语言 Q 学习进行自然语言生成的离线强化学习》。这个例子使用 Q 函数训练，实际上是 IQL 和 CQL 的组合，因此它使用带有 CQL 惩罚的 IQL 备份。然后，策略实际上是通过再次从监督训练模型中抽取样本来提取的，然后从该监督训练模型中抽取具有最大 Q 值的样本。奖励再次来自之前在视觉对话任务中进行评估的任务，其中奖励对应于代理是否获得正确答案。因此，如果您想了解更多关于特定基于价值的算法的信息，我建议您查看这些论文，了解他们选择的具体细节。我对这些方法的描述有点抽象和笼统，这些论文中涵盖了具体的实例。Snell 的公式使用每个 token 作为一个时间步长。

所以回顾一下，多步骤语言交互，例如对话，是一个 POMDP，这意味着我们需要做一些事情，例如使用对话历史作为我们的状态表示。时间步长可以定义为每个话语或每个 token，它们各有优缺点。原则上，一旦我们切换到使用历史状态，任何强化学习方法都可以使用，但在实践中，特别是如果我们有需要与人类交谈的对话代理，我们可能真的更喜欢离线强化学习公式，因为否则每次生成更多样本时我们都必须与人类互动。当然情况不一定如此，因为如果我们在做文字游戏或工具使用之类的事情，那么在线方法实际上是非常可行的。基于价值的方法将话语或 token 视为动作，并使用历史状态构建 Q 函数。我们必须应用与常规离线基于价值的方法相同的细节和技巧，包括目标网络、双 Q 学习之类的技巧，以及各种离线正则化方法，例如策略约束、CQL 或 IQL。对于哪种方法最好，没有单一的既定标准，并且有各种不同的选择，各有优缺点。