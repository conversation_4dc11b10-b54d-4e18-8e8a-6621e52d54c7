好吧嗯嗯这个麦克风好像有点
动静所以我可能最后会
大喊大叫但我想我们拭目以待嗯好的
很酷嗯哦我们在哪里我们
很好好吧我们很好嗯是的所以
非常感谢你的邀请嗯很
高兴来到这里嗯就像凯文说的我是
从斯坦福来的我希望你不要怪
我嗯但是我设法
及时赶到穿过了湾区的
交通嗯今天我要谈论
rhf一些人们可能有点
熟悉的东西但如果不熟悉也
没关系我们会尝试或多或少地涵盖它
不是完全从零开始而是提供
一些
背景嗯我试着留出一点
时间来提问嗯你知道我确实
希望人们能够真正从中学习
如果你有问题请告诉我如果时间紧迫我
可能不得不开始忽略它们
但嗯请随意
举手我会尽我
所能我们不再多说嗯
好吧如果 你在这门课上，我想
很多人可能不在这门课上，
但你可能对
强化学习很熟悉。如果你是
人类，你
可能听说过一些关于语言
模型的事情，但我们为什么要讨论
语言模型的强化学习呢？
我认为有很多方法
可以制作这张幻灯片，我想过
做一些像
MLP 和 RL 这样的长时间线，并展示它们的
收敛，但我认为
这里的主要思想是 gbd3 很酷，你所有的
研究员朋友都说，哇，
你见过这个新模型吗？
然后 Chachi P 出现了，
就像你的奶奶说，你
见过这个新模型吗？这些
完全是不同层次的，你知道，在
公众意识中的渗透，是的，
所以
问题是，
这两者之间有什么关系，
添加了什么，嗯，这真的是
一种或多或少奇怪的技巧，也就是
从人类反馈中获得的 RL，这就是
我们
今天要讨论的，嗯，只是一点点 一些
背景知识，这样我们就能达成
共识了。如果大家
对语言模型不太熟悉，
那么什么是语言模型呢？
为了我们今天的目的，你可以把
语言模型想象成一个
基于标记的自回归模型，标记
可以看作是单词，
有时它们是子词，或者
词块，但在
大多数情况下，它基本上是一个基于
单词的自回归模型。我们
会有一些词汇表，也就是
可能的标记集，嗯，在某种
现代大型语言模型中，这个词汇表大约有
50,000 个，但可能会有
很大差异，嗯，基本上你的
语言模型，你的策略，嗯，将
标记序列映射到
下一个标记的分布，嗯，
相当简单的东西，嗯，当
我们开始思考强化
学习时，我们会把状态看作是
标记序列，而动作
可以是单独的标记，或者
实际上很常见的是，它们也是
标记序列，所以嗯，虽然
你可以 你可能会很
自然地认为每个 token 都是
一个动作，嗯，在很多现有的研究中，
嗯，人们
实际上倾向于将其视为一种
上下文 Bandit 设置，嗯，你会
获得一些上下文，一些 token 技术的提示序列，
嗯，然后你发出一个
动作，也就是你的响应
序列，所以你知道，
如果你使用聊天 GPT，那么要使这个非常具体，
嗯，你知道在这种带状
问题中，你的第一个状态就是
你输入的任何内容，嗯，你
知道你的动作就是嗯，模型
响应，嗯，现在如果你
有多轮对话，你的状态
就变成了嗯，你知道
第一个输入的第一个响应和
下一个输入，你的动作就是
你的下一个响应，这里有一篇论文，
我在底部提到了这种
对比偏好学习，从
没有 RL 的人类反馈中学习，嗯，这
是一篇非常新的预印本，但如果
你对更多关于
上下文 Bandit 与每个 token 的讨论感兴趣 关于
我们如何在语言模型上进行强化学习的 mdp 类型的公式，
嗯，那篇论文
在正文和附录中都对此进行了很好的讨论，
所以如果
你真的渴望了解更多，我邀请
你去看看，但我将
坚持使用这种 Bandit 公式，
因为这是迄今为止大多数工作所
使用的，也是
我们将要讨论的算法的基础，好的，
好了，好了，
我们
今天要讲的内容基本上分为三个
主要部分，所以我将为
不熟悉 rhf 的人做一个入门介绍，
嗯，这
至少是我们所知的算法，因为
我们并不真正了解，嗯，但
据我们所知，您知道为
我们提供聊天 GPT 做了什么，我将讨论
一种较新的算法，它
简化了
这种原始
强化学习公式中的一些复杂性，这种公式来自人类
对语言模型的反馈，
称为直接偏好优化，嗯，
然后 我将
特别讨论其中的一些应用，
嗯，为了证明嗯，
值得来听听这个
独白，希望它不是独白，
希望我们会有一些好问题，
嗯，好的，第一部分，嗯，
从人类反馈中进行强化学习，嗯，我
将只给你
这个流程的前几个步骤，
在这些大型语言模型上执行 rhf 的方式
基本上有四个主要
步骤，前两个非常
简单，所以第一个只是
呃无监督预训练，这
实际上只是 GPT-3，所以如果你在
步骤零之后停止，你就会得到 GPT-3，你
从互联网上获取大量文本，我们
说的是数万亿字的文本，
你只需对序列进行无监督
生成建模条件，
预测下一个标记，
在大量 A1 100 上重复做很多个
小时，你就会得到呃这个
无监督预训练模型，这
是纯自动回归生成
模型 通过文本，嗯，我们做完这些之后，嗯，
我们实际上会
先在人类演示上进行一些监督微调
，这样基本上就能
得到我们真正感兴趣的行为，从而
学习和改进
模型的分布，嗯，我们
稍后可以再多谈谈为什么
这如此重要，但嗯，基本上
你可以把这前两个步骤看作是在
第零步学习关于世界的背景知识，
然后第一步是，我
对一些
人类编写的演示进行一些微调，你知道
响应这个示例提示的好方法是什么，
比如写一首
关于爵士乐的诗或者其他什么，所以我们对
原始的无监督
预训练模型进行微调，我们得到了这个 sft
模型，然后问题是
我们
接下来做什么，嗯，我想你知道
这里的一个问题也是，为什么
接下来要做什么，我的意思是为什么不停留
在第一步，你知道，嗯，监督
学习非常
有效，这有几个原因，
嗯，特别是两个，一个是，嗯
， 我们稍后再讨论这个问题，扩展
人类演示的注释有点困难，
这真的很费力，对吧？如果你
想要演示，比如
我该如何
回答问题，比如给我写
一首史努比
狗狗风格的关于量子力学的俏皮诗，这
会很困难，而且
收集这些
注释需要时间。而且，如果你想
在很多任务上真正超越人类的表现，
你知道，
模仿人类的行为
不太可能给你一个
实际上比
人类做得更好的策略，所以这些是
从
扩展注释和
获得一个真正强大的模型的角度出发的一些原因，我们
可能想
要这样做，好的，所以希望你
有点相信我们想尝试
用强化学习来训练这种通用语言代理，嗯，我想
你知道，那么最明显的
问题是，好吧，比如
我们要优化什么，什么是
奖励，嗯，它应该有什么属性
现在理想情况下，我们想要一种奖励，
对
人类喜欢的东西给予高奖励，对
人类不喜欢的东西给予低奖励。
这是一个很大的简化，正如我
在幻灯片底部提到的那样，但
关键是，要知道什么
应该有高奖励，实际上需要
了解一些关于人类的事情，所以
使用
一些简单的、硬编码的
或某种封闭形式的奖励可能还不够。我们
可能真的需要从
人类身上引出某种特定的注释或行为来
学习，就像逆强化
学习那样，只不过我们可能不会
从人类那里得到示范，我们
可能会从他们那里得到一些其他类型的
数据，我们可以用这些数据来
推断这种
真正值得优化的奖励函数，所以
问题是我们从哪里得到
这个神秘的奖励函数，嗯，
幸运的是，我要告诉你，嗯，
也许我们首先想到的
就是，好吧，让我们直接向
人类索要奖励，就像
我 我会向你展示模型行为，
如果你真的喜欢它，你会给我
一个大数字，如果你不喜欢它，
你会给我一个小数字，嗯，
你可以这样做，嗯，但我很好奇
你会给这个答案多少分，
比如我们可以举手，
让人们给出1到5的分数，我
知道我说的是1到10，1到
5，比如人们会给多少分，
我们得到了5分，我们得到了
8分，这相当
不错了，嗯，3，好吧，是的，我的意思是这
是一个愚蠢的练习，但我希望它能
清楚地表明，嗯，这是一个未明确
规定的任务，你可能会让
不同的人以
相同的方式对不同的反应进行排名，但
他们在
奖励的单调变换方面可能会有所不同，所以
你的标注者之间可能会有很大的分歧，
这对模型来说会很烦人，
所以也许我们可以做一个更简单的任务，
也许不是
直接给出奖励，而是更容易地
说哪两个反应更有
帮助，嗯，比如谁认为这个
反应 左边更有
帮助，对吧，所以我们会在
很多时候看到更高的一致性，
至少当我们以这种方式征求回复时，
而且嗯，也有很多的人
举手，因为
做出判断比
给我一个数字要容易得多，所以这样就更
容易扩展
注释了，因为把
两个答案放在
一个人面前，说你更喜欢哪一个，
比说给我写一个好的
示范答案，或者想出一个
你认为能体现这个答案有多好的数字要容易得多，
这就是
我们
为了
学习这个奖励
函数而收集人类反馈的方式，所以我们将
获得的反馈将作为
对模型样本的偏好，所以你还
记得我们做了那个监督
微调步骤，所以我们的模型将
大致做我们
关心的事情，嗯，我们接下来要做的是，我们
有一些联合国提示的数据集，我们
从每个提示中收集两个模型样本，
嗯，然后我们把它们放在
人类面前， 人类说哪个
更好，然后我们最终会得到这个数据
集，其中包含三个三元组 X YW 和 Y L，
其中 YW 代表
偏好或获胜的反应，
Y L 代表
失败者。那么问题是，我们
有这些数据，感觉
这对学习奖励很有帮助，
但我们究竟该怎么做呢？嗯，
这就是一些来自经济学文献的较旧的模型，
特别是 WI 模型，它使用
Bradley Terry 模型，该模型将
某些评分函数或效用函数（
可能是奖励函数）与
偏好联系起来，嗯，所以，特别是
Bradley Terry 模型是为
离散选择而构建的，特别是
二元选择设置，人类必须在
两个选择之间做出决定，我们正在
建模他们更
喜欢选择 A 而不是 B 的概率，
我们将其建模为这种 Bolman
分布，嗯，他们的分数差异，这个
分数函数是一种
未观察到的隐式潜在
评分函数，我们假设它
存在，但我们只
观察人类的
选择，现在我们有了一个
概率模型，它将
离散的二元选择（也就是
我们数据中的选择）与某个评分
函数关联起来，我们可以把它变成
一个奖励模型的损失函数，然后
进行最大似然估计，对吧？我们
刚才把这里的 s 替换成了 RFI，也就是
我们的奖励模型，它有一些
参数，然后我们只需要对
我们的
偏好数据集进行最大似然估计，就会得到一个奖励
模型，我们可以对其进行优化，所以
这有点像 RLF 中人类反馈的
部分。
我们在这里遇到了一些问题，
但现在我们找到了答案，这对我们很有好处，所以
我们得到了这些新的提示，嗯，
我们再次使用人类，但
这次我们将使用人类来给
我们提供这些偏好对，这些偏好对
来自 sft 模型的模型样本，你
知道，我的一个办公室同事告诉
我，这个数字对于一个讲座来说太庞大了，
嗯，我希望他错了，嗯，所以第二步，我们
拟合这个奖励 有了这些模型，有了
这些偏好数据，我们就有
了 sft 样本，
所以我们快完成了，嗯，你知道我们
有了这个 sft 模型，它可以做
我们想要做的事情，我们现在有了这个
奖励模型，据称它会给
好东西分配高奖励，
给坏东西分配低奖励，所以现在你
知道我们实际上需要微调
这个
策略，嗯，我在这里说据称，
因为你知道这有点傻，
对吧，就像我们有一个单一的奖励
函数，它给你一个输入和一个
输出，它给你一个数字，
表示它有多好，如果你
稍微思考一下，它就没有
任何意义，对吧，就像人们
对什么是好的，什么是坏的有不同的看法，
所以在某些方面，
rhf 的效果如此之好几乎令人惊讶，因为
我们可以做出这个令人
难以置信的限制性假设，即对
全人类来说只有一个
值得优化的单一奖励函数，这有点
傻，这是
未来研究的一个有趣方向，所以 拜托，
请弄清楚，我们想要
代表各种各样的
价值体系，嗯，各种各样的价值体系，好的，所以我们有这个
奖励函数，现在我们想要学习
一个能够获得高奖励的策略，
嗯，所以这只是强化学习，
对吧，我听说你知道一些，
嗯，现在这是显而易见的一点，我的意思是，
大家有没有想过，
如果我们只是优化这个，会出什么问题，我
不知道，我浏览了一下
你们的讲座，但我很
好奇大家对此有什么猜测，
这不是修辞，如果你有
想法，你可以举手说出你的
想法，你
不必，但你
可以或
不可以，哦，是的，嗯，我会说，也许
只是数据可能是
dis
[音乐]
反应，是的，所以这绝对是个
问题，嗯，所以你会发现，
如果你把这些奖励模型拟合到
真实数据上，就像这个奖励模型
基本上是一个分类器，如果我有
一些数据，比如二元偏好，
而不是响应，如果我符合我的奖励
模型，然后测量它实际上如何准确地将
更高的奖励分配给
人类认为更好的东西，大概在
70% 以下，所以这些
偏好非常嘈杂，
这可能是一个问题，我正在考虑
其他事情，但这是一个很好的观点，
虽然还有其他猜测，我们后面有一个，
是的，所以我的意思是，我不想
给你全部的功劳，因为
就像任何机器学习
讲座中如果有人说好的，所以我们做得很好，
这里可能出什么问题，
你总是可以说分布转移，
这听起来不错，
不幸的是，它就在这里，
所以我必须给你一些功劳，嗯，但
我想谈谈一种特定的
分布转移，嗯，嗯，
如果你还记得的话，我们嗯，答案
是抱歉，我们想在这里添加一些术语，
以保持我们的策略，我们正在
从 sft 模型微调到接近
sft 模型，这样
做的原因是，我们的奖励模型是在
我们的 sft 模型的轨迹或响应对上进行训练的，所以根据
定义，我们的 奖励模型可能
最准确地针对
sfd 模型分配高
可能性的事物，因此，如果我们在
Oblivion 不受约束的情况下进行优化，
我们最终可能会进入
行动空间的某个区域，我们的
奖励模型会给我们完全垃圾的
奖励，嗯，我们会
过度优化我们的奖励模型，所以
看起来我们的
奖励会越来越高，但是
当你把这些反应放在
人类面前时，他们会说那是
垃圾，好吧，所以这就是
我的意思，是的，这是分布转变，
嗯，现在我们有了整个
目标，嗯，我们将使用
po 来优化整个过程，我
不知道人们是否熟悉
po，它是一种 RL 算法，您不必
使用 po，
但这就是 John Schulman 决定的，
因此这就是我们
生活的世界，嗯，这是一个 PO，这是一个 p 世界，
嗯，所以基本上最终
完成了我们 rhf 的流程，
所以我们这样做 无监督
预训练，我们进行 sft，我们拟合这个
奖励模型，然后最后，我们，嗯，
我们
再次获取未标记提示的数据集，我们可能有一个
新的未标记提示数据集，我们
用这个奖励模型进行 RL，最后得到
一个策略，嗯，这基本上是
聊天 GPT，
到目前为止，嗯，这个过程的另一种观点，
我把它包括在这里，因为它
比我的图更复杂，
我觉得它有点让我看起来更好，
嗯，出于这个原因，我把它包括在
这里，嗯，但是这里的重点真的，嗯，从
教学上讲，这个流程
真的很复杂，嗯，你知道，
如果你把 Po 的所有实际部分都包括进去，
我们有嗯，那种
旧策略，我们有 sft 模型，
我们的 K 约束就是从中计算出来的，我们
有一个奖励模型，我们还有一个
价值函数，我们在
训练模型时在线拟合它，我们
还有我们的策略，
然后我们还有一个重放缓冲区，
这非常复杂，嗯，这是
你知道，
尽管如此，这个方法
确实有效，嗯，所以这是来自
嗯引用未说明的 GPT
论文嗯，这篇论文是在
da vinci3 公开发布时发表的，嗯，它
是 GPT 的直接前身
嗯，
这里有几点需要指出，我的意思
是，首先，我们正在评估胜率，
所以 y-AIS 基本上是
人类对该模型的响应比对
1750 亿
参数 sft 模型的响应更喜欢的频率，嗯，这就是为什么在
sft 曲线上它在点 5 结束嗯，
这里有趣的是嗯，人类
更喜欢 1.3b rhf 模型，而不是
175b sft 模型 如果你没有
使用过语言模型，这些
数字只是空中飞舞的数字，
但 1.3b 就像我可以
在我的 20 70 TI 175b 上运行它一样，就像我
付钱给一个工程团队来
微调这个模型，嗯，所以这
是一种非常有趣的
规模差异，嗯，而且
重要的是，
即使模型变得
非常大，收益也会继续显现出来，所以这并不是说
当我们有一个非常小的模型时，这只会帮助我们做得更好，
即使我们
有一个非常大的模型，我们也有很大的
容量可以使用，嗯，这
仍然很有帮助，嗯，你知道 gp4
据称就像在这里，这
可能是错误的信息，所以带着
颗粒感接受它，但是嗯，好的，嗯，
RF Works 嗯，我想提一下
我认为很棒的几个快速变化，
所以这是一篇非常
新的论文，讨论了
人类反馈的质量多样性，嗯，
在这里他们所做的是使用
人类反馈，嗯，不是学习奖励
函数，而是 学习一种
多样性指标，基本上是一个潜在空间，可以
捕捉人类认为
或多或少相似的东西。所以他们会向人类展示
三幅图像，然后说，对于图像 a，
呃，让我们将它与图像 B 和 C 进行比较，
你认为哪一个与
图像 a 更相似。他们使用这种
反馈来学习这个潜在的潜在
空间。他们在这里展示的
是，他们写了一些提示，
比如宇航员骑着马，嗯，
他们用了 clip，人们知道 clip
是什么，有人点头表示同意。嗯，
clip 是一个模型，它将
文本和图像联合嵌入到一个联合嵌入
空间中，这样你就可以编写文本并
找到相似的图像，或者嵌入
图像并为它们添加标题。所以
他们所做的就是嵌入这个
标题，就像宇航员骑着马一样。
他们在左边找到了
八幅最接近该
标签的图像，对吧，它们都是正确的，但
它们都非常相似。他们在
右边所做的是，他们对
学习到的空间进行了划分。
从人类反馈到 16 个单元格，
然后他们在每个单元格中找到了最接近的图像，
有趣的是，
你仍然可以获得与提示符的良好匹配，
但是
通过以这种方式约束你的图像，你显然具有更高的多样性，
好的，所以
这是我们可以从人类反馈中学到的另一件事，
它不仅仅是
学习奖励
模型，另一件事是需要的，即 RL
AI 反馈，所以这就是
Claude Works 的方式，嗯，
你知道，这是人类学
在类似的聊天机器人
领域提供的一种服务，基本上这里的交易是，我们
想要学习的对话模型
不仅仅是有用的，它不仅仅是
它们会告诉你任何你想要的东西，
我们还希望它们有某种
护栏，对吧，我们不希望它们
告诉你如何做
坏事，我甚至不会
举例子，因为每个人对
什么是坏事都有不同意见，但你知道，
有些查询我们希望这些模型能够
拒绝，对吧，嗯，一种方法
是 就像问人类一样，
这里有一对反应，哪个更
有害，嗯，但你可以做的另一件事
是，你可以用有用的数据做正常的rhf事情，
所以只要你
有人类说的数据，哪个
反应更好，然后你实际上
使用从中获得的模型来
给你无害的注释，所以
不必问人类，那么
哪个更有害，你只需展示这个
纯粹有用的模型反应，它就
会给你那些标签，这样就可以了，
所以总结一下rhf
部分，嗯，这里的关键点之一
是，人类可以通过比较提供比通过演示更具可扩展性的
反馈，
这是
一个更普遍的
现象或一般问题的一个特例，
就像
对强大的模型进行可扩展监督的最佳方法是什么，对吧，
而且似乎呃，你知道这是
一种有用的方法，我认为它
在收集大量
数据和质量方面都是可扩展的，
如果我们
想要让模型比
人类更好，那么它可能更具可扩展性，这样做会更容易
用比较而不是用
演示，嗯，下一个重点是，
我们使用这个理论偏好模型
或选择模型来推导
奖励学习的目标，这才是我们
工作的重点，
一旦我们做到了这一点，我们只需要用
或多或少现成的强化学习
和学习奖励进行微调，我们就能得到一个很棒的模型，好的，嗯，rlf 结束了，还有
什么
问题吗，好的，嗯，现在我们要
讨论 DPO，嗯，
抱歉，这只是一个自攻击模型，
它只是一个语言
模型，它接受一个 token 序列，
然后给出下一个
token 的分布，然后当你
推出你的策略时，你会得到一个 token 的即时
序列，你预测
下一个 token 会从这个
分布中采样，就像一个情节就像一个
完整的序列，你想
在每个时间步都采取一个动作，嗯，是的，所以
这取决于我们在
这里所说的动作，你可以将动作称为
单个 token，也可以将
动作称为整个输出序列，直到
你的 模型输出类似于
序列结束标记，嗯，
所以，如果从轨迹或
情节的角度来看，它就像一个动作，
或者
如果从每个标记的角度
来看，它是一系列动作，是的，他们尝试了
偏好模型，就像这样，似乎
总是在两个选项之间进行二分法，已经
尝试过了，是的，
所以，Bradley Terry 有很多非常
简单的概括，可以对
许多
响应进行排名，这是一个
叫做宽松门襟模型的通用家族，嗯，
它看起来非常类似于 Bradley
Terry 模型，嗯，是的，
还有很多问题，
当我们选择这个偏好
模型时，我们实际上假设的
底层参数是什么，我们
假设这个偏好
模型将奖励与偏好联系起来，
实际上并不明显，
可能是其他东西，比如
优势，最近有一些
研究在研究这个问题，就像
我之前提到的那篇论文中提到的对比
偏好学习，他们讨论了
每个标记与每个序列，他们
谈到了这一点，好吧，嗯，我们来谈谈
DPO，嗯，我喜欢 这些问题，嗯，
那么，rhf 和 poo 有什么不理想的呢？
正如我之前描述的，存在
实现复杂性问题，
存在资源需求。所以
我们有各种各样的模型，
有奖励模型、
价值模型、策略、参考
模型，嗯，还有训练能力，所以
当我们使用它们时，奖励实际上具有额外的自由度，
因为这个损失函数只
关心奖励之间的差异，
所以我可以
针对特定的提示移动我的奖励，对吧？如果我们固定
X，我可以用一个
不改变损失的任意常数来移动奖励，
真正的问题是这个
常数对于每个提示都可能不同，
所以我们
至少在概念上可以得到一个
奖励函数，对于特定的
提示，相对奖励是有意义的，
但你甚至不能比较
不同输入的奖励，所以这会使得
我们在进行 RL 时很难拟合价值函数，所以
让我们摆脱所有这些问题，嗯，
我将讨论这个
算法直接偏好优化，
它基本上简化了
流程。 我们已经讨论过了，
这里的妙处在于，如果我们
以一种特殊的方式参数化奖励模型，
如果我们
为奖励模型选择一种特定的架构，
我们就可以
以封闭形式提取该奖励模型的最优策略，
我们实际上不必进行任何
强化学习，
这很好，嗯，这里的主要思想
是，
最优策略和奖励模型之间存在一种一对一的对应关系，因此，
给定一个特定的奖励模型或
奖励函数，最优策略有一个封闭形式的
表达式，虽然
很难实现，但我们实际上仍然可以使用它，
嗯，至少在我们的训练目标中，使
策略训练变得更容易，嗯，我将向
你们展示如何，好的，所以我们有这个RF
目标，我们已经讨论过了，
这不是新的，所以我们
这里有提示的数据集，然后我们对模型的
响应有这个期望，
我们想要高奖励，我们想要
低K，没有什么新的，
嗯，这是针对任何奖励
函数的，你可以展示的
是，这就像几行
代数，它在 DPO 论文和
其他几篇论文都提到了，
这里有一个近似形式的最优策略，也就是这个，嗯，
希望它有点
直观，嗯，你不会立刻想到
这个，但是你
知道，我们
分配给特定响应的概率，
是
我们原始的 sft 模型（参考模型分配给该响应的概率）
和指数奖励的乘积，
所以我们最终会给那些参考模型分配了相当高概率并获得高奖励的东西
分配高概率，
好吧，这有点
疯狂，因为我们最初的
目标函数实际上是高
奖励低 KL，这有点道理，我们
会看到类似这样的结果，我们只需要将
它除以 Z，因为嗯，我们
需要进行归一化，所以它是一个
诚实
分布，现在再次，这是在
正轨上，因为这又回到了
这个想法，即动作是完整的，就像
响应一样，这是所有
可能的（不是下一个标记）的总和，所有可能的
序列无法计算
这个，但尽管如此，我们也不会 需要嗯，
我们唯一要做的另一件事
就是代数运算，好吧，
我们只需要取第二行，
然后重新排列它，这样我们就可以
得到奖励函数，它是
最优策略的函数，这
可能就像我们为什么要这样做，嗯，
但是你很快就会看到，
现在希望这有点道理，
就像我们现在
基本上有这个想法，我们可以将
奖励函数参数化为
某个策略
和参考模型之间的对数概率比，这是
最优策略，但你实际上可以
对任何
策略都这样做，当我们把所有这些放在一起时，可以
展示这
实际上可以让我们做什么，因为到目前为止，
我们实际上只是做了一些
愚蠢的代数运算，嗯，你知道，我们采用了我们的
目标，我们为它写下了一个难以处理的
封闭形式解，然后我们
做了一些似乎没什么用的代数运算
嗯，它有什么用处嗯，
嗯，最初我们
开始的是奖励函数的损失函数，
对吧，这是Bradley
Terry 我们在 rhf 中使用的损失函数，将
偏好数据转化为
奖励函数，而这个奖励函数并没有改变
我们要做的事情，我们基本上要在
奖励函数和策略之间添加一个转换，将之前使用的
损失函数转化为
直接作用于策略的损失函数，这样我们就可以跳过
学习奖励模型然后将
其提炼成策略的阶段，我们可以
直接在偏好数据上进行训练
并直接优化策略，这
就是为什么它被称为直接
偏好优化，所以我们再次有了
损失函数和奖励函数，
好的，这与 rhf 中的相同，
这只是来自 brle Terry
模型，我的意思是人们记得
这个，
好的，所以这就是这个损失
函数，我们坚持对
所选事物和被
拒绝事物进行奖励，嗯，
这个模型的概率就是
这两个奖励之间差异的 S 型函数，然后我们训练我们的
奖励函数，这样它就会
具有最大
似然，嗯，所以这只是 相同的
损失函数，我们要做的就是
使用
我在上一张幻灯片中展示的这种变换，
我们展示了，对于一个
特定的
策略，其
最优的奖励函数采用这种
形式，所以我们可以在这里坚持任何
策略，然后我们得到呃，如果
我们再次计算这个量，
这是难以处理的，但假设
我们可以，这是对响应的奖励函数，
你知道，这是由奖励
函数评估的，这是最优策略，所以
这里我们有
策略和奖励函数之间的这种变换
是
双向的，这只是对
最优
策略进行代数运算，关键是如果我们坚持这种
形式，那么现在我们有了一种奖励
函数，它不再只是像
Transformer 那样的一般函数，你取一个
序列，它会给你一个缩放器，现在
它有这种特殊形式，我们
实际上有一个自回归模型
，我们计算响应的对数概率，然后
根据参考模型减去相同响应的对数概率，或者
sft 模型，区别在于
奖励，所以高奖励的东西
就是你的策略
分配的概率比 sft
模型更高的东西，低词的东西就是
你的模型分配的
概率更低的东西，现在如果我们坚持这个
参数化的奖励，那么
这基本上只是一个
如何参数化奖励
函数的特定选择，Z 取消了，因为
我们假设我们
对“被选中”和“被拒绝”的
例子有相同的提示，所以当我们把这个
棘手的奖励参数化坚持到
我们用来学习
奖励函数的正常 Bradley Terry 损失中时，我们实际上得到了
一个完全可处理的目标，嗯，
我们可以用它来直接训练我们的
策略，那就是 DPO 嗯，所以你知道
这个损失函数实际上就是
奖励模型损失，
因为我们有这个将
策略与奖励函数联系起来的恒等式，嗯，我们可以把
这个奖励函数的损失函数直接转化
为策略的损失函数，
另一种思考方式是，我们
不是训练一个奖励
模型，然后训练一个策略以在
该奖励模型下获得高奖励，而是
训练一个 策略 Pi
Theta，它是
奖励函数的最优策略，它满足我们
偏好数据的 BRARY 模型，
这有点像是从另一个
方向思考
这个问题，好吧，这有点
让人不知所措，但是嗯，但这
几乎就是 DPO，所以再次，是的，
用对数 Z 项代入，取消了，
所以我们最终得到了一个简单的
东西，这
只是一个分类目标，我们不需要
在训练期间进行任何 rollout，
嗯，我们可以直接计算
它，有趣的是，
嗯，我
之前提到过自由度，我们在这里失去了
自由度，因为这个东西是
标准化的，嗯，但就我们可以计算的奖励函数集而言，我们实际上并没有失去
任何表现力，对吧，所以
我们之前遇到过这个问题，
好吧，我有一些奖励，
现在假设我已经这样参数化了我的
奖励函数，比如说，
我分配了一些 XY 对奖励 5，
嗯，我们之前遇到过这个问题，你
可以直接上移所有
你知道，对于该提示的奖励，某个常数，
以及你的奖励函数，你的最优
策略根本不会改变，这不再是
事实，因为这是标准化的，
这是一个概率分布，所以
为了将所有响应的奖励提高
一定量，这意味着我们
必须增加
所有响应的对数概率，我们不能这样做，对吧，你
不能增加所有事物的对数概率，
它不再是概率
分布，它不会
加起来等于1，所以这就是我们如何
摆脱额外的
自由度，你可以在论文中看到为什么
它不会失去表现力，但这
并不是非常重要，好的，这是
最令人难以抗拒的部分，所以我想，嗯，
我们为什么感到
担忧，是的，
所以这只是一个模型，
对不起，这很糟糕，呃，这是sft
模型，所以它永远不会更新，pyre在
这里是固定的，我们唯一会
改变的是pi Theta，
没有之前的批次，就像在po中一样，
你可以把它永远从你的
脑海中抹去，我们只有一个
正在学习的策略 这里没有价值
函数，没有嗯， ...
那些你
挑选出来的，哦，那个
真的非常好，所以
你有点像呃，你可以
在
行为空间里做一些推断，嗯，特别是
你可以做的是，你可以迭代这个，
对吧，你做一轮这个，你会
得到一个比
你的sft模型稍微好一点的模型，然后你重复
这个模型的样本轨迹，嗯，
你会得到对那些的偏好，嗯，你
再次训练，等等
等等，这样你每次都可以取得
一点进步，
不断改进你的
模型，是吗，不，不行，我的
意思是，我明白为什么这
肯定会有帮助，嗯，有点奇怪，如果
不这样做，它似乎就不再符合政策了，嗯，不过我想，
另一方面，弄清楚一个奖励
模型，然后进行政策推广，
这可能会优化它，
也可能像你说的那样，陷入
奇怪的语言生成领域，所以我
想纠正一件事，我认为
这真的很微妙，我不想
陷入其中 嗯，
虽然我们没有
进行任何推广，但我认为
说 dpos 偏离策略并不完全正确，
因为我们认为这是
一种偏离策略或离线学习，
因为我们在某种程度上是
用一些离线数据来拟合我们的策略，这些数据不是
来自我们的策略，我们
希望能够改进它，显然，这
是离线的，因为我们没有
从我们的策略中采样，但 PO 是
在线的，因为我们只是
使用策略样本为我们的奖励模型找到一个近似的最优策略，
我们保证能
找到
我们拟合的奖励模型的精确最优策略，如果我们把
这个对数比称为
我们的隐式奖励，我们保证能
得到在
该策略的预期下对该奖励而言最优的东西，
所以它不符合策略，
因为我们没有从策略中采样，但很
容易认为这可能是一个
缺点，而实际上它是
这里有一个优势，我们不
从模型中采样，并没有放弃任何东西，
只是我们恰好有一个接近
最优策略的解决方案，所以如果有什么
不同的话，这应该会给你一些
比 po 更好的东西，
好的，我会尝试继续，嗯，但
谢谢你的提问，嗯，所以
回到这个大局，嗯，
DPO 实际上在做什么，嗯，如果
我们看看这里的四个主要
步骤，嗯，它实际上在这个
右下角，动作
发生的地方，所以嗯，与
基于 Poo 的方法相比，我们基本上
跳过了这两个步骤，我们不再需要
优化策略了，嗯，
相反，嗯，我们只需对奖励
的这个特定参数进行微调，
我们进行这个简单的
转换，不需要任何
训练就可以得到
最适合该
奖励的策略，对吧，
所以不使用
新的，是的，你能谈谈是的，
嗯，所以嗯，这是嗯，所以你
完全正确，所以当
你看这里的 PO 图时， 我们有
一条从新提示到
策略优化过程的线，我们
在 DPO 中不再这样做了，因为我们只
使用在拟合奖励模型时使用的提示，
所以很
自然地会想，嗯，Poo
使用这些未标记的
提示会有什么用处吗？你会有一个
泛化能力更强的策略吗？或者
类似这样的，嗯，就我们目前所知，没有，嗯，
因为事情是这样的，虽然
你使用了新的
未标记提示，但
你的
奖励模型对这些
提示的响应准确性仍然会成为瓶颈，对吧？所以，如果新提示
有用，那是因为你的
奖励模型给了你准确的奖励，
但问题是 DPO 使用的提示与
你
在 Po 中用来拟合奖励模型的提示完全相同，所以如果
奖励模型泛化得很好，那么
DPO 策略也应该泛化得很好，
你可能会争辩说，这是一个
不着边际的论点，但有点像
思考过程，至少当我们
做了一些 从
经验上评估，Po 似乎并不
比 DPO 具有更好的泛化能力，
我们评估的模型
恰恰相反，所以这是一个非常
有趣的问题，它根本没有
得到令人满意的评估，但在
一些轶事实验中，我想说，
虽然有理由担心，但
似乎没有一个很大的
泛化问题，是的，这是一个很好的
问题，
嗯，好的，嗯，是的，我们摆脱了这幅
图，这很好，嗯，摆脱了
很多复杂性，很酷，嗯，我认为有一件事
很有用，那就是看看
DPO 损失的梯度是什么样
的，因为我认为这
基本上告诉你它是如何工作的，嗯，
除了我做的这个漫长而愚蠢的推导之外，
当我们
再次对策略参数进行区分时，这个梯度是什么？这里的 pyra 是
固定的，这只是你的 Frozen sft 模型，
我应该做 sft ref 是
通常使用的，我很抱歉，嗯，所以
梯度看起来像这样，这是一个
你知道这里面有很多东西，
但
如果我们一步一步来，其实并没有那么复杂。所以
从内部来看，
我们只是对
数据集的期望，这没什么不同。
这里实际上只有两件事，
我们有一个项对
所选
内容进行最大似然估计，另一个项对不喜欢的内容进行
最小似然估计或不太可能估计，
所以DPO
所做的就是将喜欢的内容向上推，将
不喜欢的内容向下推。但
我们还有每个示例的
权重，例如，每个
示例的权重是
奖励差异的S形函数，但它是另一个
方向，所以这里是
失败者的奖励减去成功者的奖励。
这意味着，
当奖励模型在
偏好对上不正确时，我们会获得更高的权重，而当
它已经正确时，我们会获得较低的权重。
所以这
基本上就是，我只会训练
我的奖励模型
分配
不正确的内容。 极性决定哪种响应
更好，这基本上是隐含的，
我们的 KL 约束就在这里，对吧？
因为一旦我们得到的
奖励比不喜欢的
奖励要好一点，我们就会
停止对这个例子的训练，
因为这个缩放
因子将变为零，在下一张
幻灯片中，我将向你们展示
只使用这个东西
而不使用每个例子的权重之间的比较，嗯，
和使用完整的 DPO 损失，
这
有意义吗？是的，我认为这也许是
更直观的方式来了解
DPO 的实际作用，老实说，嗯
，嗯，这是很棒的 archit，
他是这篇论文的作者之一，
最初做了这个分析，我
认为这真的很棒，
很有见地，嗯，是的，所以 beta 值会
出来，这只是
我们的 KL 约束的强度，
本质上是一个学习率，好的，所以这是一个
快速的实验，然后我们将继续
应用，嗯，所以我们要做的是
看看 DPO 和
其他方法是如何权衡的
因为我们的目标
没有改变，我们
仍然在 K 约束下进行奖励最大化，
所以
当我们比较算法时，我们可能想知道的是，
它们如何有效地
进行这种权衡，如果你有一个
特定的 KL 预算，
你能从中获得多少奖励，这就是
我们要做的事情，在一个相对简单的
环境中，我们将做一个
愚蠢的任务，我们希望最大化
生成模型的情绪，所以
我们将得到
一个电影评论的开头，我们只想
以最积极的方式完成它，
所以我们将生成
一个合成数据集，我们
使用 GPT2 生成电影评论对，呃，以
电影
评论开头为前缀，这样它就会生成其余部分，
我们将使用一个基本事实奖励
函数，这在现实世界中通常没有，
但为了我们能够进行
研究，我们将拥有它，这
只是一个情绪分类器，嗯，这
就是我们将要用来获取
偏好数据的东西，所以我们只是
用一对评论来评估
情绪分类器，如果每个人都有
更积极的情绪，那就是
首选的，那么我们将
使用 DPO rhf 和 p 以及其他一些
方法进行训练，嗯，然后我们会看到，
因为我们有 ground truth 奖励
函数，我们可以绘制一条曲线，
其中 x 轴是 KL，y 轴
是奖励，我们可以看到它是什么
样子的，这有意义吗，
好吧，嗯，是的，我们有一些基线，
我们会看到，实际上 H 我们是否
有基础模型，好
问题，嗯，所以我们将进行
首选微调，这
只是如果你只对
所选的完成进行微调，你可能会想，
也许这会有所作为，我们
在那里，嗯，我们有不可能性，
所以这是你做 DPO 的地方，但我们
没有每个例子的重要性权重，
所以你只是
对所有首选的东西做最大似然，
对所有不首选的东西做最小似然，
也许这会起作用，嗯，剧透一下，它
不起作用，所以我们有 DPO，嗯，我们有
po 使用学习奖励，所以这个
就像正常的完整 rhf 流程一样，我们
也有使用真实
奖励函数的 po，所以如果我们甚至不
担心 HF 部分（我们
学习奖励），而只对
真实奖励函数进行 poo，会发生什么，
这就是发生的事情，嗯，所以
有趣的是，DPO 确实提供了
最强的奖励，这里的 KL Frontier
嗯，嗯，po 实际上并没有
给出最佳奖励，嗯，
即使它使用真实
奖励函数进行优化，嗯，
所以这有点有趣，而且
不太明显，我的意思是基于
我们对参考模型有这个 KL 约束的事实，
我们有
可能无法真正获得最大
可能的奖励，但这里有一些
差距，
嗯，嗯，这种首选的 ft 嗯
方法真的不太
好用，如果你担心这一点，
同样
有趣的是这种不太可能的事情，
对吧，这就是我们摆脱它的地方，
例如 DPO 中的重要性权重，你
可以在这里得到一些好的策略，哦，
我 应该说，很抱歉，我应该
在一开始就解释清楚，
这里的每个点都是一个模型检查点，所以
我们对
每种方法都进行了很多次训练，使用了不同的
超参数，比如 KL
约束，我们只是评估了
每个检查点在 KL 中的奖励，然后将
它们全部绘制
在一起，左上角是
我们想要的位置，
这有意义吗？为什么像这个
DPO 曲线这样是好的曲线，这
非常重要，否则幻灯片就是在
浪费时间，是的，
更多
信息，是的，
是的，这是一个很好的问题，嗯，所以它
不是，我的意思是，嗯，在这种情况下，
关于地面实况奖励函数的事情
是，它是稀疏的，所以你
要么得到 1，要么得到 0，所以如果
响应有点高于某个
积极阈值，分类器
完全未校准，它基本上
只会给你 1，如果它
低于某个阈值，它会给你一个 0，
所以实际上有一些
早期的 rhf 工作，有一些
有趣的结果，
你实际上可以通过进行人工
反馈 通过使用人工反馈来
学习奖励函数，你实际上可以
得到一个
比真实奖励函数形状更好
、更容易学习的奖励函数，嗯，所以
在 PR 中，原则上，
这种结果可能是这样的：
你可能拥有真实奖励
函数，但实际上
很难从中学习，所以
你最终从人工反馈中得到的更
容易，所以嘈杂的偏好在
这里，我们的偏好不是
嘈杂的，只是你最终可能会得到
一个奖励函数，因为你有点像是
容量受限，
或者它是预先训练好的，所以它有一些很好的
归纳偏差，嗯，它最终
可能会得到一个更平滑的奖励
函数，
比总是
给你 1 或
0 的稀疏函数更容易学习，好吧，嗯，好吧，我们的
时间不多了，所以我要加快速度，
嗯，但我认为值得一看的一件事
是，仅仅改变
我们原始 l
或原始目标中的系数 beta，这
实际上如何改变我们最终的 KL
嗯，幸运的是，它的变化
非常可预测，嗯，所以这就像四
五次不同的训练运行，
我们只是绘制了 beta 的对数和
KL 的对数，嗯，这真的
值得一提，因为当我们做 po
嗯 beta 时，并没有真正完全定义
我们最终会得到的 KL，所以如果你
用同一个 beta 运行两次 po，你会
得到两个 KL 非常不同的策略，嗯，
因为它非常嘈杂，而且有点
不稳定，嗯，所以人们
在实践中使用动态 beta 控制器，
他们在
训练期间实际测量策略 KL，并
动态增加或减少 beta 以尝试将
K 放在特定区域，
你知道这有点烦人，不必
真的这样做，好吧，还有一
件事，DPO 什么时候会失败嗯，让
我们看这个例子，假设我们
有一些输入，就像一些很长的
Reddit 帖子，我们有我们选择和
拒绝的回复，它们都是
摘要，它们是 tldr 嗯，然后
这就是 来自模型的样本
看起来像来自我们的参考模型，
DPO 不会在这里学习生成
tldr 标记，嗯，这在
我们
做这个
实验的时候真的让我们很烦很沮丧，我会
不假思索地问为什么，但我们没有时间，
所以我只会问
为什么，嗯，如果我们看一下
损失的梯度，嗯，我们就能明白为什么我们
可以忽略大部分内容，我们只看损失
中的这两个最大和最小似然项，我们
可以把它们分开，因为它
只是序列中
每个时间步的每个条件的对数概率之和，
嗯，
这里的关键是嗯，选择和拒绝中的第一个标记
是 tldr 对，所以
这个时间步长为零的项
对于这两个来说都是相同的，它
会被抵消，所以
你永远不会学到任何关于
第一个标记的知识，嗯，很糟糕，但你知道，嗯，这就是
为什么这样做很重要 sft
阶段也是如此，所以当你做 sft 时，
你会学习 tldr 位，
然后当你做 rhf 时，你只是在
学习好东西和坏东西之间的 Delta，
好吧，但是，但是，虽然这是
一个有点傻的例子，但这就是我们
最初发现它的方式，
它确实让你想知道，
这个问题的更通用的版本是什么，在
我们选择和拒绝的
东西之间还有哪些其他的退化对称性或某种等价关系，我们可能学不到，因为
我们只关注
它们之间的差异，这是值得思考的，
好的，所以从 DPO 中得到的启示是，我们可以
从 rhf 中删除 RL 训练循环，是的，
DPO 很简单，很稳定，而且
运行起来更便宜，是的，它确实优化了
相同的目标，所以它不是一个
更便宜的近似值，你
可以或多或少地把它看作是更
便宜的同一件事，嗯，非常
小的星号，嗯，还有 还有很多
正在进行的工作，我们仍在理解 DPO，
并总体上改进 rhf，我的意思是
DPO 并不是故事的终点，
它只是另一个想法，
在接下来的六个月里，可能会有新的、更好的想法，
了解这个
领域的发展，
嗯，好的，所以在我们最后的五分钟里，
我是 G，简要讨论一下
DPO 的一个应用，它会进行得
很快，因为我们只有五
分钟，嗯，但是我想
简要谈谈事实，所以我们知道，嗯，
语言模型是不可信的，
所以就有了这个臭名昭著的酒吧演示，在
你知道大张旗鼓的巴德之后，
就像说了一些关于詹姆斯
网络太空望远镜的虚假事情，嗯，
不仅仅是巴德，所以即使在备受赞誉的大型演示中，
嗯，他们做了一个
很棒的演示，他们分析了差距
季度报告，这不是我的分析，
这是这个人的分析，我在
那里有链接，所以我不想
为此邀功，但这是很棒的分析，所以
就像好的，Bing 说的第一件事
很棒，就像明白了 对，太对
了，哇，太聪明了，然后下
一部分就像是，好吧，它给出了
它说的调整后的数字，
它们实际上是未调整的
数字，这非常微妙，
下一个就像是，好吧，
每股收益只是她不在
被告席上，你只是编造了那个，嗯，事实
证明
最后一段也是如此，所以预期的
增长是不正确的，未来
前景也是编造的，所以这
真的令人失望嗯，这里还有另一个
例子，我的顾问嗯，如果
你问聊天 GPT 切尔西在哪里获得
博士学位，他们会说伯克利，这是真的
嗯，如果你说她在哪里获得博士学位，用
一句话回答伯克利，这是
真的，如果你说只给我大学的名字
而不是完整的句子，
它会说斯坦福，这不是
抽样的怪癖，它是可重复的呃
我今天早上做的嗯，这就像
你到底是怎么回事，你知道所以这只是告诉
你 当
模型要判断
真假时，这是多么奇怪和神秘的事情啊，
如果你
对这个问题感兴趣的话，我认为这本身就是一个研究项目的起点，但
无论如何，使用这些
模型都很诱人，这是有问题的，我
有点被这个问题搞糊涂了，
也有一些律师使用呃
聊天 gbt 来提出一些非常
有趣的案例，所以我们能不能像
rhf 那样，用我们的方式来提高事实性，嗯，
我的意思是，如果你回到这个大局，
这是 DPO 的图景，比如
我们甚至希望事实性从何
而来，我基本上把
它们分成两部分，我们有
预训练，我们看到数万亿个
token，大量的数据，也许我们会
说这大致是我们学习
什么是真什么是假的地方，我们
可能希望在这些更轻量的
微调阶段，我们会看到
数量级更少的数据，我们
实际上没有足够的数据来学习什么是
真什么是假，但我们可以
学习，好的，我只想说真实的
东西，好的，嗯，所以，这大概就是
我将其分解的方式，
实际上有研究
表明，这并非完全是
想象出来的思考方式，
另一件重要的事情是，好的，我们
已经在做rhf了，为什么我们实际上需要
做什么特别的事情来获得事实性
呢？答案是，rhf鼓励
让人类快乐的行为，对吧？
而进行事实核查并不能让
人类快乐，让我告诉你，
所以，决定这是否
正确比
决定我是否喜欢它要困难得多，我
这么说不仅仅是因为它听起来不错，
人类学在几个月前对此进行了一项很好的研究，
他们对
响应的哪些属性进行了排名，
使其最有可能成为首选，
而诚实并没有真正
排在首位，事实上，
如果它与你的信念一致，令人惊讶的是，它是
人们是否会喜欢
某样东西的最具预测性的特征，所以我们在
做的时候必须非常小心 我们的 LHF，因为它
甚至不确定呃，甚至
不清楚我们最终要优化什么奖励，
好的，所以嗯，我们基本上想要
偏好数据，告诉我们什么比
什么时候更符合事实
嗯，人类不擅长这个，好的，这是
ARL 通过 AI 反馈的好机会，
我们真的没有
时间了，但重点是我们基本上可以
通过使用嗯，现有的
语言模型和使用
像维基百科这样的参考资料来自动进行一些真实性评分嗯，我们可以得到这个
偏好数据集嗯，我们有
一个输入，然后两个响应，一个
比另一个更符合事实嗯，我们可以
用 DPO 来训练它，我们可以评估它，
我们在生物生成和
医学问答上对其进行评估嗯，事实
证明这是有效的，所以如果我们与
这里的 sft 模型进行比较嗯，进行这种
事实性调整可以显着减少
平均每个响应的错误事实数量，并增加
每个响应的正确事实数量，
这是唯一的方法，你知道如果我们看一下
rhf，rhf 不会给出 严格
改进，
好的，其他一些不太
重要的事情，嗯，关于在
更具事实性的 llms 上进行训练的要点，这非常
重要，这真的很难，单靠 rlf
并不能真正解决问题，因为
人类不擅长这个，嗯，我们
可以在没有人类反馈的情况下
使用 DPO 和某种 AI 生成的
标签来进行 RL，以提高事实性，所以
在我最后 30 秒总结一下，嗯
rlf 嗯，让这些通用 llms
更有用，嗯，只需
一点点 R 嗯，选择这个奖励
函数非常重要，但
对人类来说也非常困难，我们
可以使用从偏好标签中推断出的隐式奖励，这些
这些
离散选择呃来学习这个
奖励嗯经典的 rhf 效果很好，但
它非常复杂，DPO 要简单得多，
没有任何近似值，我们可以
使用 DPO 为了减少幻觉之类的事情，
嗯，这只是
自动偏好生成，我们不需要
任何人来做这件事，还有
很多其他可能的应用和
问题，但我们经常处于
数据底层，我想
对我的合作者表示衷心的感谢，因为这
不仅仅是我的工作，这是
很多人所做的非常了不起的工作，
嗯，我感谢大家今天的到来，
好吧，所以我们没时间了，之后我会在
外面闲逛，如果大家有
问题，嗯，这是我在推特上的电子邮件，
呃，如果你也想跟进我，
SE 有一个关于机器学习的课程，
从人类偏好开始，幻灯片
在线，所以
如果你
好奇的话，你可以看看那些，哦，是的，我
知道你，是的，你只要把
报告发给我就行了