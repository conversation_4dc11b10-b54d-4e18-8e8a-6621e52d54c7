
### CS 285 讲座 23，第 1 部分：挑战与未解决的问题

欢迎来到 CS 285 的最后一堂课。今天我们将讨论挑战和未解决的问题。首先让我们简单回顾一下课程中涵盖的内容。我们涵盖的内容非常丰富，方法也多种多样。我将绘制一张图来展示不同的原理以及它们之间的关系。其根本在于基于学习的控制，因此我们基本上在课程中广泛涵盖了基于学习的控制方法。

基于学习的控制方法包括模仿学习方法（即从示范监督中学习）和强化学习方法（即从奖励中学习）。强化学习方法包括经典的无模型 RL 算法，例如策略梯度、基于价值的方法。基于价值的方法和策略梯度的结合产生了 Actor-Critic 方法。我们以深度 Q 学习为例，介绍了一种特定的基于价值的方法，例如 Q 函数；Actor-Critic 方法（例如 SAC 等），以及高级策略梯度方法（例如 TRPO 和 PPO）。此外，还有基于模型的控制，而基于模型的控制不一定是基于学习的。因此，我们讨论的那些规划和控制方法，例如 LQR，它们本身并不一定与学习有关，但它们可以与学习相结合，从而产生基于模型的强化学习方法。最纯粹的几种基于模型的强化学习方法不使用策略，只是简单地训练一个模型并通过该模型进行规划。它们实际上并没有使用我们在无模型部分讨论过的所有强化学习概念，但我们当然可以将它们组合在一起，并将学习模型与强化学习算法（例如策略梯度或基于价值的方法）结合使用，以获得更有效的基于模型的强化学习算法。

此外，还有许多其他概念，它们可以纵向应用于各种不同的强化学习方法，这些方法与特定的算法选择正交，例如探索策略的选择，使用无监督的目标，例如技能发现等。此外，还有一些基于学习的控制框架之外但非常有用的工具，例如概率推理和变分推理工具，它们为我们提供了强化学习的“控制即推理”视角，并且与模仿学习相结合，使我们能够推导出诸如逆强化学习方法之类的东西。这并没有完全涵盖我们讨论的每一个方面，我们还讨论了序列模型、POMDPs 等等，但希望这能让你对这门课程的具体部分有一个粗略的了解。

但今天我想谈谈深度强化学习方法面临的一些挑战，基本上是一些我们还没有解决的开放性问题，以及一些关于如何使用深度强化学习的观点。所以让我们从挑战开始吧。现在你们中的一些人可能已经熟悉了深度学习的一些挑战，比如做过这门课程的作业，在作业中经历过哪些容易，哪些难。但让我们稍微回顾一下，深度学习中的一些挑战实际上是核心算法的挑战，例如稳定性——你的算法是否真的收敛，你是否必须非常非常仔细地调整你的超参数，或者相同的超参数设置是否适用于各种不同类型的问题；效率——收敛需要多长时间，也就是说你需要多少个样本，多少次试验，以及潜在的计算量；算法收逼敛后的泛化能力——它是否真的可以推广到新的问题设置？这在强化学习领域意味着什么？

强化学习方法也面临一些挑战，这些挑战实际上与强化学习的假设有关。当我们尝试将强化学习算法应用于现实世界设置时，这些挑战变得更加明显。我们发现，强化学习算法做出的某些假设对于某些现实世界问题来说有点难以满足。那么，强化学习实际上是正确的问题表述吗？也许你想解决一个基于学习的控制问题，但强化学习假设的某些内容并不完全符合你的要求。例如，你可能无法访问真实奖励函数。你的现实世界问题的监督来源是什么？本质上，需要有人来监督算法。某些形式的监督与告诉算法你想做什么有关，比如奖励。其中一些用于使学习过程更容易，例如，访问演示。你提供的一些东西是两者的结合，例如，提供更合理的、不稀疏的奖励，这可能既可以指定你想要什么，也可以指定你希望该方法如何执行。所以这些假设通常会带来重大挑战。

所以我们先从核心算法的挑战开始，其中一个大问题是稳定性和超参数调优。强化学习算法在某种意义上解决了比监督学习方法更难的问题，因为它们必须获取自己的数据，而不必假设数据是独立同分布的，它们必须优化目标，而不是被给出真实的最优动作。所有这些额外的挑战意味着这些方法对参数的特定设置更加敏感，比如探索率、学习率等等。

现在，设计稳定的强化学习算法——稳定的意思是超参数的微小变化不会导致性能大幅下降——往往非常困难，这在不同类别的方法中以不同的方式表现出来。例如对于 Q 学习或基于价值的方法，部分问题在于，具有深度网络的拟合值方法，函数逼近通常不是一个收缩映射（contraction），因此在大多数一般情况下无法保证收敛。所以我们学到了很多技巧，可以使它们在实践中收敛，但是核心理论问题仍然存在，而且这个理论问题体现在很多方面。首先，这意味着你需要仔细选择很多参数以确保稳定性，比如目标网络的延迟、重放缓冲区的大小、你是否要进行梯度裁剪，以及你该如何选择学习方法等等。这些选择之所以如此敏感，部分原因在于核心算法框架通常可能不收auen敛，我们会将这些作为修复措施添加到模型中，使其收敛。当然，现在有很多研究试图让这些算法更稳定、更易于使用。

我想说的是，很多深度学习的改进确实会有所帮助。例如，如果做得正确，使用大型网络会有所帮助；选择合适的规范化方法会有所帮助；使用数据增强技术会有所帮助。对于那些对数据增强感兴趣的人来说，在一篇名为 DrQ 的论文中，有一些非常出色的工作解释了数据增强如何能够极大地促进 Q 学习的稳定性，但这里可能还存在一些更根本的未解决的问题。例如，为什么监督式深度学习效果如此好，这对我们来说仍然是个谜。传统的机器学习理论认为，监督式深度学习会导致非常严重的过拟合，因为你使用的模型的参数比数据点多得多。如果经典深度学习不会发生这种灾难性的过拟合，那么使用带有稳定梯度下降的大型神经网络必然会产生某种正则化效应，从而减轻这个问题的严重性。所以，从某种意义上说，深度学习确实存在某种“魔力”，而且有很多活跃的研究试图理解这种魔力。基于价值的方法不是梯度下降，它们的工作原理略有不同。实际上，这是一个非常悬而未决的问题，即在监督式深度学习工作中运用的那种“魔力”是否仍然适用于基于价值的方法，或许使用带有稳定梯度下降的大型模型的正则化效应并不在基于价值的方法中以同样的方式工作。所以这在很大程度上处于当前研究的前沿，也许是其中一些挑战的深层体现。所以我在这里没有答案，这是一个活跃的研究领域，但也是一个需要牢记的挑战。

好吧，那么策略梯度方法呢？似然比强化训练，TRPO、PPO，所有这些东西。可以说这些方法在某种程度上更容易理解，因为我们确实有收敛的策略梯度算法。从某种意义上说，策略梯度的故事是，它用基于价值的方法和基于模型的强化学习中的许多缺点来换取更高的方差。所以共同的主题是，所有其他强化学习方法都受到函数近似的偏差的影响，策略梯度方法通常没有偏差，但它们确实有方差。当然，一旦你开始使用价值函数作为优势估计的 Critic，你就会引入同样的偏差，但在它们最纯粹的形式下，它们有很高的方差，但没有偏差。这意味着它们更容易理解，但方差并不轻松，这仍然是一个重大挑战。方差意味着你可能需要大量的样本。虽然乍一看这似乎是一个深奥的问题，比如如果你需要大量的样本，只需要一个更快的模拟器，那么在实践中，方差的增加可能会非常大，你可能不只是需要 10 倍的样本，也许你需要指数级的样本。在最坏的情况下，增长实际上是指数级的。那些最坏的情况有点病态，它们是可以避免的，但在一般情况下，这似乎是一个挑战，特别是它可能是一个不可预测的挑战，因为我们可能很难预测一个新问题的方差是否会使策略梯度难以使用。所以我们最终要小心应对挑战的参数包括批量大小、学习率和策略梯度基线的设计，这是一个非常关键的选择。

基于模型的强化学习算法呢？表面上看，基于模型的强化学习似乎是一个特别方便和稳定的选择，因为模型学习过程最终可以归结为监督学习。对于给定的一批数据训练来说确实如此，该模型是一个常规的监督学习问题。然而，基于模型的强化学习方法仍然是迭代过程，这意味着模型在训练过程中会发生变化，而基于模型的强化学习方法仍在收集自己的数据。这引发了许多主要问题，模型类别以及模型与数据拟合的方法最终变得非常敏感。问题在于，更准确的模型并不一定能直接转化为更好的策略。如果模型是完美的，那当然会给你最好的策略，但如果模型只是变得更准确，那么模型可能会以一种实际上并没有改善策略的方式变得更准确，代价是其他地方的准确度略有降低，而这对策略来说是灾难性的。这应该很简单，如果你驾驶一架飞机，而你有一个关于飞机在什么高度飞行的模型，那么在 30,000 英尺的高度模型出错，显然不像飞机着陆时模型出错那么灾难性，因为飞机与地面之间的每一英寸距离都至关重要。

所以优化模型以得到好策略通常也并非易事，因为存在通过时间反向传播的问题。所以我们最终会使用各种其他方法，包括在模型中运行相同的无模型算法，这当然会引发与无模型强化学习相关的所有挑战。还有一个更微妙的问题，那就是即使模型在大多数情况下表现非常好，策略也可能“利用”模型。你的策略可能会发现一种方法，利用模型会犯的一个错误，导致它错误地预测会发生一些好事。从某种意义上说，基于模型的强化学习是一种对抗性很强的过程，这带来了额外的重大挑战。

所以所有这些方法都面临挑战，这些挑战从根本上来说实际上源于同一个核心问题。这些问题与你必须在没有地面实况监督的情况下发现最佳行为有关，通常是通过收集你自己的数据。但这些问题的表现方式，就效率而言，每类方法都略有不同。我们可以创建一个不同方法的层级结构，尝试大致评估它们的效率。这张幻灯片其实已经很老了，大概是五年前制作的，所以有些内容有点过时，但我认为总体趋势仍然适用。所以我们将从效率最低的方法开始，然后逐步介绍效率最高的方法。最后我会解释为什么我们在实践中可能更喜欢效率较低的方法。

效率最低的方法需要最多的样本，也就是无梯度方法，我们在本课程中没有讲过，但这些方法类似于 CEM 或自然进化策略，它们实际上根本不使用神经网络中的梯度。下一步是开发更高效的方法，例如完全在线的基于策略的方法，例如 A2C 和 A3C。这些方法基于策略运行，并使用集中的策略梯度更新。完全基于在线更新的策略梯度方法，例如 TRPO，它们是批量策略梯度方法，效率会更高一些。完全在线的方法基本上不存储任何试验，它们只是在收集数据时进行更新。策略梯度方法会收集一批数据并进行批量更新。然后通过使用重放缓冲区和离线策略学习的方法，我们在效率上有了很大的提高。这些是 Q 学习方法，使用 Q 函数或 Critic 方法等等，这些都是具有重放缓冲区的方法。然后我们有基于模型的方法，然后我们有基于浅层模型的方法，这些方法通常是最高效的，但也往往是最有限的。

有趣的是，每次效率的提升大约是一个数量级。这里有一个关于使用进化策略的 RL 的无梯度方法的经典论文的例子，该论文中报告的结果比使用 A3C 等算法的完全在线更新效率低 10 倍。这是 2017 年论文的一个例子，其中 A3C 展示了半猎豹（HalfCheetah）任务，大约需要百万次步骤才能将任务学习到不错的性能，这相当于大约 15 天的实时性能。如果我们使用像 TRPO 或 PPO 这样的方法，那么我们能在大约 1000 万次转换内完成，相当于大约 1.5 天的实时时间。如果我们使用带有重放缓冲区的离线策略算法，那么我们可以在大约 100 万个时间步长内学习此类任务，这大约是实时的 3 个小时。近年来，这些方法的效率大大提高，速度实际上提高了 10 到 100 倍，所以这个结果实际上已经过时了。这些方法可能效率更高，但我认为这仍然是一个合理的粗略估计。对于现实任务，几个小时的实时时间大约是从低维状态学习策略所需的时间，这实际上也适用于现实世界的机器人任务。所以我认为，如果你想要一个经验法则，一个合理的经验法则是，离线策略重放缓冲区方法可以以个位数量级的小时数学习任务。当然，这还没有考虑到从像素学习感知等因素。基于模型的深度方法可以再快一个数量级，所以我们说的是不到一小时的时间。像 PILCO 这样的浅层方法可能非常快，它们实际上可以在几秒钟内学习，但它们需要使用不可扩展的模型，例如高斯过程，而这些模型可能根本不适用于高维系统。

现在这是一个非常粗略的指南，当然，这可能会引发一个显而易见的问题是，如果这是样本复杂度的层次结构，为什么我们有时候会更喜欢效率较低的方法呢？原因是，实际上像策略梯度方法这样的东西通常更容易并行化，这意味着如果我们可以并行运行多个模拟，它们实际上可以在挂钟时间方面更快。而且样本成本不是你支付的唯一成本。因此，如果你可以访问大量模拟，并且可以与环境进行交互，而成本更多地与训练模型的计算有关，那么你可能更喜欢效率较低但需要较少计算的方法。事实上，基于模型的深度学习方法通常是最耗费计算资源的，因为你可能在每个模拟步骤中对模型进行多次梯度更新。因此，你可能实际上更喜欢样本效率较低但有其他好处的方法，比如更好的并行性或需要更少的梯度更新策略或模型。

好吧，但话虽如此，我们为什么要关心样本复杂度呢？一个显而易见的原因是，如果你的样本复杂度很差，你就得等很长时间才能完成作业。但另一方面，如果你真的想在现实世界中使用深度强化学习，低样本复杂度意味着现实世界的学习会变得非常困难甚至不切实际，这也排除了使用非常昂贵的高保真模拟器的可能性。也许你会想用某种有限元分析来模拟一个非常复杂的系统，这个系统甚至可能比实时系统还要慢，所以如果你的算法需要数亿次试验，这可能根本不可行，而且它通常会限制对现实世界问题的适用性。所以开发更高效的 RL 方法是一个主要的未解决的问题。我确实认为目前的深度 RL 方法在效率上已经有了巨大的提高，以至于现实世界的训练通常非常可行，但在许多领域，特别是当你增加问题的广度时，这意味着你希望系统能够通过在更广泛的场景中进行训练来实现更广泛的泛化，这成为一个更大的问题。

所以说到广度，泛化的扩大是深度学习面临的主要挑战。当涉及到监督学习时，比如在 ImageNet 上进行训练，或在常见的网络爬取或大型 NLP 数据集上进行训练，监督深度学习的最新成果是大规模的，强调多样性，并且评估泛化能力。所以没有人关心你的语言模型在训练特定文本时能记住得多好，每个人都关心你的语言模型的泛化能力有多好，能否泛化到一些新的提示。而在 RL 中，我们似乎经常在强调精通的小规模任务上评估方法，并根据渐进性能进行评估，这意味着我们真正衡量的是我们在特定环境中对给定目标函数的优化程度。这通常是一个非常合理的衡量标准，所以如果你想提高方法的优化性能，这就是你应该衡量的。但除了评估优化性能之外，在现实世界中，我们还关心泛化性能、多样性和广度，这开始涉及许多超出基本 RL 方法核心问题的主题，更多地与将 RL 方法更大规模地应用于多任务问题和设置的能力有关，这些问题和设置需要使用大量数据而不仅仅是大量模拟。

所以泛化将从何而来，这其中存在许多问题。首先我们可以立即说，如果我们只是深度扩展，如果我们只是运行大量不同设置的大规模模拟，并尝试获得更通用的、可泛化和高性能的策略，这基本上是通往游戏系统的道路，例如 AlphaGo 就是这样，但这相当具有挑战性。监督式机器学习需要我们与世界互动，收集数据集。监督式学习系统通常只进行一次，然后我们会该数据集上运行学习算法，进行多次迭代，最终得到一些解决方案。如果对解决方案不满意，我们不会重新收集数据，只需在修改方法后重新进行训练即可。强化学习通常通过与世界的持续互动进行学习，这意味着如果我们想修改方法，通常会重新运行交互式学习过程。但实际情况是，强化学习有一个外循环，这个外循环就是，如果你对方法的效果不满意，你会修改方法，然后重新进行训练。如果你的训练过程涉及训练半猎豹跑得更快，这没问题。但如果你的训练过程涉及在各种不同环境下进行互联网规模的训练，以实现现实世界的泛化，那么这个外循环很快就会变得不切实际。因此，考虑改进强化学习方法非常重要，不仅要解决优化的核心挑战，还要解决更适合大规模机器学习研究的工作流程。

这个问题相当严重，对吧？这里有一个来自 TRPO 的视频，虽然这个视频已经很老了，但我认为它仍然令人印象深刻。它展示了一个类人机器人如何学习奔跑。虽然这需要一段时间，并且在训练结束后会摔倒几次，但它可以在这个无限大的平面上永久地运行，所以这很酷。如果这是一个真正的机器人，它需要大约 6 天的实时时间，当然，在模拟中速度要快得多。从那时起，这些算法已经变得快得多，所以现在可能不需要 6 天，甚至可能只有 6 个小时，但这仍然是一个相当长的时间。但问题不仅仅是这个问题，如果我们可以让机器人运行几天，然后得到一个可以在任何地方运行的机器人，我们会对此非常满意，但这并不是我们实际上得到的。我们得到的是一个可以在无限大的平面上运行的机器人。现实世界呈现出各种不同的场景，现实世界是多样化的，如果你想要一个在现实世界中完成这类任务的实用系统，它必须处理各种地形、各种情况，甚至可能处理各种服务于运动的行为，不仅仅是跑步，还包括攀爬等等。

人们已经采取的一种成功方法是简单地模拟更大范围的情况，但这很快就会带来重大挑战。这些挑战包括弄清楚所有这些场景是什么，所以你可能需要现实世界的数据来确定你必须涵盖的场景范围，以及实际深入研究能够处理如此广泛场景的算法。所以在利用数据方面，离线或批量强化学习方法可能更有效。也许我们可以从过去的交互中收集一个大数据集，以确定一个有效的机器人需要在沙地、城市和各种其他情况下运行，也许我们可以将这个数据集与离线或批量程序一起使用。如果我们对解决方案不满意，我们可以出去并获取更多数据，只需将其添加到数据集中，而不是重复该过程。然后，如果我们必须调整方法，也许我们可以在不必丢弃所有数据的情况下做到这一点。也许我们也可以通过构建模拟并将这些模拟应用于现实世界来解决这个问题。如果这是您想要采取的方法，并且也许值得进一步研究。

多任务设置也带来了挑战，这些挑战通常不是强化学习研究的核心，但我认为它们非常重要。因此，泛化来自于在许多不同设置中进行的训练。我们讨论了设置多任务学习问题的各种方法，例如，您可以说您有多个 MDP，并且将它们建模为一个多任务 MDP，在第一个时间步选择不同的 MDP。也许这不需要任何新的假设，但是可能需要进行额外的处理以开发在这些场景中有效的算法。因此，虽然标准 RL 方法可以处理多任务学习，但它确实加剧了 RL 中已经存在的某些挑战，例如方差。许多不同的 MDP 会导致方差更高，因为现在初始状态的变化更大。样本复杂度也是一个挑战，如果 MDP 越多，则需要更多样本进行训练，因此现有的样本复杂度挑战会加剧。我们可能只需解决这四个挑战就能在这个问题上取得进展，或者我们可以设计出更好的方法，特别是针对多任务学习，这一点需要牢记。

现在，我们来讨论一下这些假设。除了核心方法的功能之外，强化学习的假设是，我们可以访问奖励函数并与环境交互。这些假设在现实世界中可能会出现问题。强化学习的监督机制从何而来？如果你想从许多不同的任务中学习，你需要将这些任务放到某个地方。在某些情况下，人类指定这些任务可能是非常自然的。因此，如果你想让机器人前往不同的地点，对人类来说可能并不难，只需写下这些 GPS 坐标。我希望你亲自去实践一下。但在其他情况下，仅仅指定你希望强化学习算法学习什么就非常困难。所以，如果你玩游戏，很容易获得奖励，因为游戏有分数，赢得游戏就是奖励。但假设你想倒一杯水，这是任何孩子都能做到的。但如果你想要一个机器人学会倒一杯水，仅仅判断杯子里是否装满了水本身就需要一个复杂的感知系统。这个问题最近已经成为人们关注的焦点，因为有了互联网聊天机器人（比如 ChatGPT），弄清楚你是否以让用户满意的方式与用户互动是一个重大挑战。传统的指定奖励的方式在这些情况下往往会失败。

所以，还有很多其他的事情可以做。比如，我们可以从演示中学习目标或奖励，这是逆强化学习。我们可以通过自动化技能发现自动生成目标，从而生成各种不同的任务，这样我们就可以推广到新的任务。我们也可以探索其他监督来源。所以除了演示之外——这当然是一种被广泛使用的东西——我们可以考虑利用语言的方法来弄清楚机器人应该做什么，或许还有结合语言和感知的模型的辅助监督，这些模型可以通过互联网规模训练的泛化提供奖励信号。我们还可以想象从人类偏好中学习的方法，对不同行为进行成对比较，这在强化学习基准任务中是先驱，但最近作为训练语言模型以满足用户偏好的首选方法而受到广泛关注。所以这些都是改变强化学习算法核心假设的替代监督来源。我认为考虑你的特定领域需要什么样的监督类型很重要。

我认为在监督方面还有一个相当基本的问题，那就是我们是否应该通过告诉强化学习代理我们想要他们做什么或我们希望他们如何做来监督他们。所以演示提供了“什么”和“如何做”，奖励函数原则上只提供了“什么”，但如果奖励函数形状更好，那么它们还提供了一些“如何做”。一方面，RL 方法的优势在于它们能够发现新的解决方案，所以我们不想对它们进行过于严格的监督。但与此同时，如果监督水平过低，比如你对语言模型说“为我的公司带来巨额利润”，那么它可能是一个非常困难的学习问题。所以我们必须找到一个合适的平衡点。

我们可能需要重新思考问题的表述方式，比如我们如何定义控制问题？数据是什么？在某些情况下，用数据来定义控制问题比用模拟器或交互式代理来定义更容易。因此，离线 RL 支持这种设置，而在线 RL 方法不支持这种设置。RL 代理试图实现的目标是什么？是通过奖励、演示或偏好来指定的目标？监督是什么？与目标相同，有时我们希望为代理提供一些提示，帮助它学习任务，而不会对它找到的解决方案产生偏差。已经有研究表明，使用演示作为指导，而不是作为目标规范，但这通常是一个开放的研究领域，这里没有唯一的答案。这就是为什么这是开放挑战讲座的一部分，但我鼓励大家思考适合你的问题设置的假设。有时正确的做法是将你的问题纳入标准 RL 假设中，但有时正确的做法是发明一个新问题，不要假设基本的 RL 问题是一成不变的，并思考如何调整它以适应你的设置。

### CS 285 讲座 23，第 2 部分：挑战与未解决的问题

在今天讲座的下一部分，我想更进一步，从不同的角度讨论一下关于深度学习的哲学。我认为在本课程中，有些内容比较隐晦，但我认为我应该明确指出的是，强化学习实际上可以被认为是一些非常不同的东西，并且根据你对强化学习的理解，有些方法比其他方法更适合你。我将要讨论的三个角度是：强化学习的核心实际上是一种工程工具；强化学习是人工智能代理在现实世界中发现行为的一种方式；第三个角度是我最喜欢的，可能有点奇怪，那就是强化学习确实是最通用、最基本的学习框架，它涵盖了所有其他框架。当然，这是我最喜欢的，因为我是一名强化学习研究员，所以我将采用最以强化学习为中心的角度，但我认为其他两个角度也非常重要，因为它们实际上可能更相关。也许对于你来说，如果你把强化学习更多地看作是一种实现目标的工具，那么让我们从第一个角度开始吧：强化学习作为工程工具。

在这节课的开始，我谈了很多关于我们如何获得通用学习方法的崇高哲学理想，这些方法可以获得我们与人类和动物联系在一起的那种通用智能。但是让我们暂时忘掉这些，思考一下工程问题。比如，我们认为强化学习是一种智能体与世界互动的模型，就像动物通过奖惩机制学习一样，也许我们的智能体可以通过奖惩机制学习，拥有非常自然的现实世界学习过程，其中融合了心理学元素、神经科学元素和计算机科学元素，这很酷。但是我们来谈谈一些非常实用的工程问题，比如，假设你想驾驶一枚火箭，你希望它沿着最佳轨道飞行。传统上，这不是我们与强化学习联系在一起的问题。你知道，我们会用复杂的微积分，就像你会写下一堆描述火箭物理原理的复杂方程式，然后解这些方程式，就能得到火箭在每个时间点的位置。你会把它线性化，你会做各种你在线性系统和控制理论中学到的事情，然后得到一个反馈控制器，你可以用它来控制火箭的飞行。这就是我们登上月球的方式，这很棒，而且它与强化学习无关。

但我认为，强化学习的一个观点是，如果我们将传统控制问题简单地视为“逆向物理”问题，那么它为我们提供了另一种工具。RL 只是为我们提供了另一种工具来实现这一点。现在，我所说的“逆向物理”是什么意思呢？我描述的火箭飞行过程，它的工作原理是，有人理解了系统如何随时间演变的物理过程，写下了描述该物理过程的方程式，然后使用这些方程式推导出控制律，也就是描述火箭如何驱动和节流其发动机以达到所需配置的方程式。所以你可以这样写下物理学，然后你基本上反转了物理过程，以确定导致所需边界条件或结果的控制。这与我们模拟某些东西时所做的非常相似。所以通常当我们说运动方程时，我们想象的是相对更独立的东西，我们可以把方程写在一张纸上，甚至可以求解它们。但模拟器本质上只是一个非常花哨的版本，我们写下一堆方程，我们可以对这些方程进行数值积分，以弄清楚一些复杂系统将如何随时间演变，比如飞机机翼上的应力和应变将如何影响其在空中的飞行。

所以如果我们在模拟器中运行强化学习，本质上我们在做的是使用机器学习来反转我们的物理模型。所以不是与世界互动，而是与我们自己对物理的理解互动，这提供了一个非常强大的工程工具，与传统的工程方法并没有太大区别。所以在我们通过写下运动方程来表征我们的系统之前，我们会处理一种模拟器，但之后我们会在纸上推导出如何控制系统的方程式。现在我们会通过编写代码来运行模拟器，然后使用强化学习来计算控制律。强化学习只是取代了我们之前更手动的过程，在之前，我们会进行大量的微积分来计算火箭的控制器。所以我们在这里的主要作用本质上是一个更强大的“逆向引擎”，一种将模拟器转化为控制律的方法，而之前我们都是手动完成的。

当然，这种观点的主要缺点是仍然需要有人来描述一个系统。所以从这个角度来看，这甚至算不上一个学习过程，它实际上更像是一个优化工具，但它是一个非常非常强大的优化工具。这个非常强大的优化工具已经为机器人运动等领域带来了巨大的进步。例如，我们有四足机器人，它们可以通过利用广泛的模拟在崎岖地形上非常有效地行走。我认为总的来说，随着这个工具的发展，我们会看到更多的进步。强化学习被应用于各种控制问题的模拟器的反演，并且随着我们的方法变得越来越可靠和强大，从长
远来看，如果我们想要控制任何系统，比如飞机、车辆或步行机器人，如果它是一个我们足够了解并能够描述和模拟的系统，那么模拟中的强化学习将成为为其构建控制器的标准工具。现在，我应该承认，这种观点在某些方面与强化学习的心理学根源非常不同。心理学根源更多地将其作为现实世界中学习的模型，而这更像是一个计算过程。这导致的方向是开发更快的模拟器，开发可以利用广泛模拟的算法。

我们可以采取一个非常不同的视角，将强化学习作为现实世界中学习的工具，这可以得出其他一些结论。所以，对我来说，在现实世界中研究强化学习的一个非常有激励作用的观点是所谓的莫拉维克悖论（Moravec's paradox）。稍微介绍一下，在 1986 年，人工智能的一个重要里程碑，这是计算机第一次在国际象棋比赛中击败世界冠军，这台名为“深蓝”的计算机在国际象棋比赛中击败了卡斯帕罗夫。20 年后，人工智能又迎来了另一个重要的里程碑，计算机首次在围棋比赛中击败了人类冠军。现在，围棋对人工智能来说比国际象棋更复杂，因为那些可以掌握国际象棋的树搜索式方法真的无法处理围棋，但强化学习方法可以，这很棒。

但是我们可以在这两个相隔 20 年的里程碑的两张图片中注意到，在这两种情况下，都没有机器人在玩游戏。所以在这两种情况下，都有另一个人坐在棋盘的另一边，与人类冠军相反，这个人似乎在移动棋子。那么计算机在哪里呢？计算机告诉他们如何移动棋子。所以人类本质上是一个“人类机器人”，就像通常认为人类通过远程操作控制机器人一样，这是一台计算机在控制一个人。这意味着人类只是被用来执行移动身体的能力。为什么我们不能让计算机真正移动棋子，即使计算机可以在游戏中击败人类冠军？这是一个很好的缩影，这就是所谓的莫拉维克悖论。

莫拉维克悖论的原始表述是：“我们都是感知和运动领域的神童和奥林匹克运动员，我们如此优秀，以至于我们让困难的事情看起来很容易。抽象思维虽然是一个新的技巧，可能不到 10 万年，我们还没有掌握它。它并不是那么本质上困难，它只是看起来那么难。”所以让我们来解释一下这个说法。汉斯·莫拉维克在这里说的是，你需要成为国际象棋大师的那种抽象思维实际上可能并不那么难，我们只是不太擅长它。另一方面，我们非常擅长移动我们的身体和感知世界，因为我们必须这样做。如果我们不擅长移动我们的身体和感知世界，我们就会死亡，进化就会摆脱我们，用其他更擅长移动身体和感知世界的生物来代替我们。所以移动身体和感知世界可能比击败国际象棋世界冠军要难得多，只是生物学让我们都在这方面非常擅长，所以感觉毫不费力。这一点最近被史蒂芬·平克更简洁地重申了：“35 年人工智能研究的主要教训是，难题很容易，容易的问题很难。我们认为理所当然的四岁孩子识别面部、提起铅笔、走过房间、回答问题的能力实际上解决了一些有史以来最难的工程问题。”

所以这意味着，获得这些物理能力实际上可能是问题中最难的部分。莫拉维克悖论看起来像是关于人工智能的陈述，从字面上看，它是说这对人工智能来说很难，这对计算机来说也很难。但它实际上是关于物理宇宙的陈述。所以它说在我们的物理宇宙中，运动控制和感知是困难的。你可以想象在其他宇宙中，运动控制或感知不那么困难。比如说，如果你在下棋或驾驶火箭，那么实际上就不存在感知挑战，在国际象棋中也不存在运动控制挑战。一个动作就是命令将一个棋子移动到另一个棋子的位置，将棋子从棋盘上扔下来，这不符合国际象棋的规则，也不属于国际象棋世界。所以，这是一个简单的宇宙，至少就莫拉维克悖论而言。这并不是说下棋很容易，只是那个宇宙中的运动控制和感知很容易。

困难的宇宙是我们居住的宇宙，它很混乱，有物理，有感知，有现实世界的多样性和多变性。也许我们必须以一种类似于人类学习的方式来学习这些东西，也许我们必须通过经验来学习这些东西，因为这些东西对我们来说很难写下来，而规则可以通过简单的规则和方程式来建立。这实际上是现实世界学习的动力。举一个更现实的例子，让我们来讨论一个困难的工程问题，比如让一艘油轮从世界的一端航行到另一端。这个问题的抽象思维部分就像规划一条穿越海洋的路线，这实际上是今天的计算机很容易做到的事情，你甚至不需要机器学习，比如使用 GPS 和基本搜索算法规划路线，这很简单。但如果油轮上的某个部件坏了，需要有人去机舱修理，这是我们现有的人工智能系统无法做到的。所以如果你在油轮上有一个船员，他们对于解决意外问题至关重要，但对于实际驾驶油轮来说并不重要。真正的变化性，你可能会遇到非常意外的情况，这使得现实世界如此艰难。

那么这一切与我们有什么关系呢？也许 RL 可以为我们提供答案，我们如何设计一个可以处理意外情况的系统。我认为这个问题实际上是莫拉维克悖论的核心，因为使现实世界如此艰难的原因是可变性和多样性，以及训练集之外的意外情况随时可能发生。想象一下鲁滨逊漂流记的故事，故事中有一个被困在荒岛上的人。这个故事之所以如此引人入胜，是因为他们必须想出各种巧妙的点子才能在岛上生存，建造庇护所，获取食物等等。想象一下，一个人工智能代理处于这种情况，它必须即兴发挥，利用手头的资源，在最少的外部监督下，找到问题的解决方案，以及可能需要适应的意外情况。它必须自主发现解决方案，并且还必须存活足够长的时间来找到解决方案。所以它不像一个完整的游戏，你需要多次尝试。

人类在这方面非常擅长，当然，因为我们必须这样做，这是进化选择的结果。但我们目前的人工智能系统在这方面非常糟糕。即使是最令人印象深刻的人工智能系统，它们可以从网络上获取数十亿份文档并学会回答任何问题，它们也无法在荒岛上生存，因为在那里你必须真正地处理意外情况，仅仅依靠你的训练数据不够好。我的主张是，原则上 RL 实际上可以做到这一点，其他任何技术或多或少都做不到，尽管主要是因为 RL 是解决这类问题的框架。RL 是即时学习的框架，用于进入那个世界，体验正在发生的事情，从进展顺利或不顺利的事情中获得反馈并进行调整。

话虽如此，我们很少在我们的研究中研究与此类情况相关的挑战。所以简单的宇宙可能看起来像以前的控制问题，其中成功等同于获得高额奖励，这是一个封闭的世界，有已知的规则，例如围棋游戏，有很多模拟，主要的问题是 RL 真的可以很好地优化吗？困难的宇宙是成功基本上等同于生存的宇宙，基本上做得足够好，你要么生存，要么死亡。这是一个开放的世界，一切都必须来自数据，因此模拟器中不一定有先验知识。主要的问题是你真的可以泛化和适应吗？你能否处理环境的多变性和不可预测性？从奖励反馈中学习原则上应该可以让你做到这一点，但这引入了许多超出我们通常研究的强化学习基准范围的挑战。

所以强化学习在这些困难的世界中应该表现得非常好，但在现实世界中出现了许多问题，我想让你们先了解一下。首先，我们如何告诉我们的强化学习智能体我们希望它们做什么？现实世界不一定有分数。如果你的反馈是“你是否幸存下来了”，那么反馈会延迟太久，所以我们需要更多的近期监督。我们如何在连续的环境中完全自主地学习？现实世界不像游戏那样是分回合的（episodic），你可以重置世界并重试。随着周围环境的变化，我们如何保持稳健？如何正确地使用经验和先前的数据进行泛化？如何正确地利用先前的经验来引导探索？

由于这是最后一节课，我将稍微放纵一下自己，向你们介绍一些来自我实验室以及我合作过的同事的研究，这些研究可能涉及到一些这些问题，只是为了让你大致了解一下这些问题到底是什么样子。但我会说我会举很多机器人的例子，这个挑战不仅仅关乎机器人，虽然机器人对我们来说是最自然的思考对象，因为它们和我们一样有身体，所以我们可以很直观地想象什么对机器人来说是难的或容易的。但我认为同样的问题也适用于任何与现实世界互动的系统，无论是管理库存、开处方，还是互联网上的聊天机器人，它们在某种程度上都是通过不同的媒介与现实世界环境互动的系统，而不一定像我们一样拥有身体。我们之所以讨论机器人，是因为它们的身体和我们有点像，这可能让我们更容易对它们产生共鸣。

那么其他沟通目标的方式怎么样呢？我之前提到过，有很多可能性，其中我想重点讨论的是“从偏好中学习”的想法，这是近年来非常重要的一个问题。这是 Paul Christiano 于 2017 年开发的一个框架，其中没有使用真实奖励函数，而是让智能体向人类展示试验，并让他们选择自己更喜欢的那个。然后人类可以选择不同的试验，并以这种方式引导智能体执行某些任务，例如翻转。在这种情况下，很难写下奖励函数的闭式表达。所以这是一个算法的例子，它可能使用其他类型的监督，这些监督在现实世界中比奖励分数更容易获得。

我们如何完全自主地学习？这里有一个例子，关于这个，我以前的学生 Anusha Nagabandi 一直在做一些关于控制多指手的有趣工作，她可以让多指手在这种情况下使用基于模型的强化学习来做一些很酷的事情，比如正确操纵物体等等。但是当需要在现实世界中做到这一点时，为了让手真正练习这项技能，她需要构建一个完整的独立机器人系统来重置环境，以便手可以再次尝试放下物体。我们可以想象也许有一些想法可以让这个过程变得更容易，比如把学习问题变成一个多任务问题。举个例子，假设机器人需要煮好咖啡，它只有一个任务，就是把杯子放进咖啡机。如果它把杯子弄乱了，掉了杯子，那么可能需要一个人来把杯子放回去，这样机器人才能再试一次。但是如果它还有第二个任务，就是拿起杯子，那么如果它在第一个任务中失败了，它就可以做第二个任务。本质上，失败是一个练习新东西的机会。如果它在第一个任务中成功了，它就可以做另一个任务，把杯子放回去。如果它在第一个任务中失败了，把咖啡洒了，也许这是一个清理洒出来的东西的机会。所以，如果我们同时学习多个任务，那么每次失败都可以给我们一个尝试学习新东西的机会。

这是我们可以实际尝试的。这里有一个实验，机械手正在尝试同时学习多个任务。现在这里的任务比我描述的要基础一些，这些任务包括将物体移动到垃圾箱中央、拿起它、在手掌中重新调整它的位置，如果物体掉了，就尝试再次捡起来。所以，这里有四个任务：抓取、手内操作、抬起和翻转。这些任务的设计使得无论机器人遇到什么故障，都可以从失败的状态中练习其他任务。结果是机器人实际上可以完全自动地训练，在这种情况下，大约需要 60 个小时来练习这个任务。

如果我们回想一下那个荒岛的例子，另一个挑战是，我们可能真的希望我们的智能体能够高效地学习，以便它们能够生存下来。这意味着，如果它们经历了太多的失败，也许现实世界的结构会阻止它们继续练习。所以，如果它们失败得太惨，如果它们真的伤害了自己，那可能就不好了。所以，重要的是要真正利用智能体所掌握的先验知识。它可能不知道自己在将来会遇到什么，但它可以在一个特定的环境中建立先验知识，这应该有助于它探索新的环境。就像鲁滨逊·克鲁索知道如何生火、如何搭建庇护所等等一样，尽管他也必须在探索过程中发现很多东西。

所以这是一个特定的环境，我们可以举个例子。假设我们想要一个机器人，让它学会捡起物体并把它们放在这个小方块上。我们的想法是，我们需要让它学习新的任务，比如与它以前没见过的物体互动。如果对于每个任务，我们都从头开始，用随机动作进行探索，那么一开始，机器人会做一些类似这样的事情。就像你看这个视频一样，即使你不知道机器人应该做什么，你也会立刻知道它在这里做的是不对的，因为它不是任何任务的解决方案。所以这里有一个想法，如果机器人有很多它以前解决过的其他任务的经验，它会利用这些经验来构建一种行为先验，本质上是一种不执行任何特定任务的策略。它会执行一些以前有用的随机行为，这些行为可能可以用来引导探索，这样当它被放置在一个新环境中时，它基本上会尝试一些可能有用的随机行为。所以现在，如果你看左下角的这个视频，即使机器人应该做一件非常特别的事情，比如把那个黄色物体放在立方体上，它在很多次试验中都没有完成正确的任务，但它仍然在做一些可能有用的事情，这似乎是一个更好的探索策略。所以，如果你想在现实世界中学习成功，从先前的经验中构建探索策略的概念可能非常重要。这个方法确实有效，它做了很多有用的事情，但我想说的重点是，仅仅因为我们想进行现实世界的学习，并不意味着先前的知识不存在，它只是意味着先前的知识需要是合适的类型。所以它实际上是先前的，而不是对你实际要做的事情的模拟，它需要从先前的经验中获得。

好吧，所有这些看起来都很难，看起来我们只是在描述了其他挑战之后，又面临了更多挑战。那么为什么这很有趣呢？我认为看到智能代理能够提出哪些解决方案真的非常令人兴奋。如果我们只……最令人兴奋的解决方案是我们没想到的，这就要求它们栖息在一个足够丰富、能够容纳新颖解决方案的世界中。这意味着世界必须足够复杂。为了看到有趣的新兴行为，我们必须在真正需要有趣新兴行为的环境中训练我们的代理。所以如果我们仅限于使用模拟，就像我之前描述的工程方法一样，那么代理就没有空间去发现太不同的事物，它只能发现我们设计的模拟世界范围内的事物。所以如果我们能够构建现实世界学习系统，它们可能会发现真正有趣的、新颖的解决方案，并且它们可能具有这种适应性和灵活性，可以解决莫拉维克悖论中的核心挑战，这是当今阻碍人工智能系统在现实世界中部署的最大因素之一。

好吧，我想再告诉你们一个观点，也许有点奇怪，但我觉
得思考一下很有趣，也许会给你一些想法，那就是强化学习的真正力量可能真的在于它是一个更通用的学习框架。也许它不仅仅是我们学习与现实世界互动或学习解决控制问题的一种方式，而是一种最终可能会涵盖所有机器学习的方式。

那么为什么深度学习在一些非常基础的层面上有效呢？监督深度学习在最基本的层面上很好。深度学习之所以有效，是因为你可以用大量的计算机和你花了很多钱的数据训练一个大型模型，把它和一个大数据集（通常是标签数据）结合起来，得到一些可以解决有趣问题的东西，比如成为一个好的聊天机器人，识别语音，对图像进行分类等等。但如果这就是今天机器学习发挥作用的公式，那么我们所能做的每一件事，只要允许我们使用更多的数据，都将是一件好事。而且我们看到的越来越成功的秘诀正是因为这个原因。我们有一个小的、大量的数据实际上告诉模型该做什么，然后是大量未标记的、低质量的垃圾数据。例如，所有这些，这就是语言模型的工作原理。语言模型基于来自网络的大量数据进行训练，然后我们可以通过对少量数据进行微调，甚至通过少量提示来定义一项任务。

但是，当我们进行这种无监督或弱监督训练时，模型实际获得的知识实际上来自哪里？经典的无监督学习基本上是在进行密度估计，它是对数据分布进行建模，例如大型语言模型在进行下一个标记预测时所做的那样，您正在对生成数据的过程进行建模。如果您正在学习例如来自图像的自监督表示，则分布是人们拍摄的照片的分布。如果您正在从互联网上的自然语言文本中学习，那么您就是在从人们按下的键盘按钮中学习。顺便说一句，这也许就是为什么提示大型语言模型是一门艺术，因为如果您正在学习，如果您正在学习数据的分布，这些数据每周都会被标记为垃圾数据，这些数据是你从网络上抓取的，你学习到的是一个非常奇怪的分布。也许很多原因，比如为什么语言模型很难做到我们想要做的事情，是因为你正在尝试对低质量的分布进行建模，也就是网络上的所有内容，你试图强迫它做一些高质量的事情。那么有没有更好的方法呢？

我想论证的是，强化学习实际上为我们提供了一种更好的方法来利用这些低质量数据。所以退一步来说，让我们问一个非常基本的问题，为什么我们需要机器学习？我们可以退一步来说，问一个更基本的问题，为什么我们需要大脑？研究运动控制的神经科学家丹尼尔·沃尔伯特（Daniel Wolpert）说过，我们拥有大脑只有一个原因，那就是产生适应性强的复杂运动。运动是我们影响周围世界的唯一方式，我相信理解运动就是理解整个大脑。这可能有点简化，但我认为它非常有效，因为从根本上来说，计算系统的价值实际上取决于它的输出。

我们可以将同样的逻辑应用于机器学习。我们可以假设我们需要机器学习只有一个原因，那就是做出适应性强的复杂决策。这虽然有点简化，但我认为有一个很好的理由来证明这一点。显然，如果你控制一个机器人，机器人的运动是唯一重要的输出。如果你驾驶一辆汽车，等等。但如果你有一些其他的无实体模型，比如图像分类器，它也会做出决策。这个决策实际上不是图像标签，而是与之后发生的事情有关。比如，你是否在监控摄像头里检测到有人？你是否决定报警？你是否对交通状况做出预测？这个预测会影响人们的驾驶方向，进而对现实世界产生影响。如果你是一个语言模型，你说的话会在多次交互中产生长期影响。所以机器学习系统的输出本质上就是决策，即使它们是用监督学习训练的。

所以，如果机器学习系统真的都是关于决策的，那么强化学习或许能让我们更好地利用数据来做出更好的决策。因为有了强化学习，我们可以说，我们从所有可用来源抓取的这种垃圾数据，实际上告诉我们世界上可以做什么，而不是应该做什么。然后，有限的监督可以指定代理应该执行的任务。所以我们从过去交互中获得的大数据集不一定是我们学习要去复制的东西。我们不会像语言模型那样学习 P(x)，而是用它来理解我们可用的可能性，然后在这些可能性中选择最能完成我们想要完成的任务的可能性。

所以，这种观点可能有点抽象。它主张的是使用强化学习循环中的数据，无论是基于模型的强化学习循环，还是离线强化学习循环，或者其他支持决策基础的东西，然后根据数据告诉你的可能性做出最优决策。机器学习的核心在于做决策，这应该是一个比无监督学习更好的框架，在无监督学习中我们尝试对整个数据分布进行建模。问题在于，对于简单的强化学习来说，这是一个成本高昂的过程，因为你必须与世界互动才能收集这些数据。但监督式、自监督学习的核心在于使用廉价数据。因此，离线强化学习或基于模型的强化学习或类似的东西或许是这种哲学更好的基石。

但在高层次上，方法可能是获取大量不同但质量低下的数据，然后对其运行某种类似强化学习的过程，无论是学习模型还是学习价值函数，它都应该是自监督的。在这一点上，它应该是人类定义的技能或目标条件的强化学习，甚至是自监督技能发现，并利用它来运行决策的自监督学习，学习如何做出导致特定结果的决策，然后在适度的监督下，找出如何实现人类真正想要的结果。

在某种程度上，我实际上会假设我们在语言模型（例如 ChatGPT）中看到的很多兴奋实际上是因为这种流程的某些版本开始出现，我们看到一个类似 RL 的过程，用于实际指定人类的偏好，正在提供更有用的代理。但我认为从实际使用 RL 作为整个训练过程的基本构建块，还有更多的空间可以释放，实际上运行无监督的 RL 训练来发现如何实现任何结果，然后将其专门化为我们真正想要的结果。

现在要把它带回到现实，我会告诉你一些实验，也许可以得到一些原始版本。这是我们几年前在谷歌做的一个实验，关于机器人行为的自我监督学习。我们运行了目标条件 RL，所以这里没有奖励函数，任务是完全使用目标图像进行定义，它是使用离线 RL 进行训练的，它作为一种预训练目标。所以我们的想法是，我们使用图像为机器人指定目标有点笨拙，但如果我们这样做，在达到强化学习预训练的自我监督目标之后，我们可以此基础上进行微调，并设置任务奖励，这样效果会更好，速度也会更快。所以这有点像 BERT，不过是针对机器人的。BERT 对文本进行无监督预训练，而这里是对目标进行自我监督的强化学习预训练，然后使用新的奖励信号快速进行微调。

我们也可以应用 RL 到语言模型。据我所知，它还没有被用作预训练程序，但作为微调程序，效果会很好。这是 Joey Hong 最近做的一项工作，他实际上是今年的助教之一。Joey 想要一个互动性更强的代理。也许你想要一个教师代理，你不希望它在你提问时给你一个很长的回复，而是让它真正地向你澄清你是否理解了某些概念，然后相应地调整课程。所以如果用户问“你能教我关于行为克隆的知识吗？”，那么智能体应该说“当然，我很乐意解释一下行为克隆。你能告诉我你以前是否接触过人工智能或机器学习这两个术语吗？”所以，这个问题会帮助智能体调整它的解释。当然，如果你问 GPT-4 这个问题，它不会问你很多澄清问题，只会给你一长串的文字回复，这是因为它就是被训练来这么做的。

为什么 RL 是获得这种更具交互性的智能体的好选择呢？原因在于，基于大量互联网数据训练的语言模型实际上可能是一个非常好的人类行为预测模型。所以，如果我们使用更像基于模型的强化学习的东西，我们可以让语言模型本质上模拟合理的人类反应，然后针对可能导致对我们有用的反应的各种问题进行优化。基本上，将整个过程视为一个 POMDP，并通过简短的问题优化出更好的策略，这些问题可以很快让我们获得必要的信息。所以我们可以采取的方法是实际上可以促使语言模型提供一堆对话，本质上充当人类模拟器，生成数据。这些数据可能不是很好，可能无法真正以最佳方式教人类，但它可以向强化学习算法展示人类可能产生的反应类型。然后可以将庞大的数据集与强化学习一起使用，以找出更优的行为，这实际上可以产生更好的教学代理。

因此，带有语言模型的强化学习非常适合语言模型容易模拟但难以最佳执行的任务。因此，使用强化学习，语言模型可以利用它们对人类行为方式的理解来学习实现期望的结果，而不是仅仅模拟人类。所以再次使用强化学习作为一种“反转”，但不是模拟器的反转，而是语言模型的反转。这些是你可以获得的对话类型。所以这是基线，这是带有 ChatGPT 版本的基线，专门要求它提出大量澄清问题，所以这是给它最好的机会。你可以看到它有这些非常冗长的回答。这是经过强化学习训练的代理，你会发现强化学习代理实际上会问一些非常简短、非常有针对性的问题。“我很乐意解释一下行为。首先，你能告诉我你是否听说过人工智能或机器学习吗？”对方说“是的，我听说过这个词，但我不太清楚它们是什么意思。”代理说“没问题，让我们一步一步来”，然后问“你用过电脑或智能手机吗？”这样做的目的是，一旦我们开始将这些问题视为决策问题，我们就能以更自然的方式更接近我们想要的行为。

好吧，这可能需要一点研究，希望你能在这一点上迁就我，因为这是我这节课的最后一节课。但最后我想说的是，让我们回到更大的图景。我们在第一节课中就已经讨论过更大的图景。所以在第一节课中，我们讨论了学习可能是智能的基础的可能性，如果是这样的话，那么也许深度强化学习应该是其中的核心部分，因为强化学习使我们能够推理决策，而深度部分使我们的算法能够学习复杂的输入输出映射。所以也许深度模型就是让强化学习能够端到端解决复杂问题的原因。

所以如果这是真的，那么缺少一个我认为非常有趣的观点是什么？我不认为我完全同意这一点，但我认为值得一提的是 Yann LeCun 教授经常提出的观点。这个观点后来被称为“Yann LeCun 的蛋糕”，他谈到了如何在非常高的层次上，你可以通过从每个数据点获得多少位监督来表征不同学习问题的有效性。所以这个想法是，如果你正在学习一个图像分类器，假设你有一千个类，一千的对数大约是 10，所以你实际上会从每个数据点获得 10 位监督。如果你在进行无监督学习，比如预测视频中的下一个图像，那么就会有很多像素，所以需要大量的监督对于每个数据点。如果你在进行强化学习，那么你可能正在从相当稀疏的奖励中学习，所以你没有得到太多的监督。因此，这是一个从根本上支持无监督学习方法的论点，即使用大量数据，并以强化学习作为一种机制来调整这种模型以解决特定任务。

但这让我们思考，在如何构建智能系统这个更大的问题中，强化学习的真正作用是什么。所以，Yann LeCun 的蛋糕理论认为，无监督学习或自监督学习应该是其核心，而基于模型的学习或基于模型的强化学习应该非常重要。强化学习是一种专门用于实现目标的系统机制。但也有其他观点，例如，也许很多学习信号应该来自模仿和理解其他智能体。人类是社会动物，我们有文化。你可以说，一个在文化中成长的人和一个周围没有文化的人可能实际上并不那么聪明。所以也许模仿对于通用人工智能系统的总体愿景更为重要。但我认为还有另一个非常合理且站得住脚的观点，那就是强化学习可能就足够了。因为即使在强化学习中，你从一个相当稀疏的奖励信号中学习，这个奖励信号受到非常复杂的系统动力学的调制。所以对价值函数之类的东西的实际监督比仅仅预测奖励是一还是零要复杂得多，因为一旦动力学与奖励信号相互作用，它就会以非常复杂的方式进行调节。你总是可以运行多任务学习和其他东西，即使使用标准的强化学习方法，也能给你更多的监督。

真正的答案可能是上述的结合。也许我们需要自监督的学习方法，也许这些自监督的方法看起来更像强化学习方法，因为它们有助于做出最佳决策以实现期望的结果。也许我们需要与模仿互动的元素。我不知道答案是什么，我有自己的关于什么更有可能发生的信念，但我想让你们思考的是，这些问题实际上很重要，如果我们要探索强化学习作为一种强大的通用工具，那么思考这些可能性会有所帮助。

那么我们应该如何回答这些问题呢？我们需要选择正确的问题来研究。也许在这里，非常重要的一点是，如果你选择一个研究问题，它是否有机会解决一些真正重要的问题。所以要研究那些成功上限非常高的研究问题，要考虑更大的图景哲学，然后尝试将其简化为具体的可操作的事情，可证伪的假设，它可以用以衡量你的成功，但这些假设很可能在通往大问题的道路上。面对不确定性时保持乐观是一种很好的策略，不仅适用于探索-利用问题，也适用于研究。不要害怕改变问题陈述。许多这些挑战将通过迭代现有基准来应对，也许经典强化学习的标准假设不足以回答这些问题，我们需要改变一些东西，这没关系。

最后，应用很重要。有时将方法应用到现实和具有挑战性的现实世界领域可以让我们学到很多关于缺失的重要事物，我们需要认真对待它们。所以不要害怕研究更多应用的东西，因为它实际上可能会揭示哪些问题更重要，哪些解决方案更有希望。RL 长期以来一直忽视这一事实，并致力于过于简单的任务，不要落入同样的陷阱。最后，要从大处着眼，但要从小处着手。思考大局问题，不要害怕雄心勃勃，思考什么才能真正让我们开发出真正强大的人工智能系统，完成没有人认为可能的事情。但同时要从明确的可操作目标开始，这样你就可以衡量迈向成功的进展。
```