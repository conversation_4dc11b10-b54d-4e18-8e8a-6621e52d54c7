﻿point_index,screen_x,screen_y,pupil_left,pupil_right,pupil_left_raw,pupil_right_raw,gaze_x,gaze_y,timestamp,valid,sample_count,valid_left_samples,valid_right_samples,original_point_index,repetition,trial_index,is_practice,round_num
1,960.0,270.0,1061.6,852.1,"[1010.0, 1025.0, 1030.0, 1031.0, 1043.0, 1052.0, 1057.0, 1054.0, 1045.0, 1051.0, 1053.0, 1075.0, 1082.0, 1077.0, 1078.0, 1087.0, 1088.0, 1091.0, 1097.0, 1106.0]","[803.0, 815.0, 822.0, 820.0, 825.0, 837.0, 849.0, 857.0, 849.0, 854.0, 851.0, 866.0, 864.0, 864.0, 869.0, 883.0, 872.0, 875.0, 881.0, 886.0]",966.8525009155273,191.5400005340576,1755590809.2256293,True,20,20,20,1,1,1,False,1
4,960.0,810.0,874.35,740.45,"[823.0, 816.0, 824.0, 835.0, 835.0, 848.0, 866.0, 879.0, 885.0, 891.0, 894.0, 901.0, 904.0, 904.0, 907.0, 904.0, 899.0, 894.0, 891.0, 887.0]","[665.0, 660.0, 670.0, 693.0, 697.0, 716.0, 731.0, 744.0, 756.0, 763.0, 771.0, 780.0, 783.0, 781.0, 771.0, 771.0, 774.0, 756.0, 766.0, 761.0]",991.0925033569335,727.5799987792968,1755590811.885824,True,20,20,20,4,1,2,False,1
5,1440.0,810.0,700.2222222222222,610.3888888888889,"[801.0, 806.0, 805.0, 589.0, 375.0, 514.0, 608.0, 664.0, 704.0, 733.0, 746.0, 760.0, 750.0, 757.0, 749.0, 758.0, 743.0, 742.0]","[662.0, 658.0, 657.0, 534.0, 360.0, 475.0, 535.0, 585.0, 622.0, 641.0, 654.0, 672.0, 659.0, 665.0, 658.0, 656.0, 650.0, 644.0]",1426.5027720133464,962.7527804904514,1755590814.5472589,True,20,18,18,5,1,3,False,1
3,480.0,810.0,852.95,701.45,"[863.0, 848.0, 844.0, 841.0, 838.0, 836.0, 839.0, 840.0, 844.0, 848.0, 852.0, 854.0, 851.0, 861.0, 855.0, 860.0, 861.0, 873.0, 876.0, 875.0]","[712.0, 703.0, 697.0, 696.0, 687.0, 694.0, 689.0, 678.0, 686.0, 689.0, 695.0, 703.0, 699.0, 701.0, 711.0, 706.0, 714.0, 715.0, 726.0, 728.0]",477.8274978637695,1007.7299987792969,1755590817.2063978,True,20,20,20,3,1,4,False,1
2,1440.0,270.0,931.8,778.45,"[895.0, 902.0, 902.0, 904.0, 914.0, 917.0, 919.0, 918.0, 914.0, 930.0, 930.0, 929.0, 944.0, 942.0, 949.0, 953.0, 963.0, 967.0, 971.0, 973.0]","[746.0, 749.0, 747.0, 752.0, 759.0, 764.0, 769.0, 765.0, 773.0, 776.0, 775.0, 779.0, 793.0, 795.0, 790.0, 800.0, 799.0, 805.0, 815.0, 818.0]",1446.4449920654297,154.9149995803833,1755590819.8627355,True,20,20,20,2,1,5,False,1
0,480.0,270.0,1144.1,966.7,"[1161.0, 1163.0, 1166.0, 1169.0, 1162.0, 1158.0, 1156.0, 1170.0, 1150.0, 1163.0, 1142.0, 1139.0, 1133.0, 1130.0, 1123.0, 1133.0, 1122.0, 1115.0, 1116.0, 1111.0]","[967.0, 967.0, 971.0, 973.0, 980.0, 976.0, 978.0, 971.0, 976.0, 970.0, 970.0, 970.0, 977.0, 969.0, 959.0, 959.0, 950.0, 951.0, 955.0, 945.0]",482.3099990844727,219.7824993133545,1755590822.5199068,True,20,20,20,0,1,6,False,1
2,1440.0,270.0,1147.1,956.6,"[1115.0, 1118.0, 1134.0, 1135.0, 1134.0, 1135.0, 1148.0, 1136.0, 1136.0, 1135.0, 1143.0, 1155.0, 1151.0, 1158.0, 1162.0, 1171.0, 1167.0, 1170.0, 1172.0, 1167.0]","[922.0, 923.0, 934.0, 930.0, 939.0, 941.0, 945.0, 950.0, 963.0, 962.0, 964.0, 971.0, 969.0, 974.0, 972.0, 973.0, 973.0, 976.0, 971.0, 980.0]",1441.377508544922,183.04999923706055,1755590825.1824584,True,20,20,20,2,2,7,False,2
3,480.0,810.0,949.85,801.75,"[885.0, 882.0, 893.0, 903.0, 915.0, 915.0, 934.0, 941.0, 952.0, 948.0, 959.0, 967.0, 977.0, 978.0, 988.0, 979.0, 987.0, 994.0, 1002.0, 998.0]","[766.0, 753.0, 760.0, 766.0, 774.0, 771.0, 793.0, 799.0, 799.0, 802.0, 807.0, 820.0, 815.0, 818.0, 821.0, 821.0, 831.0, 838.0, 840.0, 841.0]",524.6399963378906,1026.1324966430664,1755590827.841759,True,20,20,20,3,2,8,False,2
5,1440.0,810.0,918.95,785.75,"[910.0, 900.0, 904.0, 913.0, 903.0, 907.0, 909.0, 905.0, 912.0, 917.0, 913.0, 919.0, 922.0, 929.0, 919.0, 932.0, 934.0, 941.0, 948.0, 942.0]","[786.0, 778.0, 779.0, 772.0, 774.0, 775.0, 783.0, 772.0, 776.0, 787.0, 778.0, 782.0, 783.0, 793.0, 792.0, 801.0, 798.0, 795.0, 805.0, 806.0]",1427.4649963378906,832.4399978637696,1755590830.4974966,True,20,20,20,5,2,9,False,2
4,960.0,810.0,863.2,758.75,"[886.0, 883.0, 880.0, 882.0, 874.0, 860.0, 859.0, 857.0, 853.0, 845.0, 854.0, 848.0, 853.0, 856.0, 857.0, 855.0, 861.0, 865.0, 868.0, 868.0]","[788.0, 781.0, 771.0, 769.0, 756.0, 751.0, 737.0, 742.0, 740.0, 744.0, 747.0, 752.0, 751.0, 760.0, 759.0, 761.0, 764.0, 769.0, 764.0, 769.0]",978.7375,881.6500030517578,1755590833.1522264,True,20,20,20,4,2,10,False,2
1,960.0,270.0,1034.45,833.2,"[989.0, 995.0, 1001.0, 1011.0, 1017.0, 1026.0, 1022.0, 1031.0, 1037.0, 1047.0, 1052.0, 1064.0, 1060.0, 1069.0, 1064.0, 1049.0, 1050.0, 1043.0, 1036.0, 1026.0]","[819.0, 812.0, 818.0, 818.0, 810.0, 823.0, 815.0, 818.0, 823.0, 827.0, 837.0, 848.0, 851.0, 841.0, 856.0, 841.0, 851.0, 849.0, 852.0, 855.0]",941.9499984741211,212.1424991607666,1755590835.8069718,True,20,20,20,1,2,11,False,2
0,480.0,270.0,1110.0,922.95,"[1048.0, 1071.0, 1066.0, 1079.0, 1097.0, 1086.0, 1093.0, 1086.0, 1091.0, 1092.0, 1107.0, 1107.0, 1125.0, 1124.0, 1137.0, 1149.0, 1152.0, 1160.0, 1158.0, 1172.0]","[887.0, 904.0, 904.0, 902.0, 908.0, 908.0, 910.0, 903.0, 905.0, 915.0, 917.0, 918.0, 925.0, 930.0, 942.0, 945.0, 951.0, 953.0, 964.0, 968.0]",461.2125015258789,222.7675018310547,1755590838.477194,True,20,20,20,0,2,12,False,2
5,1440.0,810.0,854.2,772.5,"[875.0, 877.0, 864.0, 863.0, 856.0, 856.0, 851.0, 856.0, 864.0, 850.0, 860.0, 854.0, 853.0, 848.0, 843.0, 841.0, 842.0, 842.0, 843.0, 846.0]","[786.0, 776.0, 776.0, 768.0, 765.0, 774.0, 775.0, 778.0, 770.0, 759.0, 767.0, 764.0, 778.0, 776.0, 770.0, 774.0, 774.0, 769.0, 777.0, 774.0]",1407.6050048828124,852.7649978637695,1755590841.1370382,True,20,20,20,5,3,13,False,3
2,1440.0,270.0,1048.35,880.2,"[1032.0, 1039.0, 1045.0, 1054.0, 1052.0, 1063.0, 1066.0, 1065.0, 1069.0, 1065.0, 1062.0, 1061.0, 1055.0, 1050.0, 1047.0, 1035.0, 1032.0, 1026.0, 1031.0, 1018.0]","[854.0, 859.0, 863.0, 874.0, 877.0, 885.0, 882.0, 885.0, 884.0, 889.0, 888.0, 892.0, 896.0, 893.0, 890.0, 883.0, 877.0, 878.0, 878.0, 877.0]",1445.7499969482421,187.35000076293946,1755590843.803231,True,20,20,20,2,3,14,False,3
