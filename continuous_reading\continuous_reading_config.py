#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
连续文本阅读实验配置文件
包含实验的各种设置参数
"""

# ==================== 文本阅读配置 ====================
# 文本文件设置
TEXT_CONFIG = {
    # 'text_file': 'banshengyuan.txt',           # 阅读文本文件名
    # 'text_file': '9pt.txt',           # 阅读文本文件名
    # 'text_file': 'yangfang.txt',           # 阅读文本文件名
    # 'text_file': 'reading_sentenses.txt', 
    'text_file': 'reading_material\\CS285\\LEC_20.txt', 
    'encoding': 'utf-8',                       # 文件编码
    'sentence_delimiters': ['。', '？', '！', '?', '!', '.'],  # 句子分隔符列表（支持中英文标点）
    'min_sentence_length': 1,                  # 最小句子长度
    'max_sentence_length': 500,                # 最大句子长度
    'skip_empty_lines': True,                  # 是否跳过空行
    'strip_whitespace': True,                  # 是否去除首尾空白
    # 'sentences_per_display': 2                 # 每次显示的句子数量 无效的
}

# ==================== 基线标注配置 ====================
# 十字星基线设置
BASELINE_CONFIG = {
    'baseline_interval': 6,                    # 每隔多少个句子显示基线（0表示不显示）
    'baseline_duration': 3.0,                 # 基线显示时长（秒）
    'fixation_cross_size': 50,                # 十字星大小（像素）
    'fixation_cross_color': 'white',        # 十字星颜色
    'fixation_cross_width': 3,                # 十字星线宽
    'baseline_background_color': 'black'      # 基线背景颜色
}

# ==================== 实验时间配置 ====================
# 各阶段持续时间（秒）
TIMING_CONFIG = {
    'sentence_display': None,                  # 句子显示时间（None表示等待按键）
    'sentence_min_display': 0,              # 句子最小显示时间
    'rating_timeout': 5.0,                   # 评分超时时间
    'inter_sentence_interval': 0,           # 句子间间隔
    'experiment_start_delay': 2.0,            # 实验开始前延迟
    'experiment_end_delay': 1.0               # 实验结束后延迟
}

# ==================== 评分配置 ====================
# 评分量表设置
RATING_CONFIG = {
    # 好奇心评分
    'curiosity_rating': {
        'question': '此刻, 你有多好奇，多想接着读下去?',
        'scale': (1, 5),
        'labels': {
            1: '完全不好奇,不想读',
            2: '不太想接着读',
            3: '读不读都行',
            4: '比较想接着读',
            5: '极其好奇, 想立刻接着读'
        },
        'instruction': '请按数字键1-5进行评分，然后按回车确认',
        'required': True
    },
    
    # 有趣度评分
    'interest_rating': {
        'question': '刚才读的内容有多有趣, 有多少"aha, 原来是这样"的感觉?',
        'scale': (1, 5),
        'labels': {
            1: '太无聊了',
            2: '有点无聊',
            3: '没感觉',
            4: '有点意思哦',
            5: '太有趣了, 太妙了'
        },
        'instruction': '请按数字键1-5进行评分，然后按回车确认',
        'required': True
    },
    
    # # 理解程度评分
    # 'comprehension_rating': {
    #     'question': '你在阅读这段内容的理解程度?',
    #     'scale': (1, 4),
    #     'labels': {
    #         1: '完全理解',
    #         2: '基本理解了吧',
    #         3: '有些地方读不懂',
    #         4: '完全看不懂'
    #     },
    #     'instruction': '请按数字键1-4进行评分，然后按回车确认',
    #     'required': True
    # },
    
    # # 费劲程度评分
    # 'difficulty_rating': {
    #     'question': '你在阅读时的费劲程度?',
    #     'scale': (1, 3),
    #     'labels': {
    #         1: '秒懂,毫不费力',
    #         2: '要专注的看才行',
    #         3: '有些吃力'
    #     },
    #     'instruction': '请按数字键1-3进行评分，然后按回车确认',
    #     'required': True
    # }
}

# 评分顺序
# RATING_ORDER = ['curiosity_rating', 'interest_rating', 'comprehension_rating', 'difficulty_rating']
# RATING_ORDER = ['interest_rating',]
RATING_ORDER = ['interest_rating','curiosity_rating',]

# ==================== 显示配置 ====================
# 屏幕显示设置
DISPLAY_CONFIG = {
    'fullscreen': True,                        # 是否全屏显示
    'screen_size': (1920, 1080),               # 屏幕分辨率
    'background_color': 'black',               # 背景颜色
    'text_color': '#202020',                   # 文字颜色
    'font_size': 32,                           # 字体大小
    'font_name': 'SimHei',                     # 字体名称（支持中文）
    'text_wrap_width': 1200,                   # 文本换行宽度
    'line_spacing': 1.5,                       # 行间距
    'text_posy': 250,                           # 文本位置（相对于屏幕中心）
    'rating_font_size': 24,                    # 评分界面字体大小
    'instruction_font_size': 20                # 说明文字字体大小
}

# ==================== EyeLink配置 ====================
# EyeLink眼动仪设置
EYELINK_CONFIG = {
    'use_eyelink': True,                       # 是否使用EyeLink
    'dummy_mode': False,                       # 是否使用模拟模式
    'calibration_type': 'HV9',                # 校准类型
    'sampling_rate': 1000,                    # 采样率
    'file_prefix': 'reading',                 # EDF文件前缀
    'pupil_size_mode': 'AREA',            # 瞳孔大小模式
    'track_eyes': 'BOTH'                      # 追踪眼睛：LEFT, RIGHT, BOTH
}

# ==================== 摄像头录像配置 ====================
# 摄像头录像设置
CAMERA_CONFIG = {
    'enable_camera': True,                     # 是否启用摄像头录像
    'camera_index': 1,                         # 摄像头索引（通常0为默认摄像头，2为外置摄像头）
    'video_width': 1920,                       # 录像宽度（1080p）
    'video_height': 1080,                      # 录像高度
    'video_fps': 30,                           # 录像帧率
    'video_format': 'avi',                     # 视频文件格式（avi/mp4）
    'video_codec': 'MJPG',                     # 视频编码器（MJPG/avc1/hev1）
    'video_filename_suffix': 'facial_expression',  # 视频文件名后缀
    'buffer_size': 10,                         # 摄像头缓冲区大小
    'dummy_mode': False                        # 摄像头虚拟模式（用于测试）
}

# ==================== 数据保存配置 ====================
# 数据保存设置
DATA_CONFIG = {
    'base_data_dir': 'data',                  # 基础数据目录
    'save_summary': True,                     # 是否保存实验摘要
    'save_csv': True,                         # 是否保存CSV格式数据
    'save_log': True,                         # 是否保存日志文件
    'auto_backup': False,                     # 是否自动备份
    'include_sentence_text': True,            # 是否在数据中包含句子文本
    'max_sentence_text_length': 1000           # 保存的句子文本最大长度
}

# ==================== 实验流程配置 ====================
# 实验流程设置
EXPERIMENT_CONFIG = {
    'max_sentences': None,                    # 最大句子数量（None表示读完整个文本）
    'start_from_sentence': 1,                 # 从第几个句子开始
    'allow_skip': False,                      # 是否允许跳过句子
    'show_progress': True,                    # 是否显示进度
    'pause_between_ratings': 0,            # 评分间暂停时间
    'confirm_quit': True,                     # 退出时是否确认
    'auto_advance': False,                    # 是否自动推进（不等待按键）
    'sentences_per_display': 8,               # 每次显示的句子数量（当use_character_count_mode为False时使用）
    'multi_sentence_separator': '\n\n',      # 多句子显示时的分隔符
    'use_character_count_mode': True,         # 是否使用总字数控制模式
    'target_character_count': 40,            # 目标字数（当use_character_count_mode为True时使用）
    'listening_mode': False,                  # 是否启用听力模式（朗读文本而非显示）
}

# ==================== 听力模式配置 ====================
# 听力模式设置
LISTENING_CONFIG = {
    'voice': 'zh-CN-YunxiNeural',            # TTS语音
    'rate': '-0%',                          # 语速调整
    'prefix_text': '',                    # 朗读前缀（防止声音被吞掉）
    'dot_size': 20,                          # 白点大小（像素）
    'dot_color': 'black',                    # 白点颜色
    'background_color': 'black',             # 背景颜色
    'auto_advance_to_rating': True,          # 朗读完成后自动进入评分
    # 圆点位置设置（相对于屏幕中心的偏移，单位：像素）
    'dot_position_x': 0,                     # X轴偏移（正值向右，负值向左）
    'dot_position_y': 350,                     # Y轴偏移（正值向上，负值向下）
}

# ==================== Gemini测试配置 ====================
# Gemini AI测试题生成设置
GEMINI_CONFIG = {
    'enable_gemini_quiz': True,               # 是否启用Gemini测试功能
    'test_interval': 10,
    'api_key': 'AIzaSyDgHKaJRSKK-ywZTBqv6Eu-C_uTxD3Hh5c',  # Gemini API密钥
    'custom_prompt': '''''',
    'max_questions_per_test': 8,
    'min_questions_per_test': 3,             # 每次测试最小题目数量
    'question_timeout': 60,
    'show_correct_answer': True,
    'show_explanation': True,
    'auto_advance_after_answer': False,      # 答题后是否自动进入下一题
}

# ==================== 调试配置 ====================
# 调试和测试设置
DEBUG_CONFIG = {
    'debug_mode': False,                      # 是否开启调试模式
    'verbose_logging': True,                  # 是否详细记录日志
    'test_mode': False,                       # 是否测试模式
    'skip_calibration': False,                # 是否跳过校准
    'simulate_ratings': False,                # 是否模拟评分（测试用）
    'max_test_sentences': 10                  # 测试模式最大句子数
}

# ==================== 按键配置 ====================
# 按键设置
KEY_CONFIG = {
    'continue_key': 'space',                  # 继续阅读按键
    'quit_key': 'escape',                     # 退出按键
    'rating_keys': ['1', '2', '3', '4', '5'], # 评分按键
    'confirm_key': 'return',                  # 确认按键
    'skip_key': 's'                           # 跳过按键（如果允许）
}

# ==================== 消息配置 ====================
# EyeLink消息配置
MESSAGE_CONFIG = {
    'use_english_messages': True,             # 是否使用英文消息
    'sentence_prefix': 'sentence',            # 句子消息前缀
    'rating_prefix': 'rating',                # 评分消息前缀
    'baseline_prefix': 'baseline',            # 基线消息前缀
    'include_sentence_number': True,          # 是否包含句子编号
    'include_rating_value': True              # 是否包含评分值
}

# ==================== 辅助函数 ====================
def get_rating_config(rating_type):
    """获取指定评分类型的配置"""
    return RATING_CONFIG.get(rating_type, {})

def get_all_rating_types():
    """获取所有评分类型"""
    return list(RATING_CONFIG.keys())

def validate_config():
    """验证配置的有效性"""
    errors = []
    warnings = []
    
    # 检查评分顺序
    for rating_type in RATING_ORDER:
        if rating_type not in RATING_CONFIG:
            errors.append(f"评分类型 '{rating_type}' 在RATING_ORDER中但不在RATING_CONFIG中")
    
    # 检查基线间隔
    if BASELINE_CONFIG['baseline_interval'] < 0:
        errors.append("基线间隔不能为负数")
    
    # 检查文件路径
    import os
    if not os.path.exists(TEXT_CONFIG['text_file']):
        warnings.append(f"文本文件 '{TEXT_CONFIG['text_file']}' 不存在")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

if __name__ == "__main__":
    # 验证配置
    result = validate_config()
    if result['valid']:
        print("✓ 配置验证通过")
        if result['warnings']:
            print("警告:")
            for warning in result['warnings']:
                print(f"  ⚠ {warning}")
    else:
        print("✗ 配置验证失败")
        for error in result['errors']:
            print(f"  ✗ {error}")
