#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini集成测试脚本
测试Gemini AI测试题生成功能
"""

import os
import sys
import pygame

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from gemini_client import GeminiClient
from quiz_display import QuizDisplay
from quiz_manager import QuizManager
from gemini_config import GEMINI_CONFIG

def test_gemini_client():
    """测试Gemini客户端"""
    print("=== 测试Gemini客户端 ===")
    
    try:
        client = GeminiClient()
        
        test_text = """
        人工智能是计算机科学的一个分支，它试图理解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
        机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习。
        深度学习是机器学习的一个子集，它使用神经网络来模拟人脑的工作方式。
        自然语言处理是人工智能的另一个重要应用领域，它使计算机能够理解和生成人类语言。
        计算机视觉技术使机器能够识别和理解图像和视频内容。
        """
        
        print("正在生成测试题...")
        result = client.generate_questions(test_text)
        
        if result['success']:
            print(f"✅ 成功生成 {len(result['questions'])} 道测试题")
            for i, q in enumerate(result['questions'], 1):
                print(f"\n题目 {i}: {q['question']}")
                for option, text in q['options'].items():
                    print(f"  {option}. {text}")
                print(f"  正确答案: {q['correct_answer']}")
                print(f"  解释: {q['explanation'][:100]}...")
            return True
        else:
            print(f"❌ 生成测试题失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_quiz_display():
    """测试测试题显示"""
    print("\n=== 测试测试题显示 ===")
    
    try:
        # 初始化pygame
        pygame.init()
        screen = pygame.display.set_mode((1200, 800))
        pygame.display.set_caption("Gemini测试题显示测试")
        
        # 创建显示器
        display = QuizDisplay(screen)
        
        # 模拟测试题数据
        test_question = {
            'question': '什么是机器学习？',
            'options': {
                'A': '一种让计算机自动学习的技术',
                'B': '一种编程语言',
                'C': '一种硬件设备',
                'D': '一种操作系统'
            },
            'correct_answer': 'A',
            'explanation': '机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习。'
        }
        
        # 显示测试介绍
        if display.display_quiz_intro(1, 1):
            # 显示题目
            answer, response_time = display.display_question(test_question, 1, 1)
            
            if answer:
                print(f"用户选择: {answer}, 用时: {response_time:.1f}秒")
                
                # 显示答案反馈
                display.display_answer_feedback(test_question, answer, response_time)
                
                print("✅ 测试题显示测试成功")
                return True
            else:
                print("用户退出测试")
                return False
        else:
            print("用户跳过测试介绍")
            return False
            
    except Exception as e:
        print(f"❌ 显示测试失败: {e}")
        return False
    finally:
        pygame.quit()

def test_quiz_manager():
    """测试测试题管理器"""
    print("\n=== 测试测试题管理器 ===")
    
    try:
        # 初始化pygame
        pygame.init()
        screen = pygame.display.set_mode((1200, 800))
        pygame.display.set_caption("Gemini测试题管理器测试")
        
        # 创建管理器
        manager = QuizManager(screen, data_dir="test_data")
        
        test_text = """
        深度学习是机器学习的一个重要分支，它模拟人脑神经网络的工作原理。
        卷积神经网络（CNN）特别适用于图像识别任务。
        循环神经网络（RNN）适用于处理序列数据，如文本和语音。
        Transformer架构在自然语言处理领域取得了突破性进展。
        """
        
        # 运行测试
        result = manager.run_quiz(test_text, segment_number=1)
        
        if result['success']:
            print(f"✅ 测试管理器测试成功")
            print(f"正确率: {result['accuracy']:.1%}")
            print(f"平均答题时间: {result['avg_response_time']:.1f}秒")
            return True
        else:
            print(f"❌ 测试管理器测试失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 管理器测试失败: {e}")
        return False
    finally:
        pygame.quit()

def check_api_key():
    """检查API密钥设置"""
    print("=== 检查API密钥设置 ===")
    
    if GEMINI_CONFIG['api_key']:
        print("✅ API密钥已设置")
        return True
    else:
        print("❌ API密钥未设置")
        print("请运行 python setup_api_key.py 设置API密钥")
        return False

def main():
    """主测试函数"""
    print("Gemini集成功能测试")
    print("=" * 50)
    
    # 检查API密钥
    if not check_api_key():
        return
    
    # 测试客户端
    if not test_gemini_client():
        print("客户端测试失败，跳过后续测试")
        return
    
    # 测试显示（需要用户交互）
    print("\n是否测试显示功能？(需要用户交互) [y/N]: ", end="")
    if input().lower() == 'y':
        test_quiz_display()
    
    # 测试管理器（需要用户交互）
    print("\n是否测试管理器功能？(需要用户交互) [y/N]: ", end="")
    if input().lower() == 'y':
        test_quiz_manager()
    
    print("\n测试完成！")

if __name__ == "__main__":
    main()
