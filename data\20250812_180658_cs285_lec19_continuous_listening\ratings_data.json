[{"sentence_group_ids": [1, 2, 3], "representative_sentence_id": 1, "sentence_count": 3, "timestamp": 1754993457.6889992, "ratings": {"interest_rating": 3}, "sentence_texts": ["欢迎来到 CS285 第 19 讲。", "在今天的讲座中， 我们将讨论如何将控制问题重构成一个推理问题。", "我们实际上会看到， 上周关于变分推理讲座中的一些思想， 在今天的讲座中会作为解决强化学习问题的方法出现， 一旦这些问题被重构成推理问题。"], "combined_text": "欢迎来到 CS285 第 19 讲。\n\n在今天的讲座中， 我们将讨论如何将控制问题重构成一个推理问题。\n\n我们实际上会看到， 上周关于变分推理讲座中的一些思想， 在今天的讲座中会作为解决强化学习问题的方法出现， 一旦这些问题被重构成推理问题。"}, {"sentence_group_ids": [4, 5], "representative_sentence_id": 4, "sentence_count": 2, "timestamp": 1754993479.302648, "ratings": {"interest_rating": 5}, "sentence_texts": ["在今天的讲座中， 我们将讨论以下几个问题：强化学习和最优控制是否为人类行为提供了一个合理的模型？", "是否有一个比我们目前所见的传统最优性概念更好的解释？"], "combined_text": "在今天的讲座中， 我们将讨论以下几个问题：强化学习和最优控制是否为人类行为提供了一个合理的模型？\n\n是否有一个比我们目前所见的传统最优性概念更好的解释？"}, {"sentence_group_ids": [6, 7, 8], "representative_sentence_id": 6, "sentence_count": 3, "timestamp": 1754993502.370093, "ratings": {"interest_rating": 4}, "sentence_texts": ["我们能否将最优控制、强化学习和规划推导为概率推理？", "如果可以， 我们是在什么模型中进行推理的？", "这又如何改变我们的强化学习算法， 我们是否能基于这个基础推导出更好的算法？"], "combined_text": "我们能否将最优控制、强化学习和规划推导为概率推理？\n\n如果可以， 我们是在什么模型中进行推理的？\n\n这又如何改变我们的强化学习算法， 我们是否能基于这个基础推导出更好的算法？"}, {"sentence_group_ids": [9, 10], "representative_sentence_id": 9, "sentence_count": 2, "timestamp": 1754993533.9703894, "ratings": {"interest_rating": 3}, "sentence_texts": ["在下一讲中， 我们将看到这些思想对于逆强化学习方法是何等关键， 这些方法试图通过观察近乎最优的人类行为来恢复奖励函数。", "因此， 今天讲座的目标是: 理解推理与控制之间的联系， 理解特定的强化学习算法如何在这个框架下实例化， 以及理解为什么这可能是一个好主意。"], "combined_text": "在下一讲中， 我们将看到这些思想对于逆强化学习方法是何等关键， 这些方法试图通过观察近乎最优的人类行为来恢复奖励函数。\n\n因此， 今天讲座的目标是: 理解推理与控制之间的联系， 理解特定的强化学习算法如何在这个框架下实例化， 以及理解为什么这可能是一个好主意。"}, {"sentence_group_ids": [11, 12], "representative_sentence_id": 11, "sentence_count": 2, "timestamp": 1754993555.2369819, "ratings": {"interest_rating": 4}, "sentence_texts": ["让我们从讨论如何建模人类行为开始。", "我们知道， 人类有时会做一些有目标导向的事情， 我们可以争论人类有多理性， 但我认为， 说人类行为的某一部分是目标导向和有意的， 这是合理的。"], "combined_text": "让我们从讨论如何建模人类行为开始。\n\n我们知道， 人类有时会做一些有目标导向的事情， 我们可以争论人类有多理性， 但我认为， 说人类行为的某一部分是目标导向和有意的， 这是合理的。"}, {"sentence_group_ids": [13], "representative_sentence_id": 13, "sentence_count": 1, "timestamp": 1754993581.206878, "ratings": {"interest_rating": 4}, "sentence_texts": ["人们研究这个问题已经有超过 100 年的历史了， 研究最优性的概念如何能最好地代表人类行为， 从最低级的原始行为， 比如我们如何走路、如何导航到某个地方、如何伸手去拿东西， 一直到更高级的概念， 比如我们如何规划在城市中导航的路线。"], "combined_text": "人们研究这个问题已经有超过 100 年的历史了， 研究最优性的概念如何能最好地代表人类行为， 从最低级的原始行为， 比如我们如何走路、如何导航到某个地方、如何伸手去拿东西， 一直到更高级的概念， 比如我们如何规划在城市中导航的路线。"}, {"sentence_group_ids": [14], "representative_sentence_id": 14, "sentence_count": 1, "timestamp": 1754993599.735016, "ratings": {"interest_rating": 4}, "sentence_texts": ["认为如果人类是理性的、智能的生物， 那么我们应该以反映某种最优性的方式来执行我们的任务， 从最低级的运动控制到最高级的认知技能， 这是相当合理的。"], "combined_text": "认为如果人类是理性的、智能的生物， 那么我们应该以反映某种最优性的方式来执行我们的任务， 从最低级的运动控制到最高级的认知技能， 这是相当合理的。"}, {"sentence_group_ids": [15, 16], "representative_sentence_id": 15, "sentence_count": 2, "timestamp": 1754993624.8093934, "ratings": {"interest_rating": 3}, "sentence_texts": ["事实上， 定义理性的方式之一就是说， 一个理性的决策者， 其行为可以用明确定义的效用函数来表达。", "所以， 我们可以说， 我们有一个很好的框架来思考最优决策， 即我们在讨论强化学习和最优控制时学到的框架。"], "combined_text": "事实上， 定义理性的方式之一就是说， 一个理性的决策者， 其行为可以用明确定义的效用函数来表达。\n\n所以， 我们可以说， 我们有一个很好的框架来思考最优决策， 即我们在讨论强化学习和最优控制时学到的框架。"}, {"sentence_group_ids": [17, 18], "representative_sentence_id": 17, "sentence_count": 2, "timestamp": 1754993651.580223, "ratings": {"interest_rating": 5}, "sentence_texts": ["我们能用这个框架来理解人类行为吗？", "例如， 我们可以做的一件事是， 假设一个人或一个动物的行为方式是最优的， 他们是理性的决策者， 那么我们能否找出他们是在哪个奖励函数下表现为最优的？"], "combined_text": "我们能用这个框架来理解人类行为吗？\n\n例如， 我们可以做的一件事是， 假设一个人或一个动物的行为方式是最优的， 他们是理性的决策者， 那么我们能否找出他们是在哪个奖励函数下表现为最优的？"}, {"sentence_group_ids": [19, 20, 21], "representative_sentence_id": 19, "sentence_count": 3, "timestamp": 1754993672.2971401, "ratings": {"interest_rating": 4}, "sentence_texts": ["这实际上是我们周三讲座要讨论的内容， 关于逆强化学习。", "你可以在确定性环境中做这件事， 也可以在随机性环境中做。", "之前我们讨论的是， 给定奖励函数， 我们想恢复一个策略。"], "combined_text": "这实际上是我们周三讲座要讨论的内容， 关于逆强化学习。\n\n你可以在确定性环境中做这件事， 也可以在随机性环境中做。\n\n之前我们讨论的是， 给定奖励函数， 我们想恢复一个策略。"}, {"sentence_group_ids": [22, 23], "representative_sentence_id": 22, "sentence_count": 2, "timestamp": 1754993699.0424955, "ratings": {"interest_rating": 5}, "sentence_texts": ["现在我们可能会想， 我们正在观察一个人， 观察他们的策略， 我们想找到一个合适的奖励函数来解释我们观察到的数据和行为。", "如果我们研究人类或动物行为， 这是一个非常诱人的想法， 因为最优性原则提供了一个强大的工具来理解某人为什么会做某件事。"], "combined_text": "现在我们可能会想， 我们正在观察一个人， 观察他们的策略， 我们想找到一个合适的奖励函数来解释我们观察到的数据和行为。\n\n如果我们研究人类或动物行为， 这是一个非常诱人的想法， 因为最优性原则提供了一个强大的工具来理解某人为什么会做某件事。"}, {"sentence_group_ids": [24, 25], "representative_sentence_id": 24, "sentence_count": 2, "timestamp": 1754993719.8425543, "ratings": {"interest_rating": 5}, "sentence_texts": ["如果你能用一个简单紧凑的目标函数来解释他们的行为， 那么你就能预测他们在其他情况下会做什么。", "这当然非常直观， 如果你知道某人想要什么， 你就能更有效地预测他们会做什么。"], "combined_text": "如果你能用一个简单紧凑的目标函数来解释他们的行为， 那么你就能预测他们在其他情况下会做什么。\n\n这当然非常直观， 如果你知道某人想要什么， 你就能更有效地预测他们会做什么。"}, {"sentence_group_ids": [26, 27], "representative_sentence_id": 26, "sentence_count": 2, "timestamp": 1754993740.3741064, "ratings": {"interest_rating": 4}, "sentence_texts": ["好， 想象一下你是一名科学家， 你想思考最优控制和强化学习如何解释行为， 比如说动物的行为。", "你找来一只猴子， 让这只猴子完成一些任务， 你想了解它的目标函数。"], "combined_text": "好， 想象一下你是一名科学家， 你想思考最优控制和强化学习如何解释行为， 比如说动物的行为。\n\n你找来一只猴子， 让这只猴子完成一些任务， 你想了解它的目标函数。"}, {"sentence_group_ids": [28, 29], "representative_sentence_id": 28, "sentence_count": 2, "timestamp": 1754993759.641657, "ratings": {"interest_rating": 4}, "sentence_texts": ["你可能做的一件事是， 选择一个目标函数已知的任务， 也许猴子需要移动一个杠杆， 让屏幕上的一个点与另一个点匹配。", "然后， 如果猴子做对了， 它就会得到奖励。"], "combined_text": "你可能做的一件事是， 选择一个目标函数已知的任务， 也许猴子需要移动一个杠杆， 让屏幕上的一个点与另一个点匹配。\n\n然后， 如果猴子做对了， 它就会得到奖励。"}, {"sentence_group_ids": [30], "representative_sentence_id": 30, "sentence_count": 1, "timestamp": 1754993778.2733307, "ratings": {"interest_rating": 4}, "sentence_texts": ["现在你可能会说， 我知道奖励函数， 所以如果最优行为是对动物行为的一个很好的解释， 那么我期望猴子的行为会类似于一个最优控制器或强化学习算法。"], "combined_text": "现在你可能会说， 我知道奖励函数， 所以如果最优行为是对动物行为的一个很好的解释， 那么我期望猴子的行为会类似于一个最优控制器或强化学习算法。"}, {"sentence_group_ids": [31, 32], "representative_sentence_id": 31, "sentence_count": 2, "timestamp": 1754993801.876396, "ratings": {"interest_rating": 4}, "sentence_texts": ["假设猴子必须从这个橙色圆圈移动到这个红色十字， 最优的轨迹是这条直线。", "研究人员进行实验后发现， 是的， 这只猴子是一只好猴子， 它通常能到达红色十字， 但它是以各种不同的方式做到的。"], "combined_text": "假设猴子必须从这个橙色圆圈移动到这个红色十字， 最优的轨迹是这条直线。\n\n研究人员进行实验后发现， 是的， 这只猴子是一只好猴子， 它通常能到达红色十字， 但它是以各种不同的方式做到的。"}, {"sentence_group_ids": [33, 34, 35], "representative_sentence_id": 33, "sentence_count": 3, "timestamp": 1754993825.241142, "ratings": {"interest_rating": 4}, "sentence_texts": ["所以它并不总是走直线， 也许有一天猴子觉得有点懒， 但它仍然到达了目的地并得到了奖励。", "那么， 到底发生了什么？", "是猴子有点笨吗？"], "combined_text": "所以它并不总是走直线， 也许有一天猴子觉得有点懒， 但它仍然到达了目的地并得到了奖励。\n\n那么， 到底发生了什么？\n\n是猴子有点笨吗？"}, {"sentence_group_ids": [36, 37, 38], "representative_sentence_id": 36, "sentence_count": 3, "timestamp": 1754993845.2537518, "ratings": {"interest_rating": 4}, "sentence_texts": ["也许一个非常聪明的人会更准确， 但猴子只是不够精确？", "还是有其他事情在发生？", "事实证明， 毫不奇怪， 猴子和人类通常都不是完全最优的， 我们通常会犯错误。"], "combined_text": "也许一个非常聪明的人会更准确， 但猴子只是不够精确？\n\n还是有其他事情在发生？\n\n事实证明， 毫不奇怪， 猴子和人类通常都不是完全最优的， 我们通常会犯错误。"}, {"sentence_group_ids": [39, 40], "representative_sentence_id": 39, "sentence_count": 2, "timestamp": 1754993869.755456, "ratings": {"interest_rating": 5}, "sentence_texts": ["但关键是， 随着更多的练习， 这些错误倾向于发生在对任务成功影响较小的方面。", "直观地说， 猴子可能会选择一条间接的路线到达目标， 是因为它感觉有点懒， 没有真正集中注意力， 但它也知道到达目标的具体方式并没有那么重要。"], "combined_text": "但关键是， 随着更多的练习， 这些错误倾向于发生在对任务成功影响较小的方面。\n\n直观地说， 猴子可能会选择一条间接的路线到达目标， 是因为它感觉有点懒， 没有真正集中注意力， 但它也知道到达目标的具体方式并没有那么重要。"}, {"sentence_group_ids": [41], "representative_sentence_id": 41, "sentence_count": 1, "timestamp": 1754993885.13378, "ratings": {"interest_rating": 5}, "sentence_texts": ["这是我们目前讨论的强化学习算法没有真正考虑到的， 它们没有“偷懒”的概念， 也没有理解“某件事没那么重要， 因此可以做得不那么完美”的概念。"], "combined_text": "这是我们目前讨论的强化学习算法没有真正考虑到的， 它们没有“偷懒”的概念， 也没有理解“某件事没那么重要， 因此可以做得不那么完美”的概念。"}, {"sentence_group_ids": [42, 43], "representative_sentence_id": 42, "sentence_count": 2, "timestamp": 1754993908.776196, "ratings": {"interest_rating": 4}, "sentence_texts": ["有些错误比其他错误更重要。", "事实证明， 适当地考虑这一点， 对于开发能够解释人类和动物智能行为的模型至关셔重要， 并且也成为我们构建更好强化学习算法以及之后构建逆强化学习算法的工具。"], "combined_text": "有些错误比其他错误更重要。\n\n事实证明， 适当地考虑这一点， 对于开发能够解释人类和动物智能行为的模型至关셔重要， 并且也成为我们构建更好强化学习算法以及之后构建逆强化学习算法的工具。"}, {"sentence_group_ids": [44, 45, 46], "representative_sentence_id": 44, "sentence_count": 3, "timestamp": 1754993933.1456428, "ratings": {"interest_rating": 3}, "sentence_texts": ["人类和动物的自然行为， 在一阶近似下是随机的。", "这意味着， 两次面对同样的情况， 猴子不会做完全相同的事情。", "我们可以争论这到底是真正的随机， 还是仅仅受到实验中未考虑的大量其他外部和内部因素的影响。"], "combined_text": "人类和动物的自然行为， 在一阶近似下是随机的。\n\n这意味着， 两次面对同样的情况， 猴子不会做完全相同的事情。\n\n我们可以争论这到底是真正的随机， 还是仅仅受到实验中未考虑的大量其他外部和内部因素的影响。"}, {"sentence_group_ids": [47, 48], "representative_sentence_id": 47, "sentence_count": 2, "timestamp": 1754993952.7385042, "ratings": {"interest_rating": 4}, "sentence_texts": ["例如， 也许猴子饿了， 也许它的手指痒了， 也许它只是有点累， 也许它分心了， 不小心把操纵杆向左偏了一点。", "但我们可以将这些效应在一阶近似下视为随机的。"], "combined_text": "例如， 也许猴子饿了， 也许它的手指痒了， 也许它只是有点累， 也许它分心了， 不小心把操纵杆向左偏了一点。\n\n但我们可以将这些效应在一阶近似下视为随机的。"}, {"sentence_group_ids": [49, 50], "representative_sentence_id": 49, "sentence_count": 2, "timestamp": 1754993971.2125888, "ratings": {"interest_rating": 4}, "sentence_texts": ["然而， 好的行为仍然是最有可能的。", "所以， 虽然猴子可能会容忍一些适度的错误， 它仍然会确保到达目标， 因为那才能让它得到它非常想要的奖励。"], "combined_text": "然而， 好的行为仍然是最有可能的。\n\n所以， 虽然猴子可能会容忍一些适度的错误， 它仍然会确保到达目标， 因为那才能让它得到它非常想要的奖励。"}, {"sentence_group_ids": [51, 52], "representative_sentence_id": 51, "sentence_count": 2, "timestamp": 1754993999.9834194, "ratings": {"interest_rating": 5}, "sentence_texts": ["因此， 如果我们相信自然的理性决策者的行为是随机的， 那么我们就需要一个近乎最优行为的概率模型。", "我们现有的模型并没有真正做到这一点， 它们没有告诉我们为什么我们可能会选择随机而非最优。"], "combined_text": "因此， 如果我们相信自然的理性决策者的行为是随机的， 那么我们就需要一个近乎最优行为的概率模型。\n\n我们现有的模型并没有真正做到这一点， 它们没有告诉我们为什么我们可能会选择随机而非最优。"}, {"sentence_group_ids": [53], "representative_sentence_id": 53, "sentence_count": 1, "timestamp": 1754994016.367716, "ratings": {"interest_rating": 4}, "sentence_texts": ["事实上， 对于确定性和随机性的最优控制和强化学习公式， 我们都可以证明， 在所有完全可观测的环境中， 都存在确定性的最优策略。"], "combined_text": "事实上， 对于确定性和随机性的最优控制和强化学习公式， 我们都可以证明， 在所有完全可观测的环境中， 都存在确定性的最优策略。"}, {"sentence_group_ids": [54], "representative_sentence_id": 54, "sentence_count": 1, "timestamp": 1754994035.9857082, "ratings": {"interest_rating": 5}, "sentence_texts": ["这对于任何在状态-动作边缘分布上是线性的目标函数都成立， 也就是说， 任何可以表示为某个不依赖于策略的奖励在状态-动作分布下的期望值的目标， 都会有一个确定性策略作为解。"], "combined_text": "这对于任何在状态-动作边缘分布上是线性的目标函数都成立， 也就是说， 任何可以表示为某个不依赖于策略的奖励在状态-动作分布下的期望值的目标， 都会有一个确定性策略作为解。"}, {"sentence_group_ids": [55, 56, 57], "representative_sentence_id": 55, "sentence_count": 3, "timestamp": 1754994055.404397, "ratings": {"interest_rating": 4}, "sentence_texts": ["所以， 很明显， 这个框架无法解释随机行为是理性的。", "我们需要一种不同的理性概念。", "当我们想表示随机事件时， 一个我们经常求助的强大工具是概率图模型。"], "combined_text": "所以， 很明显， 这个框架无法解释随机行为是理性的。\n\n我们需要一种不同的理性概念。\n\n当我们想表示随机事件时， 一个我们经常求助的强大工具是概率图模型。"}, {"sentence_group_ids": [58, 59, 60], "representative_sentence_id": 58, "sentence_count": 3, "timestamp": 1754994079.376887, "ratings": {"interest_rating": 5}, "sentence_texts": ["这就是我们今天讲座要做的事情。", "我们实际上要画一个概率图模型， 使得在该模型中进行推理能够产生近乎最优的行为。", "关键是， 这种近乎最优的行为并不总是与强化学习和最优控制的解相同， 但会非常相似。"], "combined_text": "这就是我们今天讲座要做的事情。\n\n我们实际上要画一个概率图模型， 使得在该模型中进行推理能够产生近乎最优的行为。\n\n关键是， 这种近乎最优的行为并不总是与强化学习和最优控制的解相同， 但会非常相似。"}, {"sentence_group_ids": [61], "representative_sentence_id": 61, "sentence_count": 1, "timestamp": 1754994101.1213114, "ratings": {"interest_rating": 4}, "sentence_texts": ["它会非常像我们前一张幻灯片中看到的次优猴子行为， 即在其他条件相同的情况下， 智能体会尝试完成任务， 但对于那些对任务影响较小、对奖励影响很小的方面， 智能体宁愿随机地去做。"], "combined_text": "它会非常像我们前一张幻灯片中看到的次优猴子行为， 即在其他条件相同的情况下， 智能体会尝试完成任务， 但对于那些对任务影响较小、对奖励影响很小的方面， 智能体宁愿随机地去做。"}, {"sentence_group_ids": [62, 63], "representative_sentence_id": 62, "sentence_count": 2, "timestamp": 1754994122.1946046, "ratings": {"interest_rating": 3}, "sentence_texts": ["在思考如何为决策和控制绘制一个图模型时， 我们当然必须包含我们在马尔可夫决策过程（MDP）中常见的变量， 即状态和动作。", "我们已经知道状态和动作是如何相互关联的。"], "combined_text": "在思考如何为决策和控制绘制一个图模型时， 我们当然必须包含我们在马尔可夫决策过程（MDP）中常见的变量， 即状态和动作。\n\n我们已经知道状态和动作是如何相互关联的。"}, {"sentence_group_ids": [64, 65], "representative_sentence_id": 64, "sentence_count": 2, "timestamp": 1754994145.5989532, "ratings": {"interest_rating": 4}, "sentence_texts": ["目前， 我们将坚持使用完全可观测的设置， 所以我们也可以将观测值加入这个谱系， 但为了避免符号混乱， 我们暂时不考虑它。", "然而， 我们需要添加一些额外的变量来代表任务， 代表为什么智能体可能选择一个动作而不是另一个。"], "combined_text": "目前， 我们将坚持使用完全可观测的设置， 所以我们也可以将观测值加入这个谱系， 但为了避免符号混乱， 我们暂时不考虑它。\n\n然而， 我们需要添加一些额外的变量来代表任务， 代表为什么智能体可能选择一个动作而不是另一个。"}, {"sentence_group_ids": [66, 67, 68], "representative_sentence_id": 66, "sentence_count": 3, "timestamp": 1754994169.762144, "ratings": {"interest_rating": 3}, "sentence_texts": ["到目前TA， 我们有转移概率 p(s'|s, a)， 我们的目标是建模轨迹的联合分布 p(s1.", "T, a1.", "T)， 也就是我们的轨迹 τ。"], "combined_text": "到目前TA， 我们有转移概率 p(s'|s, a)， 我们的目标是建模轨迹的联合分布 p(s1.\n\nT, a1.\n\nT)， 也就是我们的轨迹 τ。"}, {"sentence_group_ids": [69, 70], "representative_sentence_id": 69, "sentence_count": 2, "timestamp": 1754994189.157226, "ratings": {"interest_rating": 3}, "sentence_texts": ["我们可以将这个概率设为什么呢？", "如果我们只有 MDP 中的条件概率分布（CPD）、转移概率和初始状态分布， 那么就没有最优行为的假设。"], "combined_text": "我们可以将这个概率设为什么呢？\n\n如果我们只有 MDP 中的条件概率分布（CPD）、转移概率和初始状态分布， 那么就没有最优行为的假设。"}, {"sentence_group_ids": [71, 72], "representative_sentence_id": 71, "sentence_count": 2, "timestamp": 1754994206.2334156, "ratings": {"interest_rating": 3}, "sentence_texts": ["所以我们必须添加一些其他东西来表示为什么你可能会选择一个更优的动作而不是一个次优的动作。", "我们将这些称为“最优性变量”， 我将用草书 O 来表示。"], "combined_text": "所以我们必须添加一些其他东西来表示为什么你可能会选择一个更优的动作而不是一个次优的动作。\n\n我们将这些称为“最优性变量”， 我将用草书 O 来表示。"}, {"sentence_group_ids": [73, 74, 75], "representative_sentence_id": 73, "sentence_count": 3, "timestamp": 1754994234.1335943, "ratings": {"interest_rating": 4}, "sentence_texts": ["这些最优性变量是被观测到的， 你知道猴子正在努力完成任务。", "如果你不知道这一点， 你会对它的行为做出不同的推断。", "现在我们将做一个稍微奇怪的建模选择， 但稍后我们会看到， 这个建模选择实际上会导出一个非常方便和优雅的数学公式。"], "combined_text": "这些最优性变量是被观测到的， 你知道猴子正在努力完成任务。\n\n如果你不知道这一点， 你会对它的行为做出不同的推断。\n\n现在我们将做一个稍微奇怪的建模选择， 但稍后我们会看到， 这个建模选择实际上会导出一个非常方便和优雅的数学公式。"}, {"sentence_group_ids": [76, 77], "representative_sentence_id": 76, "sentence_count": 2, "timestamp": 1754994257.8414125, "ratings": {"interest_rating": 4}, "sentence_texts": ["我们做的建模选择是， 这些变量是二元的， 你可以认为它们基本上是真或假的变量， 表示“猴子在此时此刻是否试图做到最优？", "”如果猴子总是在努力做到最优， 那么所有这些变量都是被观测到的， 并且都设置为真。"], "combined_text": "我们做的建模选择是， 这些变量是二元的， 你可以认为它们基本上是真或假的变量， 表示“猴子在此时此刻是否试图做到最优？\n\n”如果猴子总是在努力做到最优， 那么所有这些变量都是被观测到的， 并且都设置为真。"}, {"sentence_group_ids": [78, 79], "representative_sentence_id": 78, "sentence_count": 2, "timestamp": 1754994276.1589983, "ratings": {"interest_rating": 4}, "sentence_texts": ["那么， 我们需要解决的推理问题就是， 给定从时间 1 到 T 的所有最优性变量都为真， 一个轨迹的概率是多少？", "或者， 我们可能想在给定初始状态的情况下进行这个推理。"], "combined_text": "那么， 我们需要解决的推理问题就是， 给定从时间 1 到 T 的所有最优性变量都为真， 一个轨迹的概率是多少？\n\n或者， 我们可能想在给定初始状态的情况下进行这个推理。"}, {"sentence_group_ids": [80, 81, 82, 83], "representative_sentence_id": 80, "sentence_count": 4, "timestamp": 1754994304.5746577, "ratings": {"interest_rating": 5}, "sentence_texts": ["所以我们可以计算 p(τ | o1.", "T) 或 p(τ | o1.", "T, s1)。", "我们将选择的 p(ot | st, a) 的具体分布形式是， 我们将 ot 等于真的概率设置为在 st, a 处奖励的指数， 即 exp(r(st, a))。"], "combined_text": "所以我们可以计算 p(τ | o1.\n\nT) 或 p(τ | o1.\n\nT, s1)。\n\n我们将选择的 p(ot | st, a) 的具体分布形式是， 我们将 ot 等于真的概率设置为在 st, a 处奖励的指数， 即 exp(r(st, a))。"}, {"sentence_group_ids": [84, 85], "representative_sentence_id": 84, "sentence_count": 2, "timestamp": 1754994331.553393, "ratings": {"interest_rating": 4}, "sentence_texts": ["这又会看起来是一个有些随意的决定， 我们稍后会看到， 这个看似随意的决定实际上会导出一个非常方便和优雅的数学框架。", "但现在， 我们只能把它当作一个给定的条件。"], "combined_text": "这又会看起来是一个有些随意的决定， 我们稍后会看到， 这个看似随意的决定实际上会导出一个非常方便和优雅的数学框架。\n\n但现在， 我们只能把它当作一个给定的条件。"}, {"sentence_group_ids": [86, 87], "representative_sentence_id": 86, "sentence_count": 2, "timestamp": 1754994355.4510944, "ratings": {"interest_rating": 4}, "sentence_texts": ["让我们试试看， 将概率设为这个值， 然后看看数学上会推导出什么。", "为了做出这个陈述， 我们需要一个技术条件， 那就是我们需要所有的奖励都是负数。"], "combined_text": "让我们试试看， 将概率设为这个值， 然后看看数学上会推导出什么。\n\n为了做出这个陈述， 我们需要一个技术条件， 那就是我们需要所有的奖励都是负数。"}, {"sentence_group_ids": [88, 89, 90], "representative_sentence_id": 88, "sentence_count": 3, "timestamp": 1754994379.2125952, "ratings": {"interest_rating": 4}, "sentence_texts": ["因为一个离散（在这里是伯努利）随机变量的概率必须小于 1， 而任何正数的指数都会大于 1。", "所以我们需要所有的奖励都是负数。", "但幸运的是， 最优行为对于奖励的加性因子是不变的。"], "combined_text": "因为一个离散（在这里是伯努利）随机变量的概率必须小于 1， 而任何正数的指数都会大于 1。\n\n所以我们需要所有的奖励都是负数。\n\n但幸运的是， 最优行为对于奖励的加性因子是不变的。"}, {"sentence_group_ids": [91, 92], "representative_sentence_id": 91, "sentence_count": 2, "timestamp": 1754994404.0023332, "ratings": {"interest_rating": 4}, "sentence_texts": ["所以， 如果奖励不是负数， 你可以简单地构造一个等价的决策问题， 其奖励等于旧奖励减去最大可能的奖励。", "基本上， 说奖励总是负的， 只是意味着你减去了最大值， 这意味着所有剩余的奖励都是负的。"], "combined_text": "所以， 如果奖励不是负数， 你可以简单地构造一个等价的决策问题， 其奖励等于旧奖励减去最大可能的奖励。\n\n基本上， 说奖励总是负的， 只是意味着你减去了最大值， 这意味着所有剩余的奖励都是负的。"}, {"sentence_group_ids": [93, 94], "representative_sentence_id": 93, "sentence_count": 2, "timestamp": 1754994428.5235708, "ratings": {"interest_rating": 3}, "sentence_texts": ["所以这实际上不是一个限制， 只要奖励是有界的， 你就可以在不失一般性的情况下这样做。", "当然， 如果奖励是无界的， 这是不可能的， 那么你就可能得到无限的奖励， 但这也不起作用， 反正我也不知道如何处理无限奖励， 所以这也不是一个太大的限制。"], "combined_text": "所以这实际上不是一个限制， 只要奖励是有界的， 你就可以在不失一般性的情况下这样做。\n\n当然， 如果奖励是无界的， 这是不可能的， 那么你就可能得到无限的奖励， 但这也不起作用， 反正我也不知道如何处理无限奖励， 所以这也不是一个太大的限制。"}, {"sentence_group_ids": [95, 96], "representative_sentence_id": 95, "sentence_count": 2, "timestamp": 1754994446.4668863, "ratings": {"interest_rating": 3}, "sentence_texts": ["好， 现在我们定义了一个概率图模型。", "它有动态性， 有奖励， 看起来我们可以做贝叶斯规则式的计算， 得到一些分布， 然后看看这个分布的方程是否合理。"], "combined_text": "好， 现在我们定义了一个概率图模型。\n\n它有动态性， 有奖励， 看起来我们可以做贝叶斯规则式的计算， 得到一些分布， 然后看看这个分布的方程是否合理。"}, {"sentence_group_ids": [97, 98, 99, 100, 101], "representative_sentence_id": 97, "sentence_count": 5, "timestamp": 1754994469.881364, "ratings": {"interest_rating": 2}, "sentence_texts": ["我将条件概率的定义代入， 写出 p(τ | o1.", "T) = p(τ, o1.", "T) / p(o1.", "T)。", "然后我可以将所有的条件概率分布（CPD）代入。"], "combined_text": "我将条件概率的定义代入， 写出 p(τ | o1.\n\nT) = p(τ, o1.\n\nT) / p(o1.\n\nT)。\n\n然后我可以将所有的条件概率分布（CPD）代入。"}, {"sentence_group_ids": [102, 103], "representative_sentence_id": 102, "sentence_count": 2, "timestamp": 1754994493.0564594, "ratings": {"interest_rating": 2}, "sentence_texts": ["我只关心轨迹的概率， 所以我会忽略分母， 写成“正比于”。", "它正比于我们所有 CPD 的乘积， 我将其写为 p(τ)， 这只考虑了动态性和初始状态， 再乘以所有这些伯努利随机变量概率在时间 1 到 T 上的乘积。"], "combined_text": "我只关心轨迹的概率， 所以我会忽略分母， 写成“正比于”。\n\n它正比于我们所有 CPD 的乘积， 我将其写为 p(τ)， 这只考虑了动态性和初始状态， 再乘以所有这些伯努利随机变量概率在时间 1 到 T 上的乘积。"}, {"sentence_group_ids": [104, 105, 106], "representative_sentence_id": 104, "sentence_count": 3, "timestamp": 1754994522.0978532, "ratings": {"interest_rating": 3}, "sentence_texts": ["这基本上意味着， 我们将 p(τ) 的乘积与所有时间步上指数化奖励的乘积相乘。", "这似乎相当合理。", "我们知道， 指数的乘积是指数之和的指数， 所以我们将它等价地写成一种可能更能暗示其作用的方式， 即 p(τ) * exp(∑ r(τ))。"], "combined_text": "这基本上意味着， 我们将 p(τ) 的乘积与所有时间步上指数化奖励的乘积相乘。\n\n这似乎相当合理。\n\n我们知道， 指数的乘积是指数之和的指数， 所以我们将它等价地写成一种可能更能暗示其作用的方式， 即 p(τ) * exp(∑ r(τ))。"}, {"sentence_group_ids": [107, 108], "representative_sentence_id": 107, "sentence_count": 2, "timestamp": 1754994549.5620034, "ratings": {"interest_rating": 3}, "sentence_texts": ["这应该立刻给我们一些相当吸引人的直觉， 关于这个框架在做什么。", "例如， 想象一下动态是确定性的， 这意味着 p(τ) 基本上只是一个指示变量， 如果 τ 是一条物理上一致的轨迹， 它就是 1， 否则就是 0。"], "combined_text": "这应该立刻给我们一些相当吸引人的直觉， 关于这个框架在做什么。\n\n例如， 想象一下动态是确定性的， 这意味着 p(τ) 基本上只是一个指示变量， 如果 τ 是一条物理上一致的轨迹， 它就是 1， 否则就是 0。"}, {"sentence_group_ids": [109, 110], "representative_sentence_id": 109, "sentence_count": 2, "timestamp": 1754994569.6760163, "ratings": {"interest_rating": 5}, "sentence_texts": ["在这种特殊情况下， 我们会看到， 最可能的轨迹是奖励最高的那条。", "然而， 次优的轨迹仍然有非零的概率， 这个概率随着其奖励的减少而呈指数级下降。"], "combined_text": "在这种特殊情况下， 我们会看到， 最可能的轨迹是奖励最高的那条。\n\n然而， 次优的轨迹仍然有非零的概率， 这个概率随着其奖励的减少而呈指数级下降。"}, {"sentence_group_ids": [111, 112, 113], "representative_sentence_id": 111, "sentence_count": 3, "timestamp": 1754994592.07785, "ratings": {"interest_rating": 4}, "sentence_texts": ["这实际上看起来相当直观。", "它基本上意味着， 如果猴子有多个奖励相等的不同选择， 它会随机地从中选择一个。", "但如果有一个选择的奖励低得多， 它选择那个选择的可能性就会呈指数级下降。"], "combined_text": "这实际上看起来相当直观。\n\n它基本上意味着， 如果猴子有多个奖励相等的不同选择， 它会随机地从中选择一个。\n\n但如果有一个选择的奖励低得多， 它选择那个选择的可能性就会呈指数级下降。"}, {"sentence_group_ids": [114, 115], "representative_sentence_id": 114, "sentence_count": 2, "timestamp": 1754994615.331412, "ratings": {"interest_rating": 5}, "sentence_texts": ["所以， 它在伸手去拿目标时偏离直线轨迹的原因是， 它通过任何其他方式到达目标所获得的奖励大致相同。", "也许因为耗时更长， 奖励会低一点， 因为折扣因子让它变得更饿， 但基本上是差不多的。"], "combined_text": "所以， 它在伸手去拿目标时偏离直线轨迹的原因是， 它通过任何其他方式到达目标所获得的奖励大致相同。\n\n也许因为耗时更长， 奖励会低一点， 因为折扣因子让它变得更饿， 但基本上是差不多的。"}, {"sentence_group_ids": [116, 117], "representative_sentence_id": 116, "sentence_count": 2, "timestamp": 1754994632.5832996, "ratings": {"interest_rating": 4}, "sentence_texts": ["而未能到达目标则会导致一个糟糕得多的奖励， 因此它不会那样做。", "所以， 这直观上似乎解释了我们在前一张幻灯片中看到的那种行为。"], "combined_text": "而未能到达目标则会导致一个糟糕得多的奖励， 因此它不会那样做。\n\n所以， 这直观上似乎解释了我们在前一张幻灯片中看到的那种行为。"}, {"sentence_group_ids": [118], "representative_sentence_id": 118, "sentence_count": 1, "timestamp": 1754994648.5607853, "ratings": {"interest_rating": 4}, "sentence_texts": ["我们基本上构建了一个概率模型， 其中最优的轨迹是最可能的， 但次优的轨迹也可能发生， 只是其概率随着奖励的减少而呈指数级下降。"], "combined_text": "我们基本上构建了一个概率模型， 其中最优的轨迹是最可能的， 但次优的轨迹也可能发生， 只是其概率随着奖励的减少而呈指数级下降。"}, {"sentence_group_ids": [119, 120, 121, 122], "representative_sentence_id": 119, "sentence_count": 4, "timestamp": 1754994672.6127481, "ratings": {"interest_rating": 4}, "sentence_texts": ["那么， 这一切为什么有趣呢？", "如果你想为猴子建模， 这可能对你很有趣。", "但如果你不关心猴子呢？", "将次优行为表示为在某种宽松的最优性概念下近似最优的能力， 对于理解次优智能体的行为通常非常重要。"], "combined_text": "那么， 这一切为什么有趣呢？\n\n如果你想为猴子建模， 这可能对你很有趣。\n\n但如果你不关心猴子呢？\n\n将次优行为表示为在某种宽松的最优性概念下近似最优的能力， 对于理解次优智能体的行为通常非常重要。"}, {"sentence_group_ids": [123, 124], "representative_sentence_id": 123, "sentence_count": 2, "timestamp": 1754994694.799405, "ratings": {"interest_rating": 3}, "sentence_texts": ["对于模仿学习也是如此， 如果你想弄清楚一个人试图向你展示什么奖励函数， 你必须考虑到他们不会完美地完成。", "事实证明， 这对于逆强化学习非常重要， 我们将在周三的讲座中讨论。"], "combined_text": "对于模仿学习也是如此， 如果你想弄清楚一个人试图向你展示什么奖励函数， 你必须考虑到他们不会完美地完成。\n\n事实证明， 这对于逆强化学习非常重要， 我们将在周三的讲座中讨论。"}, {"sentence_group_ids": [125, 126], "representative_sentence_id": 125, "sentence_count": 2, "timestamp": 1754994724.036656, "ratings": {"interest_rating": 3}, "sentence_texts": ["你还可以应用推理算法来解决基于这个框架的控制和规划问题。", "因为我们画了一个概率图模型， 其中推理对应于解决控制问题， 这意味着我们可以利用大量的推理方法来实际解决控制和规划问题， 这被证明是一个相当强大的思想。"], "combined_text": "你还可以应用推理算法来解决基于这个框架的控制和规划问题。\n\n因为我们画了一个概率图模型， 其中推理对应于解决控制问题， 这意味着我们可以利用大量的推理方法来实际解决控制和规划问题， 这被证明是一个相当强大的思想。"}, {"sentence_group_ids": [127, 128, 129], "representative_sentence_id": 127, "sentence_count": 3, "timestamp": 1754994754.3020687, "ratings": {"interest_rating": 4}, "sentence_texts": ["最后， 这为为什么即使确定性行为是可能的， 随机性行为也可能被偏好提供了解释。", "这对于探索和迁移学习等事情非常有用。", "它对探索和迁移学习有用的原因是， 如果你以多种不同的方式执行一个任务， 那么当环境变化， 任务需要以稍微不同的方式执行时， 你更有可能迁移到新的环境中。"], "combined_text": "最后， 这为为什么即使确定性行为是可能的， 随机性行为也可能被偏好提供了解释。\n\n这对于探索和迁移学习等事情非常有用。\n\n它对探索和迁移学习有用的原因是， 如果你以多种不同的方式执行一个任务， 那么当环境变化， 任务需要以稍微不同的方式执行时， 你更有可能迁移到新的环境中。"}, {"sentence_group_ids": [130, 131], "representative_sentence_id": 130, "sentence_count": 2, "timestamp": 1754994777.0919685, "ratings": {"interest_rating": 3}, "sentence_texts": ["在今天讲座的大部分时间里， 我们实际上将讨论如何执行这个推理问题。", "我们将看到， 将精确推理和近似推理应用到这个图模型中， 会得到与我们已经学过的强化学习算法非常相似的算法。"], "combined_text": "在今天讲座的大部分时间里， 我们实际上将讨论如何执行这个推理问题。\n\n我们将看到， 将精确推理和近似推理应用到这个图模型中， 会得到与我们已经学过的强化学习算法非常相似的算法。"}, {"sentence_group_ids": [132, 133, 134], "representative_sentence_id": 132, "sentence_count": 3, "timestamp": 1754994793.0988946, "ratings": {"interest_rating": 4}, "sentence_texts": ["--- 我们如何在这个模型中进行推理呢？", "我们需要了解三个操作。", "第一个我们需要知道的操作是如何计算“后向消息”（backward messages）。"], "combined_text": "--- 我们如何在这个模型中进行推理呢？\n\n我们需要了解三个操作。\n\n第一个我们需要知道的操作是如何计算“后向消息”（backward messages）。"}, {"sentence_group_ids": [135], "representative_sentence_id": 135, "sentence_count": 1, "timestamp": 1754994815.2160037, "ratings": {"interest_rating": 3}, "sentence_texts": ["如果你研究过隐马尔可夫模型（HMM）或卡尔曼滤波器， 或者听说过变量消除（variable elimination）， 那么你可能已经对如何在这个模型中进行推理有一些想法了。"], "combined_text": "如果你研究过隐马尔可夫模型（HMM）或卡尔曼滤波器， 或者听说过变量消除（variable elimination）， 那么你可能已经对如何在这个模型中进行推理有一些想法了。"}, {"sentence_group_ids": [136, 137], "representative_sentence_id": 136, "sentence_count": 2, "timestamp": 1754994838.8202462, "ratings": {"interest_rating": 2}, "sentence_texts": ["它是一个链式结构的动态贝叶斯网络， 这意味着它应该非常适合通过消息传递进行推理， 而消息传递当然是变量消除的一个特例。", "在这样的图中， 我们想计算两种消息， 非常像你在 HMM 或卡尔曼滤波器中会做的那样。"], "combined_text": "它是一个链式结构的动态贝叶斯网络， 这意味着它应该非常适合通过消息传递进行推理， 而消息传递当然是变量消除的一个特例。\n\n在这样的图中， 我们想计算两种消息， 非常像你在 HMM 或卡尔曼滤波器中会做的那样。"}, {"sentence_group_ids": [138, 139], "representative_sentence_id": 138, "sentence_count": 2, "timestamp": 1754994856.5395072, "ratings": {"interest_rating": 4}, "sentence_texts": ["第一种消息是后向消息， 它告诉你， 给定你当前所处的状态和动作， 从现在到轨迹结束一直保持最优的概率是多少。", "我们将这些后向消息称为 β。"], "combined_text": "第一种消息是后向消息， 它告诉你， 给定你当前所处的状态和动作， 从现在到轨迹结束一直保持最优的概率是多少。\n\n我们将这些后向消息称为 β。"}, {"sentence_group_ids": [140, 141], "representative_sentence_id": 140, "sentence_count": 2, "timestamp": 1754994874.2405474, "ratings": {"interest_rating": 4}, "sentence_texts": ["事实证明， 使用 β 你实际上可以恢复策略。", "策略是在时间步 t， 给定时间步 t 的状态以及整个轨迹从 1 到 T 都是最优的证据下， 一个动作的概率。"], "combined_text": "事实证明， 使用 β 你实际上可以恢复策略。\n\n策略是在时间步 t， 给定时间步 t 的状态以及整个轨迹从 1 到 T 都是最优的证据下， 一个动作的概率。"}, {"sentence_group_ids": [142, 143, 144], "representative_sentence_id": 142, "sentence_count": 3, "timestamp": 1754994901.9448893, "ratings": {"interest_rating": 4}, "sentence_texts": ["这是这个图模型中的随机最优策略。", "事实证明， 如果你能计算后向消息， 你就能计算策略。", "第三个非常有用的操作， 尤其是在我们处理逆强化学习时， 是计算所谓的“前向消息”（forward messages）。"], "combined_text": "这是这个图模型中的随机最优策略。\n\n事实证明， 如果你能计算后向消息， 你就能计算策略。\n\n第三个非常有用的操作， 尤其是在我们处理逆强化学习时， 是计算所谓的“前向消息”（forward messages）。"}, {"sentence_group_ids": [145, 146], "representative_sentence_id": 145, "sentence_count": 2, "timestamp": 1754994920.3333313, "ratings": {"interest_rating": 5}, "sentence_texts": ["前向消息有点像后向消息的反向模拟。", "一个前向消息说， 如果你在时间步 t-1 之前都是最优的， 那么你最终到达特定状态 st 的概率是多少。"], "combined_text": "前向消息有点像后向消息的反向模拟。\n\n一个前向消息说， 如果你在时间步 t-1 之前都是最优的， 那么你最终到达特定状态 st 的概率是多少。"}, {"sentence_group_ids": [147], "representative_sentence_id": 147, "sentence_count": 1, "timestamp": 1754994939.1587212, "ratings": {"interest_rating": 4}, "sentence_texts": ["如果我们将后向消息和前向消息放在一起， 我们实际上可以恢复状态占用率， 这在技术上对于恢复最优策略不是必需的， 但对于进行逆强化学习是必需的。"], "combined_text": "如果我们将后向消息和前向消息放在一起， 我们实际上可以恢复状态占用率， 这在技术上对于恢复最优策略不是必需的， 但对于进行逆强化学习是必需的。"}, {"sentence_group_ids": [148, 149], "representative_sentence_id": 148, "sentence_count": 2, "timestamp": 1754994958.9450314, "ratings": {"interest_rating": 4}, "sentence_texts": ["让我们从后向消息开始， 它们确实是最重要的， 因为如果你能计算这些， 你就能恢复近乎最优的策略。", "我们推导后向消息的方式只是通过一些递归、概率论和一点代数。"], "combined_text": "让我们从后向消息开始， 它们确实是最重要的， 因为如果你能计算这些， 你就能恢复近乎最优的策略。\n\n我们推导后向消息的方式只是通过一些递归、概率论和一点代数。"}, {"sentence_group_ids": [150, 151], "representative_sentence_id": 150, "sentence_count": 2, "timestamp": 1754994979.1777995, "ratings": {"interest_rating": 2}, "sentence_texts": ["首先， 我们可以取后向消息的这个方程， 然后插入下一个状态 st+1 并将其积分掉。", "所以后向消息等于在 st+1 的所有值上对 p(o_t."], "combined_text": "首先， 我们可以取后向消息的这个方程， 然后插入下一个状态 st+1 并将其积分掉。\n\n所以后向消息等于在 st+1 的所有值上对 p(o_t."}, {"sentence_group_ids": [152, 153, 154], "representative_sentence_id": 152, "sentence_count": 3, "timestamp": 1754995010.9161065, "ratings": {"interest_rating": 4}, "sentence_texts": ["T, st+1 | st, a) 的积分。", "现在我们要做的， 就是使用我们模型中的条件概率分布（CPD）来分解这个分布。", "我们做这个分解的目标是恢复一个递归表达式， 使得我们可以将 β_t(st, a) 表示为 β_t+1(st+1, at+1) 的某个函数。"], "combined_text": "T, st+1 | st, a) 的积分。\n\n现在我们要做的， 就是使用我们模型中的条件概率分布（CPD）来分解这个分布。\n\n我们做这个分解的目标是恢复一个递归表达式， 使得我们可以将 β_t(st, a) 表示为 β_t+1(st+1, at+1) 的某个函数。"}, {"sentence_group_ids": [155, 156], "representative_sentence_id": 155, "sentence_count": 2, "timestamp": 1754995027.6482177, "ratings": {"interest_rating": 4}, "sentence_texts": ["为了分解这个表达式， 我们必须注意到， 未来的最优性变量， 即 o_t+1.", "T， 在给定 st+1 的条件下， 与过去的一切都是独立的。"], "combined_text": "为了分解这个表达式， 我们必须注意到， 未来的最优性变量， 即 o_t+1.\n\nT， 在给定 st+1 的条件下， 与过去的一切都是独立的。"}, {"sentence_group_ids": [157, 158, 159], "representative_sentence_id": 157, "sentence_count": 3, "timestamp": 1754995046.59463, "ratings": {"interest_rating": 2}, "sentence_texts": ["我们可以通过检查图模型来看到这一点。", "这意味着我们可以将这个表达式分解为三个部分。", "第一部分是从 t+1 到结束的所有最优性变量给定 st+1 的概率。"], "combined_text": "我们可以通过检查图模型来看到这一点。\n\n这意味着我们可以将这个表达式分解为三个部分。\n\n第一部分是从 t+1 到结束的所有最优性变量给定 st+1 的概率。"}, {"sentence_group_ids": [160, 161], "representative_sentence_id": 160, "sentence_count": 2, "timestamp": 1754995072.5654242, "ratings": {"interest_rating": 1}, "sentence_texts": ["我们知道我们不必以 st 和 a 为条件， 因为给定 st+1， 所有未来的最优性变量都独立于 st 和 a。", "然后我们有 p(st+1 | st, a)， 这只是我们已经知道的转移概率。"], "combined_text": "我们知道我们不必以 st 和 a 为条件， 因为给定 st+1， 所有未来的最优性变量都独立于 st 和 a。\n\n然后我们有 p(st+1 | st, a)， 这只是我们已经知道的转移概率。"}, {"sentence_group_ids": [162], "representative_sentence_id": 162, "sentence_count": 1, "timestamp": 1754995086.744011, "ratings": {"interest_rating": 2}, "sentence_texts": ["然后我们有剩余的最优性变量 ot 给定 st 和 a 的概率， 这只对应于指数化的奖励， 因为那只是我们图模型中的一个 CPD。"], "combined_text": "然后我们有剩余的最优性变量 ot 给定 st 和 a 的概率， 这只对应于指数化的奖励， 因为那只是我们图模型中的一个 CPD。"}, {"sentence_group_ids": [163], "representative_sentence_id": 163, "sentence_count": 1, "timestamp": 1754995104.6272042, "ratings": {"interest_rating": 3}, "sentence_texts": ["所以我们知道这个东西， 我们知道那个东西， 那是我们的转移概率， 尽管当我们在做强化学习时， 我们可能想在不知道这个概率的函数知识的情况下计算后向消息。"], "combined_text": "所以我们知道这个东西， 我们知道那个东西， 那是我们的转移概率， 尽管当我们在做强化学习时， 我们可能想在不知道这个概率的函数知识的情况下计算后向消息。"}, {"sentence_group_ids": [164, 165, 166], "representative_sentence_id": 164, "sentence_count": 3, "timestamp": 1754995127.824491, "ratings": {"interest_rating": 2}, "sentence_texts": ["现在我们假设我们知道它。", "这只剩下最后一项了。", "从 t+1 到 T 的最优性概率， 给定 st+1， 可以写成从 t+1 到 T 的最优性概率， 给定 st+1 和 at+1， 乘以 at+1 给定 st+1 的概率。"], "combined_text": "现在我们假设我们知道它。\n\n这只剩下最后一项了。\n\n从 t+1 到 T 的最优性概率， 给定 st+1， 可以写成从 t+1 到 T 的最优性概率， 给定 st+1 和 at+1， 乘以 at+1 给定 st+1 的概率。"}, {"sentence_group_ids": [167, 168], "representative_sentence_id": 167, "sentence_count": 2, "timestamp": 1754995146.2210512, "ratings": {"interest_rating": 3}, "sentence_texts": ["这一部分只是时间步 t+1 的后向消息。", "我们已经得到了一些改进， 我们得到了一个基本上是递归的表达式， 除了它有一个我们还没有定义的奇怪项：一个动作给定一个状态的概率。"], "combined_text": "这一部分只是时间步 t+1 的后向消息。\n\n我们已经得到了一些改进， 我们得到了一个基本上是递归的表达式， 除了它有一个我们还没有定义的奇怪项：一个动作给定一个状态的概率。"}, {"sentence_group_ids": [169, 170], "representative_sentence_id": 169, "sentence_count": 2, "timestamp": 1754995167.683698, "ratings": {"interest_rating": 1}, "sentence_texts": ["关键是， 这不是一个策略。", "这是在说， 先验地哪些动作是可能的， 也就是说， 如果你不知道你是否最优， 你采取某个特定动作的可能性有多大。"], "combined_text": "关键是， 这不是一个策略。\n\n这是在说， 先验地哪些动作是可能的， 也就是说， 如果你不知道你是否最优， 你采取某个特定动作的可能性有多大。"}, {"sentence_group_ids": [171, 172], "representative_sentence_id": 171, "sentence_count": 2, "timestamp": 1754995190.8131866, "ratings": {"interest_rating": 3}, "sentence_texts": ["通常， 如果你不知道你是否正在努力做到最优， 你可能对哪些动作更可能或更不可能一无所知。", "所以我们可以定义这一项， 我们可以定义一个动作先验 p(a|s)， 但我们现在假设它是均匀的。"], "combined_text": "通常， 如果你不知道你是否正在努力做到最优， 你可能对哪些动作更可能或更不可能一无所知。\n\n所以我们可以定义这一项， 我们可以定义一个动作先验 p(a|s)， 但我们现在假设它是均匀的。"}, {"sentence_group_ids": [173, 174, 175], "representative_sentence_id": 173, "sentence_count": 3, "timestamp": 1754995219.8606317, "ratings": {"interest_rating": 1}, "sentence_texts": ["这个假设有几个合理的原因。", "首先， 如果你对猴子想做什么一无所知， 那么你可能无法说出它更可能或更不可能执行哪些动作。", "其次， 也许更具数学意义的是， 如果你想施加一个动作先验， 事实证明你可以等价地修改奖励函数， 并保持一个均匀的动作先验， 从而得到完全相同的解。"], "combined_text": "这个假设有几个合理的原因。\n\n首先， 如果你对猴子想做什么一无所知， 那么你可能无法说出它更可能或更不可能执行哪些动作。\n\n其次， 也许更具数学意义的是， 如果你想施加一个动作先验， 事实证明你可以等价地修改奖励函数， 并保持一个均匀的动作先验， 从而得到完全相同的解。"}]