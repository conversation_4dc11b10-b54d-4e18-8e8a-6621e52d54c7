CS 285_ Lecture 20: Inverse Reinforcement Learning (Parts 1-4)

### Part 1

欢迎来到 CS285 的第 20 讲。今天我们将讨论逆强化学习。

到目前为止，每次我们遇到强化学习问题时，我们都会假设系统会为我们提供一个奖励函数。通常，如果你要使用这些强化学习算法，你会手动编写一个奖励函数。但如果你的任务的奖励函数很难手动指定，但你可以访问人类的数据，或者一般来说，某个专家成功地完成了这项任务，你可以通过观察他们的行为来推导出他们的奖励函数，然后用强化学习算法重新优化这个奖励函数。

今天我们要学习的是如何应用上次形式化为推理问题的近似最优模型来学习奖励函数，而不是直接从已知的奖励中学习策略，这就是所谓的逆强化学习问题。所以今天的目标是理解逆强化学习问题的定义，理解如何使用行为概率模型来推导逆强化学习算法，并理解一些我们可以在高维空间中使用的实用逆强化学习方法，我们在深度强化学习中遇到的这类问题。

我在上一讲中提到的一件事是，最优控制和强化学习可以作为人类行为的模型。事实上，科学家们试图通过最优决策和理性的视角来研究人类运动、人类决策和人类行为，这已经有很长的历史了。事实上，理性行为的定义之一是，理性行为可以被定义为最大化一个定义明确的效用函数。事实证明，任何理性的决策策略，例如，如果你更喜欢a而不是b，你更喜欢b而不是c，那么你更喜欢a而不是c。任何在某种意义上是理性的策略都可以用一组定义明确的标量效用集来解释。而非理性的策略，例如，如果你更喜欢苹果而不是香蕉，你更喜欢香蕉而不是橙子，但你更喜欢橙子而不是苹果，那是非理性的，实际上无法用一组定义明确的标量效用集来解释。

所以，如果我们想通过最优性的视角来解释人类运动、人类决策等等，我们可以做的是，写下描述最优决策的方程，无论是确定性情况下的最优决策，就像我们在最优控制课程中学到的那样，还是随机情况下的最优决策。然后，我们可以假设人类正在解决这个优化问题，我们可以用什么来代替奖励函数r，以便这个优化问题的解与人类实际表现出的行为相匹配？事实上，神经科学、运动控制、心理学等领域的研究已经应用了这个基本模型。

虽然正如我们上周讨论的那样，经典的最优模型有时不太适合人类的决策，因为人类通常不是确定性的，也不是完全最优的。但在许多情况下，软最优模型可以很好地解释人类行为。事实上，最优性是思考人类决策和运动控制的良好框架，这一概念在人类行为和神经科学研究中具有极大的影响力。

好吧，这或许是一种更理论的动机，但我们也可以问一个实际的问题，为什么我们要担心学习奖励函数呢？我们可以采取的一个视角是模仿学习的视角。因此，正如我们在课程开始时讨论的那样，解决模仿学习问题的标准方法是向机器人或其他任何智能体（比如自动驾驶汽车、电子商务智能体）展示你想要的行为，让它通过行为克隆模仿这种行为。然而，当人类模仿其他人类时，我们实际上并不是这样做的。

想象一下，通过模仿学习来教一个机器人，你也许会远程操作机器人，让它的手臂按照你想要的动作移动。但当你想到一个人模仿另一个人时，你并不需要有人抱着你，用你完成任务所需的方式移动你的身体。不，这不是你要做的。你观察别人，弄清楚他们想要做什么，然后你试图模仿的不是他们的直接动作，而是他们的意图。

所以，标准的模仿学习处理的是复制专家执行的操作，而不去推理这些操作的目的，也不去推理它们的结果。人类的模仿学习则截然不同。当人类模仿时，我们复制专家的意图，我们可能会做他们做的事情，但方式会有所不同，因为我们理解他们为什么采取行动，以及他们寻求的结果会是什么。这可能会导致与专家采取的行动截然不同，但结果是相同的。这里有一个很棒的视频，我认为它说明了这一点。这是一个心理学实验。心理学实验的对象是画面右下角的孩子。现在，如果你扮演这个角色，想象一下当你看到这个画面时你会怎么做。你会推断出实验者的意图。你不会执行实验者正在执行的操作，而是会执行导致期望结果的操作，你推断的结果就是他们想要的结果。所以我们能否弄清楚如何让强化学习智能体做到这样的事情？

我们可以从另一个角度来思考为什么逆向强化学习很重要。在我们想要解决的许多强化任务中，比如你们在家庭作业3中要玩的游戏，目标是相当自然的。所以如果你想尽可能地玩游戏，那么你的奖励函数将是游戏中的分数，这是有道理的。分数就印在图像上，所以它不是说这是我的奖励函数有点牵强，但在许多其他场景中，奖励函数不那么明显。想象一下，一辆自动驾驶汽车在高速公路上行驶，它必须平衡许多不同的竞争因素，它必须以特定的速度到达目的地，不能违反交通法规，不能骚扰其他司机，所有这些不同的因素都必须相互平衡，才能安全、舒适地驾驶，让乘客感到舒适。写出一个方程来描述这一点可能非常困难，但让专业司机演示一下就相对容易得多，所以在这种情况下学习奖励函数是非常有吸引力的。

逆强化学习指的是从演示中推断奖励函数的问题。比如在这个驾驶场景中，你让专业司机演示了一个好的驾驶策略，然后你想找出一个好的奖励函数，从中提取出来，交给你的强化学习智能体。

正如我所说，逆强化学习本身是一个非常不明确的问题，其原因是，对于任何给定的行为模式，实际上有无数种不同的奖励函数可以解释这种行为，这也许是最明显的。举个例子，让我们考虑一个非常简单的网格世界，有16个状态。如果我有这个演示，我问你执行这个演示的智能体的奖励函数是什么，你会怎么回答？你可能会反对，你可能会说，这到底是怎么回事？在自动驾驶场景中，语义要丰富得多，还有其他车辆、停车标志和交通信号灯。但请记住，算法并不具备所有这些语义。就像在探索课程中我们讨论蒙特祖玛的复仇时那样，探索之所以困难，是因为我们缺乏基础。我们有语义，可以让我们理解世界，但算法缺乏这些语义。同样，在逆强化学习的情况下，算法也只是处理状态和动作。它无法理解有意义的奖励函数与交通法规有关，而不是与特定的GPS坐标有关。

所以我想给你们看这个例子，因为我想要构建一个场景，在这个场景中，我们故意将恢复奖励的问题与我们之前的任何语义知识分开。好的，那么对于这个网格世界，思考一下奖励函数可能是什么。猜测一下，一个非常合理的猜测是，智能体到达这个特定方格时会获得丰厚的奖励，而到达其他地方时则会获得糟糕的奖励。这肯定可以解释他们为什么这么做。但还有另一种解释，如果他们到达这个方格时会获得丰厚的奖励，如果你只观察到一条由四步组成的轨迹，这两种奖励都能同样很好地解释专家的行为。如果他们有这样的奖励函数，下半部分的任何内容都会获得丰厚的奖励，而穿过那些较暗的方格则会受到很大的惩罚，这也可以解释他们的行为。事实上，他们的行为甚至可以用一个奖励函数来解释，这个函数只是说，对于采取除演示中的动作之外的任何动作，你都会获得负无穷大的奖励。所以，一般来说，存在无限多的奖励，对于这些奖励，在传统意义上观察到的行为是最优的。

好的，在我们讨论如何澄清这个歧义之前，让我们更正式地定义逆强化学习问题。逆强化学习，更正式地说，我们可以这样做：我将在幻灯片左侧展示常规前向强化学习的形式，在右侧展示逆强化学习的形式，以便您可以并排查看和比较它们。首先，在前向强化学习和逆强化学习中，我们给出了什么？在这两种情况下，我们都有一个状态空间和一个动作空间。有时，我们会给出转移概率，有时则没有，有时我们必须根据经验推断它们。在前向强化学习中，我们给出了一个奖励函数，我们的目标是学习该奖励函数的最优策略π*。在逆强化学习中，我们给出了通过运行最优策略采样的轨迹τ。我们不一定知道最优策略是什么，但我们假设我们的样本轨迹来自该策略或其近似值。我们的目标是学习π*优化后的奖励函数R_psi，以产生这些τ，其中psi是一个参数向量，它参数化了奖励。

现在，我们可以为奖励做出许多不同的选择。在更经典的逆强化学习文献中，一种非常常见的选择是使用线性奖励函数。奖励函数是特征的加权组合。您可以等效地将其写成内积 psi转置 乘以粗体f，其中粗体f是特征向量。您可以直观地将这些特征视为智能体想要或不想要的一系列事物，然后您要确定的是，它们究竟想要或不想要多少这些东西。如今，在深度强化学习领域，我们可能还想处理神经网络奖励函数。奖励函数通过深度神经网络将状态和动作映射到标量值奖励，并通过某个参数向量psi进行参数化，psi表示该神经网络的参数。一旦我们恢复了奖励函数和逆强化学习，通常我们想要做的就是使用该奖励函数来学习相应的最优策略π*。

好的，所以在我谈论今天讲座的主题之前，这将是基于我们在上一讲中看到的概率模型，我想提供一些历史背景，来讨论一下在现代深度学习时代之前人们思考解决逆强化学习问题的一些方法。许多以前的逆强化学习算法都集中在一个叫做特征匹配的东西上。今天我要讨论的主要算法是基于最大熵原理，并借鉴了我在上一讲中提出的图形模型。这与特征匹配不同，但是我将首先描述特征匹配算法，只是为了提供一些背景信息，并让你们对相关文献有一个更广泛的概述。

经典地，当人们开始思考逆强化学习问题时，他们是这样处理的：假设我们有一些特征，我们将学习一个在这些特征上是线性的奖励函数。如果特征f很重要，那么我们解决逆强化学习问题的方法是说，让我们学习一个奖励函数，对于该函数，最优策略对这些特征具有相同的期望值。因此，这些特征是状态和动作的函数，你可以说，让π_R_psi成为我们学习到的奖励R_psi的最优策略，然后我们将选择psi，使得我们的特征向量在π_R_psi下的期望值等于其在π*下的期望值。不，这非常合理，这只是说，嗯，如果你看到，假设最佳驾驶员驾驶汽车很少遇到撞车事故，很少闯红灯，并且经常在左侧超车，然后匹配这些特征的期望值，如果你现在获得了正确的特征，可能会产生类似的行为。

不幸的是，这个公式仍然模棱两可，所以你可以相当轻松地做到这一点，因为你有来自最佳策略的轨迹样本，所以虽然你不知道最佳策略本身，但你可以通过平均你演示的轨迹中的特征向量来近似右侧，但它仍然是模棱两可的，因为多个不同的psi向量仍然可以导致相同的特征期望。所以回想一下我之前给出的网格世界的示例，所有这些不同的奖励函数都会导致相同的策略，这意味着它们都具有相同的期望值。所以，人们想到的一种进一步消除歧义的方法是使用最大边际原则。因此逆RL的最大边际原则与支持向量机的最大边际原则非常相似，它指出你应该选择psi，以便最大化观察到的策略π*与所有其他策略之间的边际。

因此，如果奖励是psi转置f，那么预期奖励就是psi转置乘以f的期望值。你会选择psi，这样psi转置乘以f的期望值，这意味着π*下的预期奖励大于或等于任何其他策略下的预期奖励加上最大可能的边
际，你会选择边际和psi以最大化这个值。这基本上是在说，找出一个权重向量psi，这样专家的策略就比其他所有策略好出最大可能的边际。

现在这有点像启发式方法，因为这并不一定意味着你会恢复专家的权重向量，即专家的真实奖励函数，但这是一个合理的启发式方法。它说的是，你知道如果你有两个不同的奖励，它们具有相同的特征期望，那么专家会选择一个让专家看起来比其他所有策略都更好的策略，所以不要选择一个专家策略比其他策略略好一点点的奖励。

现在，这个公式的问题仍然在于，如果策略空间非常大且连续，那么很可能存在其他与专家策略非常相似的策略。事实上，很可能存在其他几乎完全相同的策略。因此，仅仅最大化所有策略的边距本身可能不是一个好主D意。也许你想用π和π*之间的相似性来加权边际。也许你想要的是最大化与专家策略差异更大的策略的边距，而与其他与专家策略非常相似的策略的边距可能非常小。

幸运的是，这又与我们在支持向量机中遇到的问题非常相似。现实生活中，关于特征匹配的许多文献实际上都借鉴了支持向量机的技术来解决这个问题。所以，对于熟悉支持向量机的人来说，你可能会认出我们。如果你不熟悉支持向量机，也不要太担心，你真的不需要知道这一点。但这是一个很好的补充说明，让你了解相关文献。SVM技巧基本上是针对像这样的最大边际问题（通常很难解决）并将其重新表述为最小化权重向量长度的问题，其中边际始终为1。为什么可以这样做有点微妙，它需要一点拉格朗日对偶，但你可以相信我的话，这两个问题是等价的。事实证明，如果你想将策略之间的相似性纳入第二个问题，你所做的就是用策略之间某种程度的散度来替换它。这意味着，如果你有另一个与π*相同的策略π，那么左侧和右侧可以相等，因为d将为零。但是随着策略变得越来越不同，你需要增加这些策略的边距。因此，d的一个好选择可能是它们的特征期望之间的差异。另一个好选择可能是它们的预期KL散度。

现在，这个公式仍然存在一些问题，它确实会导致一些我们可以实际实现并尝试使用的逆强化学习算法，但这些逆强化学习算法会有一些缺点。一个主要缺点是最大化边际有点武断。它的基本意思是，你应该找到一个奖励函数，在这个函数中，专家的策略不仅仅是比其他选择好一点点，你不想只找到一个奖励函数，这个奖励函数与专家的一些非常不同的策略捆绑在一起，你想找到一个奖励函数，在这个奖励函数中，专家的行为显然是更好的选择。但这并没有说明你为什么要这么做。你这么做的原因大概是因为你对专家做了一些假设。也许你隐含地假设专家会故意展示一些容易计算出回报的东西，但最大化边际的概念是对此的启发式回应，而关于专家行为的假设实际上并没有在这里明确提出。

另一个问题是，这个公式并没有真正给我们提供一个清晰的专家次优模型。它没有解释为什么专家有时会做出实际上并非最优的事情。现在，熟悉支持向量机的人可能会记得，在类别不是完全可分离的情况下，你可以做一些事情，比如添加松弛变量来解释某种程度的次优性。但在设置中添加这样的松弛变量在很大程度上仍然是一种启发式方法。它并不是专家行为的清晰模型。它只是启发式地修改问题，使其能够适应次优专家。最后，这导致了某种混乱的约束优化问题，如果你有线性参数化的奖励函数，这不是什么大问题，但如果你想要用神经网络来表示奖励函数，那么对于深度学习来说，这确实是一个大问题。

然而，如果你想了解更多关于这类方法的知识，我建议你阅读一些资料，例如Abbeel和Ng的一篇经典论文，名为《通过逆强化学习进行学徒学习》（Apprenticeship Learning via Inverse Reinforcement Learning），以及Ratliff的一篇论文，名为《最大边际规划》（Maximum Margin Planning），它们非常具有代表性，代表了这类特征匹配和边际最大化逆强化学习方法。

然而，今天讨论的主题实际上是建立在专家行为的概率模型上。所以从上一节课中我们看到，我们实际上可以将次优行为建模为特定图形模型中的推断，即其状态、动作和这些额外的最优性变量。因此该模型中的概率分布是s1的初始状态分布p，st+1的转移概率p（给定st, at），以及我们选择等于奖励指数的最优性变量的概率。

现在在我们之前，我们关心的是这个问题：假设专家采取最优行为，那么轨迹的概率是多少？我们之前说过，如果不假设最优性，那么任何物理上一致的轨迹都具有相同的概率。但是，如果你假设了最优行为，那么你可以说，假设专家是最优的，那么轨迹的概率是多少？我们发现，这有一个很好的解释，即最优轨迹是最有可能的，而次优轨迹的可能性则呈指数级下降。我们讨论了这可能是次优专家或随机行为的一个很好的模型。但是现在我们要做的实际上是使用这个模型来学习奖励函数，所以我们不是问在给定奖励的情况下轨迹的概率是多少（在这个模型中是推理），而是在这个模型中进行学习。我们说，给定轨迹，我们能否学习r的参数，以便最大化在这个图模型下这些轨迹的概率？这就是我将在下一节课中讨论的内容。

### Part 2

好的，所以在图形模型中学习奖励函数相当于学习最优性变量的概率分布。所以现在给定st, at时ot的概率p仍然等于奖励的指数，但现在它是R_psi，它是通过参数psi参数化的奖励。我们的目标是找到奖励参数，所以为了清楚起见，我将其写为p(ot | st, at, psi)，以强调这个条件概率分布取决于我们的参数psi。给定最优性的轨迹概率，其形式与之前一样，与轨迹的物理概率乘以奖励总和的指数成正比。在逆RL设置中，我们从这个未知的最优策略中获得了样本，我们学习奖励的方式是通过最大似然学习。我们基本上会选择参数psi，它将最大化我们观察到的轨迹的对数概率。所以这非常类似于任何其他机器学习设置中的最大似然。

现在当我们这样做时，事实证明我们实际上可以忽略p(τ)项，因为它与psi无关。所以执行最大似然优化的主要挑战实际上是指数奖励部分。所以如果我们代入这个表示轨迹对数概率的表达式，我们会得到以下非常直观的表达式：我们希望最大化关于psi的函数，即所有轨迹的平均奖励（也就是沿着轨迹τi的奖励总和）减去对数正则化器。

现在，如果你忽略对数正则化器，这看起来既直观又有点傻，这只是说找到使轨迹具有高奖励的奖励函数。但问题是，如果你为所有轨迹分配巨大的奖励，那么其他一些未被采用且在专家策略中概率很低的轨迹可能会获得更高的奖励。这就是对数正则化器要处理的，对数Z项。正则化常数表示你不能为任何东西分配高奖励，你需要分配奖励，使你看到的轨迹看起来比你没有看到的其他轨迹更有可能。

实际上，正是对数正则化器使得逆强化学习变得困难。所以让我们来谈谈对数正则化器或配分函数，也就是Z，有时被称为配分函数。Z等于对所有可能轨迹p(τ)乘以exp(R_psi(τ))的积分。现在当然，我们可以立即说，好吧，让我们代入Z的这个方程，取其梯度并进行优化，这是非常合理的事情，但当然，对所有可能的轨迹进行积分通常是难以处理的。

所以如果你代入Z的方程，然后对psi求导，你会得到这个表达式：你得到 1/n 乘以所有样本轨迹奖励梯度之和，即∇R(τi)，减去 1/z 乘以对p(τ)exp(R_psi(τ))∇R(τ)的积分。但你可能会注意到，第二项实际上可以看作是在由你当前的psi引导的分布下的期望值，对吗？因为这个表达式，1/z 乘以p(τ)乘以exp(R_psi(τ))，恰好是p(τ | O_1:T, psi)，也就是给定最优性和psi的轨迹概率。所以您可以等效地将梯度写为专家策略π*下∇R的期望值，减去p(τ | O_1:T, psi)下∇R的期望值。第一项用样本近似，然后变成所有轨迹τi上∇R之和除以n，第二项变成那个积分。

所以这是一个非常有吸引力的解释，您的梯度只是专家策略下的梯度期望值与当前奖励下的梯度期望值之间的差值。请注意，p(τ | O_1:T, psi)只是我们轨迹的分布，这些轨迹相对于我们的psi是软最优的。所以这可能立即暗示一种有吸引力的算法方法：采用您当前的奖励R_psi，通过在我们周一看到的图形模型中运行推理来找到软最优策略，我们从该策略中抽取轨迹样本，然后进行这种对比操作，我们增加从专家那里看到的轨迹的奖励，同时减少我们为当前奖励采样的轨迹的奖励。所以我们用来自专家的样本估计第一项，第二项来自当前奖励下的软最优策略。我们可以使用周一讲座中学到的算法来计算软最优策略。

但让我们实际研究一下如何估计这个期望，因为我认为这会让我们对最大熵IRL在现实生活中的方法有一点了解。所以如果我们采用第二项，让我更明确地说明一下，它实际上是沿着该轨迹所有时间步骤的奖励总和相对于psi的梯度。这意味着我们可以通过将总和通过期望的线性移到期望之外来写它，并将其写为从t=1到T的总和，在给定最优性的状态-动作边际分布下，对∇R(st, at)的期望。

所以我们取期望值的分布是给定最优性时动作的概率乘以给定最优性时状态的概率。周一我们学习了如何计算这两个量。因为第一个量是策略，我们学习的是两个后向消息的比率。第二个项是状态边际，我们在周一学习了它可以作为前向和后向消息的乘积。对吧，这就是我们之前看到过的地方。所以第一个项等于β(st, at)除以β(st)的比率，如果你不记得如何计算β，请回到周一的讲座并重新观看第一个推理问题。第二个项与α(st)乘以β(st)成正比，如果你不记得我们如何推导这个，请回到周一的讲座并观看第三个推理问题。

现在这里第一个表达式分母中的β(st)与第二个表达式分子中的β(st)抵消，我们只需得到这个量与β(st, at)乘以α(st)成比例的答案。然后你必须在状态和动作上对其进行归一化，而不是在轨迹上，这一点至关重要。所以如果你的状态空间相对较小，那么归一化会更容易处理。

好的，现在我们可以估算第二个项的方法是，首先计算这个量，我称之为μ_t，即状态-动作边际量。我们通过将后向消息乘以前向消息，并对整体状态和动作进行归一化来计算它。然后，我们可以将这个期望表示为离散空间中的和，或者连续空间中的积分，即μ(st, at)乘以∇R(st, at)。我们也可以将其写成概率向量μ和每个状态-动作元组的奖励导数向量之间的内积。

好的，这是梯度的一个非常优雅的表达式，它需要我们真正能够计算μ，这意味着我们需要一个小的可数状态空间，通常离散状态空间是最好的。当然，我们需要知道转移概率才能真正计算出这些前向和后向消息，所以这不适用于未知的动态，也不适用于大型或连续的状态-动作空间。但对于小型和离散的空间，这是非常可行的。

所以这引出了经典的最大熵逆强化学习算法，正如Brian Ziebart在其2008年的论文中提出的：给定你当前的向量psi，按照上一讲的描述计算你的后向消息，按照上一讲的描述计算你的前向消息，然后通过将这些消息相乘并重新归一化来计算你的μ，然后评估轨迹似然的梯度，即所有演示轨迹的平均∇R_psi减去μ和∇R之间的内积。然后简单地对psi进行梯度上升，因为你刚刚计算了似然的梯度，然后重复此过程直到收敛。所以这基本上是一种计算演示轨迹似然梯度的算法，在收敛时，这将产生最大化这些轨迹的可能性的奖励函数，从而产生最能解释专家潜在次优行为的奖励。

至关重要的是，这个公式消除了我们之前看到的许多歧义。它消除歧义的方式是利用次优性的概念。它表明，实际上，不同的奖励拥有非常相似的策略的情况已经不再存在了。如果奖励更高，专家就会更具确定性。它本质上是利用专家的随机性来消除逆强化学习问题的歧愈。这似乎非常直观。如果你看到专家做一些非常随机的事情，这可能意味着他们并不关心这些不同的随机结果。这可能意味着，尽管所有这些结果对专家来说都差不多好，但如果你看到专家反复做一些非常具体的事情，你可能会说，这件事对专家来说真的很重要，因此它的奖励要大得多。

为什么我们称之为最大熵算法呢？在奖励与参数向量psi呈线性关系的情况下，我们实际上可以证明，该算法还可以优化一个约束优化问题，即最大化学习策略的熵，同时策略必须符合专家的特征期望。所以这种算法和我们之前看到的特征匹配方法之间有着很深的联系。它们消除歧义和特征匹配的方式是，你必须匹配特征，但除此之外，你还应该尽可能随机，这就是最大熵原理。它说你不应该做任何推断，除了数据支持的推断之外，它是奥卡姆剃刀的一种统计形式化。这也许可以部分解释为什么这种逆强化学习方法如此有效，因为它避免了做出无根据的假设，避免了对专家行为做出数据不支持的推断，而最大熵原理就是让你能够做到这一点的原因。

所以这种最大熵逆强化学习算法在许多较小的离散设置中得到了相当有效的应用。例如，Brian Ziebart关于这个主题的原始论文表明，你可以使用这种算法来推断地图中的导航路线。例如，你可以收集匹兹堡出租车司机的数据，推断他们的奖励函数，看看他们是喜欢在城市街道还是高速公路上行驶，然后使用路线规划软件像出租车司机一样进行导航。这种方法通常效果很好，但是这种方法仍然局限于状态空间相对较小且离散的设置。在下一节课中，我们将讨论如何将其扩展到状态空间可能非常大或连续的设定中。

### Part 3

好的，在下一节课中，我们将讨论如何在高维或连续空间中进行近似逆强化学习。那么，到目前为止，我们讨论的方法中缺少什么呢？到目前为止，最大熵逆强化学习需要一些在大型现实问题设置中难以获得的东西。它需要在内循环中求解软最优策略，以便计算那些前向和后向消息。它需要枚举所有状态-动作元组，以便规范化访问频率并计算梯度。将其应用于实际问题设置时，我们愿意处理这样一个事实：我们可能拥有庞大而连续的状态-动作空间，这使得这两件事都变得困难。状态可能只能通过采样来获得，这使得枚举所有状态-动作元组变得不可能。我们可能有未知的动态，这使得计算前向和后向消息的简单方法变得不可行。

所以，正如我到目前为止讨论的那样，最大熵IRL算法在现实环境中并不完全实用，我们需要提出易于处理的近似值，以便处理这种我们在深度强化学习中经常遇到的高维和连续的问题。

那么，为了能够在未知动态和较大状态或动作空间的情况下进行逆强化学习，我们可以做些什么呢？首先，我们假设我们不知道动态，但我们可以像在标准模型自由强化学习中那样进行采样。回想一下，似然函数的梯度是两个期望值的差：专家轨迹样本的梯度奖励期望值，减去从当前奖励函数的最优策略采样的轨迹的奖励梯度期望值。您可以使用专家提供的轨迹样本轻松估算第一项，因此，最大的挑战实际上是第二项，它需要在当前奖励下采用软最优策略。

因此，我们可以探索的一个想法是，让我们尝试学习软最优策略p(at | st, O_1:T, psi)，使用任何最大熵RL算法，基本上是周一讲座中的任何算法，例如软Q学习或熵正则化策略梯度。因此基本上任何可以最大化此目标的算法，然后运行该策略用于对轨迹τj进行采样。然后我们从专家那里获取轨迹τi，并使用这些轨迹来估计第一个期望值，然后使用来自此最大熵最优策略的轨迹τj来估计第二项。

现在，这实际上是一种在现实生活中执行近似梯度上升的可行方法，但是，它需要对奖励函数上的每个梯度步骤都运行最大熵RL，相应的正向问题才能收敛，这实际上非常困难。所以这是一个难以处理的过程：使用任何最大熵RL算法学习策略，然后运行策略对轨迹进行采样。第一个求和是对专家样本进行的，第二个求和是对策略样本进行的。

如果我们采用某种惰性策略优化程序，而不是在每次采取梯度步骤时都优化最大熵最优策略以使其收敛，如果我们在每次采取梯度步骤时只对其进行一点点优化呢？那么，我们不是学习p(at | st, O_1:T, psi)，而是从上一个psi得到的策略开始改进，也许我们可以稍微改进一下，哪怕只是一步梯度下降。现在的问题是我们的估计器有偏差，我们的分布是错误的。所以我们可以采用一个重要性采样校正，我们基本上可以说我们想要最优策略的样本，但我们得到的是次优策略的样本。基本上我们没有训练p(at | st, O_1:T, psi)直到收敛，但也许我们可以对这些样本进行重要性调整，使它们看起来像是来自最优策略的样本。

所以我们不再使用幻灯片顶部的等式，而是在第二项中引入一些重要性权重wj，我们将用wj加权每个样本，然后用wjs的和进行归一化，这样权重总和为1。如果我们这样做，我们就可以校正由于没有完全优化策略而产生的偏差。事实证明，重要性权重实际上具有非常吸引人的形式，因为我们知道，在归一化常数范围内，最优策略的轨迹概率由p(τ)乘以奖励的指数给出。我们知道奖励，不是真正的奖励，而是我们当前参数化的奖励。所以我们实际上可以计算这些重要性权重，前提是我们能够访问我们刚刚优化的策略来计算分母，我们通常这样做。重要性权重我们可以像这样写出来，这与我们之前在重要性加权策略梯度讲座中所讲的非常相似。我们有初始状态项，动态项，在顶部我们有额外的奖励项，在底部我们有额外的策略项。所以未知项全部抵消，我们只剩下指数的比率，即当前奖励函数下该轨迹的总奖励除以所有动作概率的乘积。由于你刚刚学习了策略π，你通常会知道它的概率，你知道在连续空间中它可能类似于高斯概率，所以你可以计算这些重要性权重。

现在至关重要的是，相对于我们的psi的每次策略更新都会让我们越来越接近目标分布，所以我们期望我们越优化策略，这些重要性权重就会越接近1。这很有吸引力。即使策略没有完全优化，我们也可以采取梯度下降的方法。我们对策略的优化程度越高，重要性权重的方差就越低。这个想法是Chelsea Finn等人提出的引导成本学习（Guided Cost Learning）算法的基础，这是第一个可以扩展到高维动作和状态空间的深度逆强化学习算法。

该算法的设计如下：你对策略π有一个初始猜测，可能是随机的，并且你有一些人类演示。那么你可以从当前策略中采样以生成策略样本，然后你将使用这些策略样本和人类演示来更新奖励，本质上是使用你的样本和演示来计算梯度，然后使用更新后的奖励来更新策略。所以最终这将产生一个奖励函数和一个策略。如果策略经过优化以达到收敛，那么该策略实际上就是该奖励函数的一个良好策略，并且该奖励函数有望很好地解释专家行为。奖励函数的更新将基于我在上一张幻灯片中展示的梯度，策略更新将基于最大熵学习框架。所以这是奖励梯度的表达式，策略梯度将是常规策略梯度加上额外的熵项，而重要性权重由奖励的指数除以实际策略概率的比率给出。

所以在原始论文中，Chelsea Finn所做的是，她实际上用一个真正的机器人收集了演示，展示了如何将食物倒入杯子中。然后学习到的策略必须使用视觉找到杯子，然后将食物倒入杯子中，无论杯子位于何处。所以我必须弄清楚任务的目的，即执行倒水操作。

### Part 4

在今天讲座的最后一部分，我们将讨论这些近似逆强化学习方法与另一类算法——也学习分布，称为生成对抗网络（GANs）——之间的更深层次的关系。我们将发现，探索这种联系实际上可以让我们更清楚地了解设计现实生活中的模仿学习算法的其他方式。

有些人可能已经意识到，我在讲座上一部分描述的算法结构有点像一个博弈。我们有一个初始策略，我们根据该策略生成样本。我们有人类的演示，我们从人类的演示中获取样本。我们将这些样本组合起来，产生某种奖励函数，使人类的演示看起来不错，而策略样本看起来很糟糕。然后我们修改策略，使策略实际上优化了该奖励函数，使其更难与演示区分开来。因此，您可以将其视为奖励函数试图根据当前奖励使人类演示看起来与策略样本截然不同，因为它试图给予人类演示高奖励，而给予策略样本较低的奖励。反过来，策略试图做出相反的事情，试图让它的样本看起来符合奖励，理想情况下和人类样本一样好。所以你几乎可以把它看作是策略和奖励函数之间的一种博弈，策略试图欺骗奖励函数，让它认为自己和人类一样好，而奖励函数则试图找到一个奖励，让它能够区分人类和策略。

事实上，这种联系不仅仅是表面的，逆强化学习和博弈之间的联系可以被正式化，逆强化学习可以与所谓的生成对抗网络联系起来。所以生成对抗网络有很多用途，它是一种将马变成斑马的方法，一种制作非常逼真的人脸的方法，一种将线条画变成猫的方法，但它实际上是一种生成建模的方法。它是一种学习神经网络的方法，可以捕捉特定的给定数据分布，比如逼真的人脸、逼真的帽子或逼真的斑马的分布。

你们当中可能有人不熟悉生成对抗网络，它们由两个神经网络组成：一个生成器网络，它接收随机噪声z，并将其转换为样本x，理想情况下，样本x应该与数据分布相似。所以，如果你在人脸上训练这个系统，样本x应该看起来像真实的人脸。数据由来自真实未知分布p*(x)的样本组成。还有第二个网络，称为判别器，它是一个二元分类器，它尝试将数据中的所有样本标记为“真”，将生成器的所有样本标记为“假”。所以这里的D_psi(x)是代表判别器的二元分类器，基本上是这个样本为真的概率，意思是判别器认为这个样本是来自p*的真实样本，而不是来自生成器的伪造样本的概率。所以判别器的目标是最大化样本在p*上为真的对数概率，并最大化所有来自p_theta的样本为假的对数概率（即log(1 - D(x))）。它试图使p_theta的样本看起来像假的，而p*的样本看起来像真的。它只是另一个神经网络，输入x并输出一个伯努利变量的概率。然后训练生成器来欺骗判别器。它试图生成图像x，使得判别器会给出高的“为真”概率。

这非常类似于我之前概述的逆RL过程。实际上，你可以将逆RL构建为一种GAN。你需要做出的一个选择是应该使用哪种判别器。在GAN中，我们可以证明最佳判别器（收敛时的贝叶斯最佳判别器）应该表示p*和p_theta之间的密度比。在实践中，我们通常没有最佳判别器，但如果我们要训练这些直到收敛，我们期望它收敛到一个网络，对于每个x，给出p*(x) / (p*(x) + p_theta(x))的概率。现在你可能会说，这看起来有点奇怪，判别器收敛时不应该只给出p*所有样本的概率1吗？不一定，因为如果p_theta生成的一些图像与p*可能生成的图像相同，而这些图像又与来自真实数据分布的图像相同，那么判别器就不能给它们1.0的概率，因为它们可能是假的，所以它必须根据这个比率生成概率。如果p_theta很差，这没问题，因为通常在这种情况下，真实的图像都有非常低的p_theta概率，而假图像的p*概率也很低。但是随着p_theta越来越好，判别器会产生除0和1之外的值。实际上，当p_theta(x)实际上等于p*(x)时，我们的收敛会预期判别器产生的概率始终为0.5。

好吧，这可能有点像学术练习，但我们实际上可以使用这个推论将逆RL转换为一种具有非常特殊判别器的GAN。因此，对于逆RL，最优策略p_theta会趋近于与p(τ)乘以exp(R_psi(τ))成正比的分布。所以我们要做的就是为我们的判别器选择这个形式，假设判别器等于(p(τ) * (1/Z) * exp(R)) / (p(τ) + p(τ) * (1/Z) * exp(R))。我们基本上直接使用了最优判别器的公式，用p(τ)乘以奖励的指数代替了p*，这很合理，因为这就是我们期望在收敛时得到的。如果我们像以前一样展开p(τ)的方程，那么包含所有初始状态和动态项的轨迹概率将会抵消，这样我们就得到了一个形式为 (1/Z * exp(R)) / (1 + 1/Z * exp(R)) 的判别器。这里要注意的是，只有当策略概率等于奖励的指数时（即策略收敛时），这个判别器才会等于0.5。

然后我们要做的是，针对奖励的参数psi来优化这个判别器的目标函数。所以我们会选择一个奖励，使得这个判别器对于人类样本最大，对于策略样本最小。所以训练psi的目标仍然与GAN完全相同，最大化数据分布p*下log(D_psi)的期望值，以及最大化当前策略样本下log(1 - D_psi)的期望值。只不过D_psi现在不仅仅是一个输出二元概率的神经网络，它内部还有这个特殊的形式，而我们要优化的是奖励R。事实证明，如果你针对与psi相同的目标来优化Z，这种方法实际上是有效的。所以你实际上不必计算分区函数Z，你可以将其作为一个与奖励函数参数一起优化的参数。它的推导过程稍微复杂一些，但你可以在底部的论文中找到它，论文名为《生成对抗网络、逆强化学习和基于能量的模型之间的联系》。

这个推导过程的有趣之处在于，我们实际上不再需要重要性权重，它们实际上被纳入了分区函数Z中。我们会优化Z，并将其与奖励函数参数一起进行优化。然后，策略也会像生成器一样进行优化，同样是为了最大化奖励。因此，我们有了生成器（策略），我们根据该策略生成样本。我们有数据（或演示），这些样本来自p*(τ)。我们根据标准GAN目标训练判别器，但判别器的形式与上一张幻灯片中提到的很像。我们优化了策略，以最大化预期奖励和熵。因此，策略会发生变化，使其更难与演示区分开来。

现在，如果我们一个实用算法中实例化这种想法，（在底部的另一篇论文《通过对抗学习进行稳健奖励学习》中也做过），我们可以做的一件事就是研究我们恢复的奖励是否能够以有意义的方式推广。例如，如果我们对一只看起来很愤怒的四足蚂蚁进行演示，然后我们从中恢复奖励函数，然后将该奖励函数应用于修改后的模型，其中也许两条腿是残疾的，它会发现，它仍然可以通过使用与专家演示非常不同的步态来最大化奖励函数。这是逆强化学习的好处之一，如果你真的恢复了专家的奖励函数，你可以在新条件下重新优化该奖励函数并获得有意义的行为，而仅仅复制动作不会导致有意义的行为。那么，我们可以从演示中学到什么以实现更好的迁移呢？我们需要做的是将目标奖励函数与动态分离，这正是逆强化学习所做的。

现在我们可以问的一个问题是，为了将GAN和逆强化学习联系起来，我们必须使用这种非常有趣的判别器。现在使用这种精细的判别器实际上对我们有利，因为它允许我们恢复奖励函数，然后可以推广到新情况。但是，如果我们不需要奖励函数，如果我们只想复制专家的策略，我们可以使用常规判别器吗？就像以前一样，我们有来自策略的样本和来自数据的样本，但现在D只是一个常规的二进制神经网络分类器，就像在常规GAN中一样。然后策略最大化log(D)的期望值，使其更难与演示区分开来。这个想法是在Ho和Ermon的一篇论文《生成对抗模仿学习》（Generative Adversarial Imitation Learning）中提出的。这种算法不再是逆强化学习算法，因为它不能恢复奖励函数，但它可以恢复专家的策略。所以它是一种定义明确的模仿学习方法。

因此存在许多权衡。为这种方法设置优化通常更简单，因为移动部件较少，但判别器的收敛实际上并不知道任何关于奖励的信息。所以在收敛时，判别器的值为0.5，你通常不能在新的设置下重新优化奖励，所以你不能保证你能恢复专家的奖励函数，但你可以保证，如果一切正常，你就能恢复他们的策略。

总结一下，我们可以将经典的深度IRL方法（例如在引导成本学习中）与对抗模仿学习进行对比。在IRL中，我们有策略样本和人类演示，奖励函数试图最小化策略样本的奖励，最大化人类演示的奖励，它学习一个轨迹的分布，使得演示具有最大似然性。生成对抗模仿学习方法则使用一个分类器，该分类器试图将所有策略样本标记为“假”，将所有人类演示标记为“真”，D(τ)基本上是τ是演示的概率，然后你使用log(D(τ))作为奖励。它们基本上是同一回事，只是对于其中一个，你可以恢复奖励函数，而对于另一个，你不能恢复奖励，但你可以恢复策略。所以区别在于，引导成本学习和其他IRL方法的判别器只是具有这种特殊形式，而对于生成对抗模仿学习方法，判别器只是一个二元分类器。

现在这些东西已经在许多不同的设置中使用，因此你可以将它与某种聚类方法相结合，并从异构演示中恢复多个不同的行为聚类。嗯，您甚至可以执行逆强化学习或从图像中模仿并复制模拟运动步态之类的东西。

如果您想了解有关逆强化学习的更多信息，这里有一些建议阅读材料。这些是逆强化学习中的一些经典论文：《通过逆强化学习进行学徒学习》（Apprenticeship Learning via Inverse Reinforcement Learning）和《最大熵逆强化学习》（Maximum Entropy Inverse Reinforcement Learning）。这里有一些更现代的论文：《引导成本学习》（Guided Cost Learning）是我讨论过的论文，它提出了一种将最大熵IRL实际扩展到高维深度学习设置的方法。下一篇论文《深度最大熵逆强化学习》（Deep Maximum Entropy Inverse Reinforcement Learning）在小表格域中执行逆强化学习，但使用深度网络。《生成对抗模仿学习》（Generative Adversarial Imitation Learning）不执行逆强化学习，但它确实恢复了策略。然后《通过对抗逆强化学习学习稳健奖励》（Learning Robust Rewards with Adversarial Inverse RL）实例化了GAN方法并研究了迁移问题。